<div class="code-editor-container" [ngClass]="customCssClass">
  <div class="editor-header" *ngIf="title || primaryButtonSelected.observed">
    <div class="title-action-row">
      <h3 class="editor-title" *ngIf="title">{{ title }}</h3>
      <span class="run-btn-wrapper" *ngIf="primaryButtonSelected.observed">
        <app-button
          variant="secondary"
          size="small"
          [disabled]="state.loading"
          (buttonClick)="onPrimaryButtonClick()"
          customClass="run-black-btn">
          Run
        </app-button>
      </span>
    </div>

    <div class="editor-actions" *ngIf="actionButtons.length">
      <ng-container *ngFor="let btn of actionButtons; let i = index">
        <span [ngClass]="btn.customClass">
          <app-button
            class="action-btn"
            [variant]="btn.style || 'secondary'"
            [icon]="btn.icon || ''"
            size="small"
            [disabled]="state.loading"
            (buttonClick)="onActionButtonClick(i)">
            {{ btn.label }}
          </app-button>
        </span>
      </ng-container>
    </div>
  </div>

  <div *ngIf="state.loading" class="loading-container">
    <div class="loading-spinner"></div>
    <p>Loading Code Editor...</p>
  </div>

  <div *ngIf="state.error" class="error-container">
    <div class="error-icon">⚠️</div>
    <h4>Failed to Load Editor</h4>
    <p>{{ state.errorMessage }}</p>
    <app-button
      variant="danger"
      size="small"
      (buttonClick)="initializeEditor()">
      Retry
    </app-button>
  </div>

  <div
    #editorContainer
    class="monaco-editor-container"
    [class.hidden]="state.loading || state.error">
  </div>

  <div *ngIf="state.processing" class="editor-loader-overlay">
    <div class="editor-loader-spinner"></div>
  </div>

  <div class="editor-footer" *ngIf="footerText">
    <p class="footer-note">
      <strong>Note:</strong> {{ footerText }}
    </p>
  </div>
</div>
