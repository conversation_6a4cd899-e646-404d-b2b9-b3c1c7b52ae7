import { CommonModule } from '@angular/common';
import {
  AfterViewChecked,
  Component,
  ElementRef,
  EventEmitter,
  HostListener,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import {
  SelectDropdownComponent,
  SelectOption,
} from '../select-dropdown/select-dropdown.component';
import { ChatMessage } from '../chat-window';
import { FormsModule } from '@angular/forms';
import { ButtonComponent } from '@ava/play-comp-library';

interface Tool {
  id: number;
  name: string;
}

@Component({
  selector: 'app-playground',
  standalone: true,
  imports: [CommonModule, SelectDropdownComponent,FormsModule, ButtonComponent],
  templateUrl: './playground.component.html',
  styleUrl: './playground.component.scss',
})
export class PlaygroundComponent implements OnInit, AfterViewChecked {
  isMenuOpen = false;
  isToolMenuOpen = false;
  @Output() promptChange = new EventEmitter<SelectOption>();
  @Input() promptOptions: SelectOption[] = [];
  selectedPrompt: string = 'default';
  // Chat data
  inputText: string = '';
  previousMessagesLength = 0;
  shouldScrollToBottom = false;
  @Input() messages: ChatMessage[] = [];
  @Input() isLoading: boolean = false;
  @Input() isDisabled: boolean = false;
  @Input() showLoader: boolean = true; // Control whether to show the animated loader
  @Output() messageSent = new EventEmitter<string>();
  @ViewChild('messagesContainer') messagesContainer!: ElementRef;

  ngOnInit(): void {
    this.messages = [
      {
        from: 'ai',
        text: 'Hi there, how can I help you today?',
      },
    ];
    this.shouldScrollToBottom = true;
  }

  ngAfterViewChecked() {
    if (this.shouldScrollToBottom) {
      this.scrollToBottom();
      this.shouldScrollToBottom = false;
    }
  }

  scrollToBottom(): void {
    try {
      if (this.messagesContainer && this.messagesContainer.nativeElement) {
        // Scroll to bottom to show latest messages
        this.messagesContainer.nativeElement.scrollTop = this.messagesContainer.nativeElement.scrollHeight;
      }
    } catch (err) {
      console.error('Error scrolling to bottom:', err);
    }
  }

   handleSendMessage(): void {
    if (!this.inputText.trim() || this.isDisabled) {
      return;
    }

    // Add user message to the chat
    this.messages = [
      ...this.messages,
      {
        from: 'user',
        text: this.inputText
      }
    ];
    this.shouldScrollToBottom = true;
    this.previousMessagesLength = this.messages.length;

    // Emit the message to parent component
    const messageText = this.inputText;
    this.inputText = '';
    this.messageSent.emit(messageText);
  }
  toggleMenu() {
    this.isMenuOpen = !this.isMenuOpen;
  }

  onPromptChange(value: string | string[]): void {
    const selectedValue = Array.isArray(value) ? value[0] : value;
    this.selectedPrompt = selectedValue;

    const selectedOption = this.promptOptions.find(
      (opt) => opt.value === selectedValue,
    );
    console.log('Selected prompt:', selectedOption);

    if (selectedOption) {
      this.promptChange.emit(selectedOption);
    }
  }

  save() {
    this.isMenuOpen = false;
    console.log('Save clicked');
    // your save logic here
  }

  export() {
    this.isMenuOpen = false;
    console.log('Export clicked');
    // your export logic here
  }

  // Hide menu when clicking outside
  @HostListener('document:click', ['$event'])
  onClickOutside(event: Event) {
    const target = event.target as HTMLElement;
    if (!target.closest('.btn-menu')) {
      this.isMenuOpen = false;
    }
  }

  @HostListener('keydown.enter', ['$event'])
  onEnterKeydown(event: KeyboardEvent): void {
    // Only prevent default and send if Shift key is not pressed
    if (!event.shiftKey) {
      event.preventDefault();
      this.handleSendMessage();
    }
  }

  // Track by function for ngFor performance
  trackByIndex(index: number, item: any): number {
    return index;
  }
}
