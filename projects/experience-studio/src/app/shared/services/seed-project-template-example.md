# Seed Project Template - Complete Example with Sample Data

## Example 1: React + Tailwind CSS Template

### 1. SSE Event Trigger
```json
{
  "id": "evt_1705312200_abc123",
  "event": "initial-code-gen", 
  "data": {
    "status": "COMPLETED",
    "progress": "SEED_PROJECT_INITIALIZED",
    "metadata": {
      "framework": "react",
      "design_library": "tailwindcss",
      "project_id": "proj_react_tailwind_001"
    },
    "log": null,
    "timestamp": "2024-01-15T10:30:00.000Z"
  }
}
```

### 2. API Request Generated
```http
GET /download/template?framework=react&design_library=tailwindcss HTTP/1.1
Host: api.example.com
Accept: application/octet-stream
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
User-Agent: Angular/17.0.0
```

### 3. API Response
```http
HTTP/1.1 200 OK
Content-Type: application/zip
Content-Length: 47832
Content-Disposition: attachment; filename="react-tailwindcss-template.zip"
Cache-Control: no-cache

[Binary ZIP file data - 47,832 bytes]
```

### 4. Enhanced ZIP Processing Logs

#### Initial Processing
```
📦 Starting ZIP file processing for seed project template
{
  "zipBlobSize": 47832,
  "zipBlobType": "application/zip", 
  "processingStartTime": "2024-01-15T10:30:02.145Z"
}

📁 ZIP archive loaded successfully
{
  "totalEntriesInZip": 18,
  "zipEntryPaths": [
    "react-tailwind-template/",
    "react-tailwind-template/src/",
    "react-tailwind-template/src/App.tsx",
    "react-tailwind-template/src/index.tsx",
    "react-tailwind-template/src/App.css",
    "react-tailwind-template/src/index.css",
    "react-tailwind-template/src/components/",
    "react-tailwind-template/src/components/Button.tsx",
    "react-tailwind-template/src/components/Card.tsx",
    "react-tailwind-template/public/",
    "react-tailwind-template/public/index.html",
    "react-tailwind-template/public/favicon.ico",
    "react-tailwind-template/public/manifest.json",
    "react-tailwind-template/package.json",
    "react-tailwind-template/tailwind.config.js",
    "react-tailwind-template/tsconfig.json",
    "react-tailwind-template/postcss.config.js",
    "react-tailwind-template/README.md"
  ]
}
```

#### File-by-File Processing
```
⏭️ Skipping file/directory
{
  "path": "react-tailwind-template/",
  "isDirectory": true,
  "reason": "Filtered out by shouldSkipFile logic"
}

⏭️ Skipping file/directory  
{
  "path": "react-tailwind-template/src/",
  "isDirectory": true,
  "reason": "Filtered out by shouldSkipFile logic"
}

📄 Processing ZIP file entry
{
  "relativePath": "react-tailwind-template/src/App.tsx",
  "fileName": "App.tsx",
  "fileExtension": "tsx",
  "contentLength": 1247,
  "contentPreview": "\"import React from 'react';\\nimport './App.css';\\nimport Button from './components/Button';\\nimport Card from './components/Card';\\n\\nfunction App() {\\n  return (\\n    <div className=\\\"min-h-screen bg-gray-100 py-8\\\">\\n      <div className=\\\"container mx-auto px-4\\\">\\n        <h1 className=\\\"text-4xl font-bold text-center text-blue-600 mb-8\\\">\\n          Welcome to React + Tailwind\\n        </h1>\\n        <div className=\\\"grid grid-cols-1 md:grid-cols-2 gap-6\\\">\\n          <Card title=\\\"Getting Started\\\" description=\\\"Start building your app with this template\\\" />\\n          <Card title=\\\"Documentation\\\" description=\\\"Learn more about React and Tailwind CSS\\\" />\\n        </div>\\n        <div className=\\\"text-center mt-8\\\">\\n          <Button onClick={() => alert('Hello!')}>Click Me</Button>\\n        </div>\\n      </div>\\n    </div>\\n  );\\n}\\n\\nexport default App;...\"",
  "zipEntrySize": 1247,
  "zipEntryCompressedSize": 456,
  "processingSteps": [
    "Extracted from ZIP archive",
    "Converted to string format",
    "Created FileModel object", 
    "Added to files array"
  ]
}

📄 Processing ZIP file entry
{
  "relativePath": "react-tailwind-template/src/components/Button.tsx",
  "fileName": "Button.tsx",
  "fileExtension": "tsx", 
  "contentLength": 567,
  "contentPreview": "\"import React from 'react';\\n\\ninterface ButtonProps {\\n  children: React.ReactNode;\\n  onClick?: () => void;\\n  variant?: 'primary' | 'secondary';\\n  disabled?: boolean;\\n}\\n\\nconst Button: React.FC<ButtonProps> = ({ \\n  children, \\n  onClick, \\n  variant = 'primary',\\n  disabled = false \\n}) => {\\n  const baseClasses = 'px-6 py-3 rounded-lg font-semibold transition-colors duration-200';\\n  const variantClasses = {\\n    primary: 'bg-blue-600 hover:bg-blue-700 text-white',\\n    secondary: 'bg-gray-200 hover:bg-gray-300 text-gray-800'\\n  };\\n  \\n  return (\\n    <button\\n      className={`${baseClasses} ${variantClasses[variant]} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}\\n      onClick={onClick}\\n      disabled={disabled}\\n    >\\n      {children}\\n    </button>\\n  );\\n};\\n\\nexport default Button;...\"",
  "zipEntrySize": 567,
  "zipEntryCompressedSize": 234,
  "processingSteps": [
    "Extracted from ZIP archive",
    "Converted to string format",
    "Created FileModel object",
    "Added to files array"
  ]
}

📄 Processing ZIP file entry
{
  "relativePath": "react-tailwind-template/package.json",
  "fileName": "package.json", 
  "fileExtension": "json",
  "contentLength": 892,
  "contentPreview": "\"{\\n  \\\"name\\\": \\\"react-tailwind-template\\\",\\n  \\\"version\\\": \\\"1.0.0\\\",\\n  \\\"private\\\": true,\\n  \\\"dependencies\\\": {\\n    \\\"@testing-library/jest-dom\\\": \\\"^5.16.4\\\",\\n    \\\"@testing-library/react\\\": \\\"^13.3.0\\\",\\n    \\\"@testing-library/user-event\\\": \\\"^13.5.0\\\",\\n    \\\"@types/jest\\\": \\\"^27.5.2\\\",\\n    \\\"@types/node\\\": \\\"^16.18.23\\\",\\n    \\\"@types/react\\\": \\\"^18.0.37\\\",\\n    \\\"@types/react-dom\\\": \\\"^18.0.11\\\",\\n    \\\"react\\\": \\\"^18.2.0\\\",\\n    \\\"react-dom\\\": \\\"^18.2.0\\\",\\n    \\\"react-scripts\\\": \\\"5.0.1\\\",\\n    \\\"tailwindcss\\\": \\\"^3.3.0\\\",\\n    \\\"typescript\\\": \\\"^4.9.5\\\",\\n    \\\"web-vitals\\\": \\\"^2.1.4\\\"\\n  },\\n  \\\"scripts\\\": {\\n    \\\"start\\\": \\\"react-scripts start\\\",\\n    \\\"build\\\": \\\"react-scripts build\\\",\\n    \\\"test\\\": \\\"react-scripts test\\\",\\n    \\\"eject\\\": \\\"react-scripts eject\\\"\\n  }...\"",
  "zipEntrySize": 892,
  "zipEntryCompressedSize": 345,
  "processingSteps": [
    "Extracted from ZIP archive",
    "Converted to string format", 
    "Created FileModel object",
    "Added to files array"
  ]
}
```

### 5. Final Processing Summary
```
✅ ZIP processing completed - Detailed file extraction summary
{
  "zipBlobSize": 47832,
  "totalZipEntries": 18,
  "filesProcessed": 11,
  "filesSkipped": 7,
  "finalFileCount": 11,
  "processingCompletedAt": "2024-01-15T10:30:03.892Z",
  "fileTypesSummary": {
    "tsx": 3,
    "css": 2,
    "html": 1,
    "json": 2,
    "js": 2,
    "md": 1
  },
  "extractionSummary": {
    "totalContentSize": 8934,
    "averageFileSize": 812,
    "largestFile": { "name": "react-tailwind-template/src/App.tsx", "size": 1247 },
    "smallestFile": { "name": "react-tailwind-template/postcss.config.js", "size": 89 }
  },
  "detailedFileList": [
    {
      "fullPath": "react-tailwind-template/src/App.tsx",
      "fileName": "App.tsx",
      "fileType": "file",
      "extension": "tsx",
      "contentLength": 1247,
      "contentPreview": "\"import React from 'react';\\nimport './App.css';\\nimport Button from './components/Button';\\nimport Card from './components/Card';\\n\\nfunction App() {\\n  return (\\n    <div className=\\\"min-h-screen bg-gray-100 py-8\\\">\\n      <div className=\\\"container mx-auto px-4\\\">\\n        <h1 className=\\\"text-4xl font-bold text-center text-blue-600 mb-8\\\">\\n          Welcome to React + Tailwind\\n        </h1>\\n        <div className=\\\"grid grid-cols-1 md:grid-cols-2 gap-6\\\">\\n          <Card title=\\\"Getting Started\\\" description=\\\"Start building your app with this template\\\" />\\n          <Card title=\\\"Documentation\\\" description=\\\"Learn more about React and Tailwind CSS\\\" />\\n        </div>\\n        <div className=\\\"text-center mt-8\\\">\\n          <Button onClick={() => alert('Hello!')}>Click Me</Button>\\n        </div>\\n      </div>\\n    </div>\\n  );\\n}\\n\\nexport default App;\""
    },
    {
      "fullPath": "react-tailwind-template/src/components/Button.tsx", 
      "fileName": "Button.tsx",
      "fileType": "file",
      "extension": "tsx",
      "contentLength": 567,
      "contentPreview": "\"import React from 'react';\\n\\ninterface ButtonProps {\\n  children: React.ReactNode;\\n  onClick?: () => void;\\n  variant?: 'primary' | 'secondary';\\n  disabled?: boolean;\\n}\\n\\nconst Button: React.FC<ButtonProps> = ({ \\n  children, \\n  onClick, \\n  variant = 'primary',\\n  disabled = false \\n}) => {\\n  const baseClasses = 'px-6 py-3 rounded-lg font-semibold transition-colors duration-200';\\n  const variantClasses = {\\n    primary: 'bg-blue-600 hover:bg-blue-700 text-white',\\n    secondary: 'bg-gray-200 hover:bg-gray-300 text-gray-800'\\n  };\\n  \\n  return (\\n    <button\\n      className={`${baseClasses} ${variantClasses[variant]} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}\\n      onClick={onClick}\\n      disabled={disabled}\\n    >\\n      {children}\\n    </button>\\n  );\\n};\\n\\nexport default Button;\""
    }
  ]
}
```

### 6. Final FileModel Objects Passed to Monaco Editor
```typescript
const finalFileModels: FileModel[] = [
  {
    name: "react-tailwind-template/src/App.tsx",
    type: "file",
    fileName: "App.tsx",
    content: `import React from 'react';
import './App.css';
import Button from './components/Button';
import Card from './components/Card';

function App() {
  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="container mx-auto px-4">
        <h1 className="text-4xl font-bold text-center text-blue-600 mb-8">
          Welcome to React + Tailwind
        </h1>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card title="Getting Started" description="Start building your app with this template" />
          <Card title="Documentation" description="Learn more about React and Tailwind CSS" />
        </div>
        <div className="text-center mt-8">
          <Button onClick={() => alert('Hello!')}>Click Me</Button>
        </div>
      </div>
    </div>
  );
}

export default App;`
  },
  {
    name: "react-tailwind-template/package.json",
    type: "file",
    fileName: "package.json",
    content: `{
  "name": "react-tailwind-template",
  "version": "1.0.0",
  "private": true,
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "tailwindcss": "^3.3.0",
    "typescript": "^4.9.5"
  },
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build"
  }
}`
  }
  // ... 9 more files
];
```

### 7. Code Window Component Processing Logs
```
🏗️ Template files received from SSE-triggered loading:
{
  "fileCount": 11,
  "fileNames": [
    "react-tailwind-template/src/App.tsx",
    "react-tailwind-template/src/components/Button.tsx",
    "react-tailwind-template/package.json",
    // ... more files
  ],
  "totalSize": 8934
}

🎯 Initializing Monaco Editor with seed project template files:
{
  "fileCount": 11,
  "files": [
    { "name": "react-tailwind-template/src/App.tsx", "size": 1247 },
    { "name": "react-tailwind-template/src/components/Button.tsx", "size": 567 },
    { "name": "react-tailwind-template/package.json", "size": 892 }
  ]
}

✅ Monaco Editor successfully initialized with seed project template files:
{
  "validFiles": 11,
  "fileNames": ["react-tailwind-template/src/App.tsx", "react-tailwind-template/src/components/Button.tsx", ...]
}
```

### 8. Final UI State and Monaco Editor Display
```typescript
// Component state after template loading completion
{
  isCodeGenerationComplete: false,
  isTemplateFilesAvailable: true,
  canShowCodeTabContent: true,
  isCodeTabEnabled: true,
  currentCodeTabState: {
    isEnabled: true,
    isLoading: false,
    hasError: false,
    tooltipMessage: "View seed project template"
  },
  currentView: "preview", // Changes to "editor" when code tab is clicked
  files: [/* 11 FileModel objects as shown above */]
}
```

### 9. Monaco Editor UI Display
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ File Explorer                    │ Monaco Editor                              │
├─────────────────────────────────┼────────────────────────────────────────────┤
│ 📁 react-tailwind-template/     │ 📑 App.tsx  📑 Button.tsx  📑 package.json │
│   📁 src/                       │ ┌──────────────────────────────────────────┐ │
│     📄 App.tsx            ←──── │ │  1  import React from 'react';          │ │
│     📄 index.tsx                │ │  2  import './App.css';                 │ │
│     📄 App.css                  │ │  3  import Button from './components/Button'; │
│     📄 index.css                │ │  4  import Card from './components/Card';│ │
│     📁 components/              │ │  5                                       │ │
│       📄 Button.tsx             │ │  6  function App() {                     │ │
│       📄 Card.tsx               │ │  7    return (                           │ │
│   📁 public/                    │ │  8      <div className="min-h-screen    │ │
│     📄 index.html               │ │           bg-gray-100 py-8">             │ │
│     📄 manifest.json            │ │  9        <div className="container      │ │
│   📄 package.json               │ │             mx-auto px-4">               │ │
│   📄 tailwind.config.js         │ │ 10          <h1 className="text-4xl     │ │
│   📄 README.md                  │ │               font-bold text-center      │ │
│                                 │ │               text-blue-600 mb-8">       │ │
│                                 │ │ 11            Welcome to React + Tailwind│ │
│                                 │ │ 12          </h1>                        │ │
│                                 │ │ 13          <div className="grid         │ │
│                                 │ │               grid-cols-1 md:grid-cols-2│ │
│                                 │ │               gap-6">                    │ │
│                                 │ │ 14            <Card title="Getting       │ │
│                                 │ │               Started" ... />            │ │
│                                 │ │ 15          </div>                       │ │
│                                 │ │ 16        </div>                         │ │
│                                 │ │ 17      </div>                           │ │
│                                 │ │ 18    );                                 │ │
│                                 │ │ 19  }                                    │ │
│                                 │ │ 20                                       │ │
│                                 │ │ 21  export default App;                  │ │
│                                 │ └──────────────────────────────────────────┘ │
└─────────────────────────────────┴────────────────────────────────────────────┘
```
