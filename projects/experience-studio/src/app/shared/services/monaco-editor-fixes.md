# Monaco Editor Initialization Fixes

## Issues Fixed

### 1. Angular Injection Context Error (NG0203)
**Problem**: `effect() can only be used within an injection context`
**Root Cause**: Using Angular `effect()` function outside of injection context in `subscribeToGenerationStateService()`
**Solution**: Replaced `effect()` with RxJS Observable subscription

#### Before (Broken):
```typescript
private subscribeToGenerationStateService(): void {
  effect(() => {
    const state = this.generationStateService.generationState();
    // ... handle state changes
  });
}
```

#### After (Fixed):
```typescript
private subscribeToGenerationStateService(): void {
  const stateChanges$ = new Observable(observer => {
    // Convert signal to observable with polling fallback
    let lastState = this.generationStateService.generationState();
    observer.next(lastState);
    
    const interval = setInterval(() => {
      const currentState = this.generationStateService.generationState();
      if (JSON.stringify(currentState) !== JSON.stringify(lastState)) {
        lastState = currentState;
        observer.next(currentState);
      }
    }, 100);
    
    return () => clearInterval(interval);
  });

  stateChanges$
    .pipe(takeUntilDestroyed(this.destroyRef))
    .subscribe(state => {
      // Handle state changes safely
    });
}
```

### 2. Code Viewer Not Available Error
**Problem**: `⚠️ Code viewer not available yet, will retry after view init`
**Root Cause**: ViewChild not initialized when template files are loaded
**Solution**: Improved timing and retry mechanism with NgZone optimization

#### Enhanced Retry Mechanism:
```typescript
private ensureMonacoEditorReadiness(retryCount = 0): void {
  const maxRetries = 10;
  const retryDelay = 200;

  if (!this.codeViewer) {
    if (retryCount < maxRetries) {
      this.ngZone.runOutsideAngular(() => {
        setTimeout(() => {
          this.ngZone.run(() => {
            this.ensureMonacoEditorReadiness(retryCount + 1);
          });
        }, retryDelay);
      });
    }
    return;
  }
  // Proceed with Monaco initialization
}
```

#### AfterViewInit Integration:
```typescript
ngAfterViewInit(): void {
  // Check if template files are already loaded and code viewer is now available
  if (this.files$.value.length > 0 && this.codeViewer) {
    this.ensureMonacoEditorReadiness();
    if (this.enableAutoTabSwitch && this.currentCodeTabState.isEnabled) {
      this.scheduleAutomaticCodeTabSwitch();
    }
  }
}
```

### 3. Empty File Content Warnings
**Problem**: `⚠️ File "src/index.css" has no content!` and `⚠️ File "tailwind.config.ts" has no content!`
**Root Cause**: Some template files in ZIP are empty
**Solution**: Added default content generation for common empty file types

#### Default Content Handler:
```typescript
private handleEmptyFileContent(content: string, relativePath: string, fileName: string): string {
  if (content && content.trim().length > 0) {
    return content;
  }

  const lowerFileName = fileName.toLowerCase();

  // CSS files
  if (lowerFileName.endsWith('.css')) {
    if (lowerFileName.includes('index') || lowerFileName.includes('main')) {
      return `/* Global styles */\n* {\n  box-sizing: border-box;\n}\n\nbody {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI';\n}\n`;
    }
    return `/* ${fileName} styles */\n`;
  }

  // Tailwind config files
  if (lowerFileName.includes('tailwind') && lowerFileName.endsWith('.ts')) {
    return `/** @type {import('tailwindcss').Config} */\nexport default {\n  content: ["./src/**/*.{js,jsx,ts,tsx}"],\n  theme: { extend: {} },\n  plugins: [],\n};\n`;
  }

  // Default for other empty files
  return `// ${fileName}\n// This file was empty in the template\n`;
}
```

## Implementation Details

### 1. Timing Improvements
- **NgZone Optimization**: Use `runOutsideAngular()` for timeouts to avoid unnecessary change detection
- **Extended Delays**: Increased delays for ViewChild initialization (200ms initial, up to 1000ms extended)
- **Retry Mechanism**: Up to 10 retries with 200ms intervals for code viewer availability

### 2. State Management Fixes
- **RxJS Instead of Signals**: Replaced Angular `effect()` with RxJS Observable subscription
- **Proper Cleanup**: Use `takeUntilDestroyed()` for automatic subscription cleanup
- **State Validation**: Re-validate conditions at execution time

### 3. File Content Enhancement
- **Default Content**: Provide meaningful default content for empty files
- **File Type Detection**: Smart detection based on file extensions and names
- **Logging**: Enhanced logging to track empty files and content generation

## Expected Results

### Before Fixes:
```
❌ ERROR RuntimeError: NG0203: effect() can only be used within an injection context
⚠️ Code viewer not available yet, will retry after view init
⚠️ File "src/index.css" has no content!
⚠️ File "tailwind.config.ts" has no content!
```

### After Fixes:
```
✅ Generation state changed: { isCodeTabEnabled: true }
✅ Code viewer is available, ensuring Monaco Editor readiness
✅ Template files successfully loaded into Monaco Editor
🚀 Performing automatic switch to code tab for seed project template
✅ Successfully auto-switched to code tab
```

## Testing Checklist

1. **No Injection Context Errors**
   - [ ] No NG0203 errors in console
   - [ ] Generation state subscription works correctly

2. **Code Viewer Availability**
   - [ ] Code viewer initializes without warnings
   - [ ] Monaco editor displays template files
   - [ ] Automatic tab switching works

3. **File Content Handling**
   - [ ] No empty file warnings
   - [ ] Default content provided for empty files
   - [ ] All files display properly in Monaco editor

4. **Automatic Tab Switching**
   - [ ] Automatically switches to code tab after template loading
   - [ ] Respects user interactions (5-second delay)
   - [ ] Can be disabled via configuration

## Configuration Options

```html
<!-- Default behavior -->
<app-code-window></app-code-window>

<!-- Disabled automatic switching -->
<app-code-window [enableAutoTabSwitch]="false"></app-code-window>

<!-- Custom delay -->
<app-code-window [autoTabSwitchDelay]="2000"></app-code-window>
```

## Performance Optimizations

1. **NgZone Usage**: Timeouts run outside Angular zone to prevent unnecessary change detection
2. **Retry Limits**: Maximum 10 retries to prevent infinite loops
3. **Efficient Polling**: 100ms intervals for signal state changes
4. **Cleanup**: Proper subscription cleanup with `takeUntilDestroyed()`

These fixes ensure that the Monaco editor initializes properly with seed project template files, automatically switches to the code tab, and handles edge cases gracefully while following Angular best practices.
