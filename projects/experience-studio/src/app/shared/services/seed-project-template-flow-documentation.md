# Seed Project Template Extraction - Complete Data Flow Documentation

## Overview
This document details the complete data flow from SSE event reception to Monaco editor display for seed project template extraction, including comprehensive logging and example data.

## 1. SSE Event Trigger

### Event Structure
```typescript
// Example SSE event that triggers seed project template loading
const sseEvent = {
  id: "event-123456",
  event: "initial-code-gen",
  data: JSON.stringify({
    status: "COMPLETED",
    progress: "SEED_PROJECT_INITIALIZED", 
    metadata: {
      framework: "react",
      design_library: "tailwindcss"
    },
    log: null,
    timestamp: "2024-01-15T10:30:00Z"
  })
};
```

### SSE Processing Chain
1. **SSE Service** receives event
2. **SSE Data Processor** processes event in `processSSEEvent()`
3. **Two key handlers are triggered:**
   - `handleSeedProjectTemplateLoading()` - Triggers template loading when `OVERVIEW + COMPLETED`
   - `handleCodeTabEnabling()` - Enables code tab when `SEED_PROJECT_INITIALIZED + COMPLETED`

## 2. Template Loading Trigger

### SSE Data Processor - handleSeedProjectTemplateLoading()
```typescript
// Location: sse-data-processor.service.ts:1587
private handleSeedProjectTemplateLoading(eventData: SSEEventData): void {
  if (eventData.progress === 'OVERVIEW' && eventData.status === 'COMPLETED') {
    const framework = this.generationStateService.getCurrentFramework(); // "react"
    const designLibrary = this.generationStateService.getCurrentDesignLibrary(); // "tailwindcss"
    
    // Triggers template loading service
    this.templateLoadingService.loadTemplate(framework, designLibrary).subscribe({...});
  }
}
```

### Code Tab Enabling
```typescript
// Location: sse-data-processor.service.ts:1714
private handleCodeTabEnabling(eventData: SSEEventData): void {
  if (eventData.progress === 'SEED_PROJECT_INITIALIZED' && eventData.status === 'COMPLETED') {
    this.generationStateService.updateCodeTabState(true);
    this.initializeMonacoEditorWithTemplateFiles(); // Re-emit existing template files
  }
}
```

## 3. API Call to Download Template

### Template Loading Service - loadTemplate()
```typescript
// Location: template-loading.service.ts:58
loadTemplate(framework: string, designLibrary: string): Observable<FileModel[]> {
  return this.downloadTemplateDirectly(framework, designLibrary).pipe(
    switchMap(zipBlob => this.processZipFile(zipBlob)),
    // ... processing pipeline
  );
}
```

### API Endpoint Call
```typescript
// Location: template-loading.service.ts:122
private downloadTemplateDirectly(framework: string, designLibrary: string): Observable<Blob> {
  const params = new HttpParams()
    .set('framework', framework)        // "react"
    .set('design_library', designLibrary); // "tailwindcss"
    
  const url = `${environment.apiUrl}/download/template`; // GET /download/template
  
  return this.http.get(url, {
    params,
    responseType: 'blob' // Returns ZIP file as Blob
  });
}
```

### Example API Request
```http
GET /download/template?framework=react&design_library=tailwindcss
Accept: application/octet-stream
```

### Example API Response
```
Content-Type: application/zip
Content-Length: 45678
Content-Disposition: attachment; filename="react-tailwindcss-template.zip"

[Binary ZIP file data containing seed project files]
```

## 4. ZIP File Processing with JSZip

### Enhanced processZipFile() Method
```typescript
// Location: template-loading.service.ts:151
private async processZipFile(zipBlob: Blob): Promise<FileModel[]> {
  // Initial logging
  this.logger.info('📦 Starting ZIP file processing', { 
    zipBlobSize: zipBlob.size,           // 45678
    zipBlobType: zipBlob.type,           // "application/zip"
    processingStartTime: "2024-01-15T10:30:05Z"
  });

  const zip = new JSZip();
  const zipContent = await zip.loadAsync(zipBlob);
  
  // ZIP structure logging
  this.logger.info('📁 ZIP archive loaded successfully', {
    totalEntriesInZip: 15,
    zipEntryPaths: [
      "src/",
      "src/App.tsx",
      "src/index.tsx", 
      "src/App.css",
      "public/",
      "public/index.html",
      "package.json",
      "tailwind.config.js",
      // ... more files
    ]
  });
}
```

### Example ZIP Archive Structure
```
react-tailwindcss-template.zip
├── src/
│   ├── App.tsx                 (2,456 bytes)
│   ├── index.tsx              (1,234 bytes)
│   ├── App.css                (3,567 bytes)
│   └── components/
│       └── Button.tsx         (1,890 bytes)
├── public/
│   ├── index.html             (2,345 bytes)
│   └── favicon.ico            (4,286 bytes)
├── package.json               (1,567 bytes)
├── tailwind.config.js         (892 bytes)
├── tsconfig.json              (1,234 bytes)
└── README.md                  (2,456 bytes)
```

### File Processing Loop with Enhanced Logging
```typescript
for (const [relativePath, zipEntry] of Object.entries(zipContent.files)) {
  // Skip filtering with logging
  if (this.shouldSkipFile(relativePath, zipEntry)) {
    skippedCount++;
    this.logger.debug('⏭️ Skipping file/directory', { 
      path: relativePath,           // "src/"
      isDirectory: zipEntry.dir,    // true
      reason: 'Filtered out by shouldSkipFile logic'
    });
    continue;
  }

  // File processing with comprehensive logging
  const content = await zipEntry.async('string');
  const fileName = relativePath.split('/').pop() || relativePath;
  
  this.logger.info('📄 Processing ZIP file entry', {
    relativePath: "src/App.tsx",
    fileName: "App.tsx", 
    fileExtension: "tsx",
    contentLength: 2456,
    contentPreview: "import React from 'react';\nimport './App.css';\n\nfunction App() {\n  return (\n    <div className=\"App\">...",
    zipEntrySize: 2456,
    zipEntryCompressedSize: 1234,
    processingSteps: [
      'Extracted from ZIP archive',
      'Converted to string format', 
      'Created FileModel object',
      'Added to files array'
    ]
  });
}
```

## 5. FileModel Creation and Final Processing

### FileModel Structure
```typescript
// Each processed file becomes a FileModel object
interface FileModel {
  name: string;      // Full path: "src/App.tsx"
  type: 'file';      // Always 'file' for template files
  content: string;   // Full file content
  fileName: string;  // Just filename: "App.tsx"
}
```

### Example FileModel Objects
```typescript
const exampleFileModels: FileModel[] = [
  {
    name: "src/App.tsx",
    type: "file",
    fileName: "App.tsx",
    content: `import React from 'react';
import './App.css';

function App() {
  return (
    <div className="App">
      <header className="App-header">
        <h1 className="text-4xl font-bold text-blue-600">
          Welcome to React + Tailwind
        </h1>
        <p className="text-lg text-gray-600 mt-4">
          This is your seed project template
        </p>
      </header>
    </div>
  );
}

export default App;`
  },
  {
    name: "package.json",
    type: "file",
    fileName: "package.json",
    content: `{
  "name": "react-tailwind-template",
  "version": "1.0.0",
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "tailwindcss": "^3.3.0"
  },
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build"
  }
}`
  }
];
```

### Final ZIP Processing Summary
```typescript
// Enhanced completion logging
this.logger.info('✅ ZIP processing completed - Detailed file extraction summary', {
  zipBlobSize: 45678,
  totalZipEntries: 15,
  filesProcessed: 8,
  filesSkipped: 7,  // Directories and filtered files
  finalFileCount: 8,
  processingCompletedAt: "2024-01-15T10:30:07Z",
  fileTypesSummary: {
    "tsx": 2,
    "css": 1,
    "html": 1,
    "json": 2,
    "js": 1,
    "md": 1
  },
  extractionSummary: {
    totalContentSize: 15678,
    averageFileSize: 1959,
    largestFile: { name: "src/App.tsx", size: 2456 },
    smallestFile: { name: "tailwind.config.js", size: 892 }
  }
});
```

## 6. Observable Emission to Code Window Component

### Template Loading Service - File Emission
```typescript
// Location: template-loading.service.ts:100
// Emit files to subscribers (code-window component)
this.templateFilesSubject.next(files);

// Debug emission verification
this.logger.debug('📡 Template files emitted to subscribers:', {
  subscriberCount: this.templateFilesSubject.observers.length, // 1
  emittedFiles: files.length // 8
});
```

### Code Window Component - File Reception
```typescript
// Location: code-window.component.ts:2008
private subscribeToTemplateLoadingService(): void {
  this.templateLoadingService.templateFiles$
    .pipe(takeUntil(this.destroy$))
    .subscribe({
      next: (templateFiles) => {
        if (templateFiles && templateFiles.length > 0) {
          console.log('🏗️ Template files received from SSE-triggered loading:', {
            fileCount: templateFiles.length,        // 8
            fileNames: templateFiles.map(f => f.name), // ["src/App.tsx", "package.json", ...]
            totalSize: templateFiles.reduce((sum, f) => sum + (f.content?.length || 0), 0) // 15678
          });

          // Process and initialize Monaco Editor
          this.initializeMonacoEditorWithSeedProjectFiles(validFileModels);
        }
      }
    });
}
```
