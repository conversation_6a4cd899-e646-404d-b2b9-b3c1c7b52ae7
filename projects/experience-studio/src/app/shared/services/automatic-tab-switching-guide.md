# Automatic Tab Switching for Seed Project Templates

## Overview
Implemented automatic tab switching functionality that navigates users to the code tab when seed project template files are loaded into the Monaco editor, following Angular best practices.

## Features Implemented

### 1. Automatic Tab Switching
**Trigger**: When seed project template files are successfully loaded into Monaco editor
**Action**: Automatically switches from current tab to code tab after a configurable delay
**UX**: Smooth transition with proper timing to avoid jarring user experience

### 2. User Interaction Awareness
**Problem**: Prevent automatic switching when user is actively using other tabs
**Solution**: Track user tab interactions and delay automatic switching for 5 seconds after user interaction
**Benefit**: Respects user intent and prevents interrupting their workflow

### 3. Configurable Behavior
**Input Properties**:
- `enableAutoTabSwitch` (default: true) - Enable/disable automatic tab switching
- `autoTabSwitchDelay` (default: 1000ms) - Configurable delay before switching

### 4. Comprehensive Validation
**Conditions Checked**:
- Not already on code tab
- Code tab is enabled and has no errors
- Template files are loaded
- No recent user tab interactions
- No preview errors
- Automatic switching is enabled

## Implementation Details

### Core Methods

#### 1. scheduleAutomaticCodeTabSwitch()
```typescript
private scheduleAutomaticCodeTabSwitch(): void {
  // Check configuration and current state
  if (!this.enableAutoTabSwitch || this.currentView$.value === 'editor') {
    return;
  }
  
  // Schedule switch with configurable delay using NgZone for performance
  this.ngZone.runOutsideAngular(() => {
    setTimeout(() => {
      this.ngZone.run(() => {
        this.performAutomaticCodeTabSwitch();
      });
    }, this.autoTabSwitchDelay);
  });
}
```

#### 2. performAutomaticCodeTabSwitch()
```typescript
private performAutomaticCodeTabSwitch(): void {
  // Re-validate conditions at execution time
  const canAutoSwitch = this.validateAutoSwitchConditions();
  
  if (canAutoSwitch.isValid) {
    // Use existing navigation logic
    this.toggleCodeViewEnhanced();
  }
}
```

#### 3. validateAutoSwitchConditions()
```typescript
private validateAutoSwitchConditions(): { isValid: boolean; reason: string } {
  // Check user interaction timing
  const timeSinceLastInteraction = Date.now() - this.lastUserTabInteraction;
  if (timeSinceLastInteraction < this.AUTO_SWITCH_DELAY_AFTER_USER_INTERACTION) {
    return { isValid: false, reason: 'User recently interacted with tabs' };
  }
  
  // Check all other conditions...
  return { isValid: true, reason: 'All conditions satisfied' };
}
```

#### 4. trackUserTabInteraction()
```typescript
private trackUserTabInteraction(): void {
  this.lastUserTabInteraction = Date.now();
  // Called in onTabClick() to track user interactions
}
```

### Integration Points

#### 1. Template Loading Service Subscription
```typescript
// In subscribeToTemplateLoadingService()
if (this.codeViewer) {
  this.ensureMonacoEditorReadiness();
  this.scheduleAutomaticCodeTabSwitch(); // ← Automatic switching triggered here
}
```

#### 2. User Interaction Tracking
```typescript
// In onTabClick()
onTabClick(tab: string): void {
  this.trackUserTabInteraction(); // ← Track user interactions
  // ... rest of tab click logic
}
```

## Angular Best Practices Used

### 1. NgZone Optimization
- Use `runOutsideAngular()` for setTimeout to avoid unnecessary change detection
- Use `run()` for actual UI updates to ensure proper change detection

### 2. Input Properties for Configuration
- `@Input() enableAutoTabSwitch = true`
- `@Input() autoTabSwitchDelay = 1000`
- Allows parent components to customize behavior

### 3. Comprehensive State Validation
- Check multiple conditions before performing automatic actions
- Re-validate conditions at execution time (state might change during delay)

### 4. User Experience Considerations
- Respect user interactions (don't interrupt active usage)
- Configurable delays for smooth transitions
- Comprehensive logging for debugging

### 5. Error Handling
- Try-catch blocks around automatic switching logic
- Graceful fallbacks when conditions aren't met
- Detailed logging for troubleshooting

## Usage Examples

### 1. Default Behavior (Automatic Switching Enabled)
```html
<app-code-window></app-code-window>
```
- Automatically switches to code tab 1 second after seed project files load
- Respects user interactions (waits 5 seconds after user clicks tabs)

### 2. Custom Delay
```html
<app-code-window [autoTabSwitchDelay]="2000"></app-code-window>
```
- Switches to code tab 2 seconds after seed project files load

### 3. Disabled Automatic Switching
```html
<app-code-window [enableAutoTabSwitch]="false"></app-code-window>
```
- No automatic switching, user must manually click code tab

### 4. Custom Configuration
```html
<app-code-window 
  [enableAutoTabSwitch]="true" 
  [autoTabSwitchDelay]="1500">
</app-code-window>
```
- Enabled with 1.5 second delay

## Workflow

### Complete Flow
1. **SSE Event**: `SEED_PROJECT_INITIALIZED + COMPLETED` received
2. **Template Loading**: Files loaded from API and processed
3. **Monaco Initialization**: Files loaded into Monaco editor
4. **Automatic Switching**: `scheduleAutomaticCodeTabSwitch()` called
5. **Validation**: Check all conditions including user interactions
6. **Execution**: Switch to code tab using existing navigation logic
7. **Result**: User sees Monaco editor with seed project template files

### Timing
- **Template Loading**: Immediate after SSE event
- **Monaco Initialization**: Immediate after template loading
- **Switch Scheduling**: Immediate after Monaco initialization
- **Switch Execution**: After configurable delay (default 1000ms)
- **User Interaction Delay**: 5 seconds after user clicks any tab

## Debugging

### Log Messages to Look For
```
🔄 Scheduling automatic switch to code tab for seed project template viewing
🚀 Performing automatic switch to code tab for seed project template
✅ Successfully auto-switched to code tab
⏭️ Auto-switch conditions no longer met, skipping
👆 User tab interaction tracked
```

### Common Issues
1. **Not switching**: Check `enableAutoTabSwitch` and validation conditions
2. **Switching too early**: User recently interacted with tabs
3. **Switching to wrong tab**: Validation logic prevents this
4. **Performance issues**: NgZone optimization handles this

## Benefits

### 1. Improved User Experience
- Users immediately see the seed project template files
- No need to manually navigate to code tab
- Smooth, non-jarring transitions

### 2. Configurable Behavior
- Can be disabled if not desired
- Customizable timing for different use cases
- Respects user preferences

### 3. Robust Implementation
- Comprehensive validation prevents edge cases
- User interaction awareness prevents interruptions
- Angular best practices ensure performance

### 4. Maintainable Code
- Clear separation of concerns
- Comprehensive logging for debugging
- Follows existing code patterns
