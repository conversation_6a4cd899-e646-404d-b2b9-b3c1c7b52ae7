# Seed Project Template Debug Guide

## Overview
This guide helps debug the seed project template workflow from SSE event to Monaco editor display.

## Key Changes Made

### 1. Added Generation State Service Subscription
**Problem**: SSE data processor was updating generation state, but code-window component wasn't reacting to it.
**Solution**: Added `subscribeToGenerationStateService()` method to react to SSE-triggered state changes.

```typescript
// Location: code-window.component.ts
private subscribeToGenerationStateService(): void {
  this.generationStateService.generationState
    .pipe(takeUntilDestroyed(this.destroyRef))
    .subscribe(state => {
      if (state.isCodeTabEnabled) {
        this.setCodeTabEnabled('View seed project template');
        this.isCodeTabEnabled = true;
        this.ensureMonacoEditorReadiness();
        this.cdr.detectChanges();
      }
    });
}
```

### 2. Enhanced Debugging Logs
Added comprehensive logging at key points:
- Generation state changes
- Files loaded into files$ observable
- Tab navigation attempts
- Monaco editor readiness checks

## Debug Workflow

### Step 1: Verify SSE Event Processing
Look for these logs when `SEED_PROJECT_INITIALIZED + COMPLETED` is received:

```
📝 Detected SEED_PROJECT_INITIALIZED + COMPLETED - enabling code tab
🔄 Ensuring template files are loaded for code tab
✅ Template files ensured for code tab
✅ Code tab successfully enabled for seed project viewing
```

### Step 2: Verify Generation State Updates
Look for this log in code-window component:

```
🔄 Generation state changed: {
  isCodeTabEnabled: true,
  isTemplateLoading: false,
  framework: "react",
  designLibrary: "tailwindcss"
}
✅ Code tab enabled via generation state service
```

### Step 3: Verify Template Files Loading
Look for these logs in template loading service:

```
📤 ===== EMITTING FILES TO CODE-VIEWER COMPONENT =====
📊 Total files being emitted: 8
[Complete file content logs for each file]
```

### Step 4: Verify Files Reception in Code Window
Look for these logs in code-window component:

```
📥 ===== RECEIVED TEMPLATE FILES WITH COMPLETE CONTENT =====
📊 Total template files received: 8
[Complete file content logs for each file]
```

### Step 5: Verify Files Loading into Observable
Look for this log:

```
📊 Files loaded into files$ observable: {
  filesInObservable: 8,
  fileNames: ["src/App.tsx", "package.json", ...],
  filesHaveContent: true
}
```

### Step 6: Verify Code Tab State
Check the HTML template debug info:

```
Code generation complete: false, Code tab enabled: true, Files: 8
```

### Step 7: Verify Tab Navigation
When clicking the code tab, look for:

```
🔄 Navigating to code tab: {
  isCodeGenerationComplete: false,
  codeTabEnabled: true,
  hasPreviewError: false,
  codeTabHasError: false,
  filesCount: 8,
  filesNames: ["src/App.tsx", "package.json", ...],
  reason: "Seed project template available"
}
```

### Step 8: Verify Monaco Editor Display
Look for:

```
✅ Showing code view: {
  isCodeGenerationComplete: false,
  codeTabEnabled: true,
  hasPreviewError: false,
  reason: "Seed project template available"
}
```

## Common Issues and Solutions

### Issue 1: Code Tab Not Enabled
**Symptoms**: Code tab remains disabled after SSE event
**Debug**: Check if generation state service subscription is working
**Solution**: Verify `subscribeToGenerationStateService()` is called in ngOnInit

### Issue 2: Files Not Loading
**Symptoms**: Code tab enabled but no files in Monaco editor
**Debug**: Check files$ observable content
**Solution**: Verify template loading service is emitting files correctly

### Issue 3: Tab Navigation Fails
**Symptoms**: Code tab enabled but clicking doesn't navigate
**Debug**: Check `canNavigateToCode` logic in onTabClick
**Solution**: Verify both `codeTabState.isEnabled` and no errors

### Issue 4: Monaco Editor Not Displaying
**Symptoms**: Navigation works but Monaco editor not visible
**Debug**: Check HTML template conditions
**Solution**: Verify `currentCodeTabState.isEnabled` is true

## Testing Checklist

1. **SSE Event Reception**
   - [ ] `SEED_PROJECT_INITIALIZED + COMPLETED` event received
   - [ ] SSE data processor logs show code tab enabling
   - [ ] Generation state service updated

2. **State Synchronization**
   - [ ] Code-window component receives state change
   - [ ] Code tab state updated to enabled
   - [ ] Change detection triggered

3. **Template Loading**
   - [ ] Template files loaded from API
   - [ ] Files emitted to code-window component
   - [ ] Files loaded into files$ observable

4. **UI State**
   - [ ] Code tab appears enabled in UI
   - [ ] HTML template shows correct debug info
   - [ ] Monaco editor display condition met

5. **Navigation**
   - [ ] Code tab click triggers navigation
   - [ ] Tab navigation logic passes
   - [ ] Monaco editor becomes visible

6. **Monaco Editor**
   - [ ] Code-viewer component receives files
   - [ ] File tree structure displayed
   - [ ] Files selectable and viewable

## Expected Final State

After successful seed project template loading:

```typescript
{
  // Generation State
  isCodeTabEnabled: true,
  isTemplateLoading: false,
  
  // Code Window State
  currentCodeTabState: {
    isEnabled: true,
    isLoading: false,
    hasError: false,
    tooltipMessage: "View seed project template"
  },
  
  // Files State
  files$: [
    { name: "src/App.tsx", content: "...", type: "file" },
    { name: "package.json", content: "...", type: "file" },
    // ... more files
  ],
  
  // UI State
  currentView: "editor", // After clicking code tab
  isCodeActive: true,
  isPreviewActive: false
}
```

## Manual Testing Steps

1. **Trigger SSE Event**: Send `SEED_PROJECT_INITIALIZED + COMPLETED` event
2. **Check Console**: Verify all debug logs appear in correct order
3. **Check UI**: Verify code tab becomes enabled and clickable
4. **Click Code Tab**: Verify navigation to Monaco editor
5. **Check Files**: Verify file tree and file content display
6. **Test Navigation**: Verify files can be opened and viewed

This comprehensive debugging approach should help identify exactly where the seed project template workflow is failing.
