import { Injectable } from '@angular/core';
import { createLogger } from '../../../utils/logger';
import { UIDesignCanvasService } from './ui-design-canvas.service';
import { UIDesignNodeService } from './ui-design-node.service';

export interface MultiPageLayoutConfig {
  spacing: {
    horizontal: number;
    vertical: number;
  };
  alignment: 'grid' | 'flow' | 'custom';
  columns: number;
  padding: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
}

export interface PagePosition {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  row: number;
  column: number;
}

@Injectable({
  providedIn: 'root'
})
export class UIDesignMultiPageAlignmentService {
  private readonly logger = createLogger('UIDesignMultiPageAlignment');

  private readonly referenceViewport = {
    x: 39.86,
    y: 82.95,
    zoom: 0.5
  };

  private readonly defaultLayoutConfig: MultiPageLayoutConfig = {
    spacing: {
      horizontal: 50,
      vertical: 60
    },
    alignment: 'grid',
    columns: 3,
    padding: {
      top: 40,
      right: 40,
      bottom: 40,
      left: 40
    }
  };

  constructor(
    private canvasService: UIDesignCanvasService,
    private nodeService: UIDesignNodeService
  ) {
  }

  alignMultiplePages(): void {
    const nodes = this.nodeService.getNodes();

    if (nodes.length === 0) {
      return;
    }

    const pagePositions = this.calculatePagePositions(nodes);

    pagePositions.forEach(position => {
      this.nodeService.updateNode(position.id, {
        position: { x: position.x, y: position.y }
      });
    });

    this.canvasService.updateViewport(this.referenceViewport);

  }

  private calculatePagePositions(nodes: any[]): PagePosition[] {
    const config = this.defaultLayoutConfig;
    const positions: PagePosition[] = [];

    const pageWidth = 300;
    const pageHeight = 400;

    nodes.forEach((node, index) => {
      const row = Math.floor(index / config.columns);
      const column = index % config.columns;

      const x = config.padding.left + column * (pageWidth + config.spacing.horizontal);
      const y = config.padding.top + row * (pageHeight + config.spacing.vertical);

      positions.push({
        id: node.id,
        x,
        y,
        width: pageWidth,
        height: pageHeight,
        row,
        column
      });
    });

    return positions;
  }

  getOptimalViewportForPages(): { x: number; y: number; zoom: number } {
    const nodes = this.nodeService.getNodes();

    if (nodes.length === 0) {
      return this.referenceViewport;
    }

    if (nodes.length > 1) {
      return this.referenceViewport;
    }

    const contentBounds = this.nodeService.getContentBounds();
    const containerSize = { width: 800, height: 600 };

    const centerX = (containerSize.width - contentBounds.width * this.referenceViewport.zoom) / 2;
    const centerY = (containerSize.height - contentBounds.height * this.referenceViewport.zoom) / 2;

    return {
      x: centerX,
      y: centerY,
      zoom: this.referenceViewport.zoom
    };
  }

  autoArrangePages(): void {
    const nodes = this.nodeService.getNodes();

    if (nodes.length <= 1) {

      this.centerSinglePage();
    } else {

      this.alignMultiplePages();
    }
  }

  private centerSinglePage(): void {
    const nodes = this.nodeService.getNodes();

    if (nodes.length !== 1) {
      return;
    }

    const node = nodes[0];
    const centerX = 200;
    const centerY = 150;

    this.nodeService.updateNode(node.id, {
      position: { x: centerX, y: centerY }
    });

    this.canvasService.updateViewport(this.referenceViewport);

  }

  getLayoutConfig(): MultiPageLayoutConfig {
    return { ...this.defaultLayoutConfig };
  }

  updateLayoutConfig(updates: Partial<MultiPageLayoutConfig>): void {
    Object.assign(this.defaultLayoutConfig, updates);
  }

  calculateTotalContentBounds(): { width: number; height: number; minX: number; minY: number; maxX: number; maxY: number } {
    const nodes = this.nodeService.getNodes();

    if (nodes.length === 0) {
      return { width: 0, height: 0, minX: 0, minY: 0, maxX: 0, maxY: 0 };
    }

    const positions = this.calculatePagePositions(nodes);

    const minX = Math.min(...positions.map(p => p.x));
    const minY = Math.min(...positions.map(p => p.y));
    const maxX = Math.max(...positions.map(p => p.x + p.width));
    const maxY = Math.max(...positions.map(p => p.y + p.height));

    return {
      width: maxX - minX,
      height: maxY - minY,
      minX,
      minY,
      maxX,
      maxY
    };
  }

  getReferenceViewport(): { x: number; y: number; zoom: number } {
    return { ...this.referenceViewport };
  }

  applyReferenceViewport(): void {
    this.canvasService.updateViewport(this.referenceViewport);
  }

  isAtReferenceViewport(): boolean {
    const current = this.canvasService.getViewport();
    const tolerance = 1;

    return Math.abs(current.x - this.referenceViewport.x) < tolerance &&
           Math.abs(current.y - this.referenceViewport.y) < tolerance &&
           Math.abs(current.zoom - this.referenceViewport.zoom) < 0.01;
  }

  getAlignmentStats(): {
    totalPages: number;
    layoutType: string;
    isAtReference: boolean;
    contentBounds: any;
    viewport: any;
  } {
    const nodes = this.nodeService.getNodes();
    const contentBounds = this.calculateTotalContentBounds();
    const viewport = this.canvasService.getViewport();

    return {
      totalPages: nodes.length,
      layoutType: nodes.length > 1 ? 'multi-page-grid' : 'single-page-centered',
      isAtReference: this.isAtReferenceViewport(),
      contentBounds,
      viewport
    };
  }
}
