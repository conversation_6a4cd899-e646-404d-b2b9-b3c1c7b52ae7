import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { createLogger } from '../../../utils/logger';

export interface CanvasViewport {
  zoom: number;
  x: number;
  y: number;
  isDragging: boolean;
  lastMouseX: number;
  lastMouseY: number;
}

export interface CanvasPoint {
  x: number;
  y: number;
}

@Injectable({
  providedIn: 'root'
})
export class UIDesignCanvasService {
  private readonly logger = createLogger('UIDesignCanvasService');

  private readonly viewport$ = new BehaviorSubject<CanvasViewport>({
    zoom: 0.5,
    x: 39.86,
    y: 82.95,
    isDragging: false,
    lastMouseX: 0,
    lastMouseY: 0
  });

  private readonly minZoom = 0.1;
  private readonly maxZoom = 3.0;
  private readonly zoomStep = 0.1;

  constructor() {
  }

  get viewport(): Observable<CanvasViewport> {
    return this.viewport$.asObservable();
  }

  getViewport(): CanvasViewport {
    return this.viewport$.value;
  }

  updateViewport(updates: Partial<CanvasViewport>): void {
    const current = this.viewport$.value;
    const updated = { ...current, ...updates };
    this.viewport$.next(updated);
  }

  startDragging(mouseX: number, mouseY: number): void {
    this.updateViewport({
      isDragging: true,
      lastMouseX: mouseX,
      lastMouseY: mouseY
    });
  }

  stopDragging(): void {
    this.updateViewport({
      isDragging: false
    });
  }

  panCanvas(deltaX: number, deltaY: number): void {
    const current = this.viewport$.value;
    this.updateViewport({
      x: current.x + deltaX,
      y: current.y + deltaY
    });
  }

  zoomToLevel(newZoom: number, centerPoint?: CanvasPoint): void {
    const clampedZoom = Math.max(this.minZoom, Math.min(this.maxZoom, newZoom));
    const current = this.viewport$.value;

    if (centerPoint) {

      const zoomRatio = clampedZoom / current.zoom;
      const newX = centerPoint.x - (centerPoint.x - current.x) * zoomRatio;
      const newY = centerPoint.y - (centerPoint.y - current.y) * zoomRatio;

      this.updateViewport({
        zoom: clampedZoom,
        x: newX,
        y: newY
      });
    } else {

      this.updateViewport({
        zoom: clampedZoom
      });
    }
  }

  zoomIn(centerPoint?: CanvasPoint): void {
    const current = this.viewport$.value;
    const newZoom = Math.min(this.maxZoom, current.zoom + this.zoomStep);
    this.zoomToLevel(newZoom, centerPoint);
  }

  zoomOut(centerPoint?: CanvasPoint): void {
    const current = this.viewport$.value;
    const newZoom = Math.max(this.minZoom, current.zoom - this.zoomStep);
    this.zoomToLevel(newZoom, centerPoint);
  }

  resetViewport(): void {
    this.viewport$.next({
      zoom: 0.5,
      x: 39.86,
      y: 82.95,
      isDragging: false,
      lastMouseX: 0,
      lastMouseY: 0
    });
  }

  getTransformStyle(): string {
    const viewport = this.viewport$.value;
    return `translate(${viewport.x}px, ${viewport.y}px) scale(${viewport.zoom})`;
  }

  getZoomPercentage(): number {
    return Math.round(this.viewport$.value.zoom * 100);
  }

  screenToCanvas(screenPoint: CanvasPoint, containerRect: DOMRect): CanvasPoint {
    const viewport = this.viewport$.value;
    return {
      x: (screenPoint.x - containerRect.left - viewport.x) / viewport.zoom,
      y: (screenPoint.y - containerRect.top - viewport.y) / viewport.zoom
    };
  }

  canvasToScreen(canvasPoint: CanvasPoint, containerRect: DOMRect): CanvasPoint {
    const viewport = this.viewport$.value;
    return {
      x: canvasPoint.x * viewport.zoom + viewport.x + containerRect.left,
      y: canvasPoint.y * viewport.zoom + viewport.y + containerRect.top
    };
  }

  isAtMinZoom(): boolean {
    return this.viewport$.value.zoom <= this.minZoom;
  }

  isAtMaxZoom(): boolean {
    return this.viewport$.value.zoom >= this.maxZoom;
  }

  fitContentToView(contentBounds: { width: number; height: number }, containerSize: { width: number; height: number }): void {
    const padding = 50;
    const availableWidth = containerSize.width - padding * 2;
    const availableHeight = containerSize.height - padding * 2;

    const scaleX = availableWidth / contentBounds.width;
    const scaleY = availableHeight / contentBounds.height;
    const scale = Math.min(scaleX, scaleY, 1);

    const centerX = (containerSize.width - contentBounds.width * scale) / 2;
    const centerY = (containerSize.height - contentBounds.height * scale) / 2;

    this.updateViewport({
      zoom: scale,
      x: centerX,
      y: centerY
    });
  }

  clear(): void {
    this.resetViewport();
  }
}
