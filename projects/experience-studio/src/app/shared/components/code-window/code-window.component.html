<awe-splitscreen
  class="container smooth-split-screen"
  [isResizable]="true"
  [minWidth]="(minWidth | async) || '300'"
  defaultLeftPanelWidth="35%"
  defaultRightPanelWidth="65%">
  <awe-leftpanel [hasHeader]="true" awe-leftpanel>
    <div awe-leftpanel-header>
      <!-- Custom left panel header instead of awe-header -->
      <div class="custom-header" [ngClass]="(currentTheme | async) + '-theme'">
        <div class="header-left">
          <awe-icons
            iconName="awe_home"
            iconColor="neutralIcon"
            class="cursor-pointer"
            (click)="navigateToHome()"
            title="Navigate to home page"></awe-icons>
          <awe-icons
            (click)="toggleLeftPanel()"
            iconName="awe_dock_to_right"
            iconColor="neutralIcon"></awe-icons>
        </div>
        <div class="header-center">
          <div
            class="project-name"
            [class.shimmer]="isProjectNameLoading"
            [class.hidden]="shouldHideProjectName | async"
            *ngIf="!(shouldHideProjectName | async)">
            {{ projectName }}
          </div>
        </div>
        <div class="header-right">
          <!-- Hide History tab in UI Design mode -->
        </div>
      </div>
    </div>
    <div awe-leftpanel-content class="adjust-height">
      <div *ngIf="!(isHistoryActive | async)" class="adjust-height">
        <!--Injected chat-window on left content-->
        <app-chat-window
          #chatWindow
          [theme]="(currentTheme | async) || 'light'"
          [defaultText]="getPromptBarPlaceholder()"
          [rightIcons]="rightIcons"
          [(textValue)]="lightPrompt"
          [chatMessages]="lightMessages"
          [showStepper]="(isUIDesignMode | async) ? false : shouldShowStepper()"
          [progress]="currentProgressState"
          [progressDescription]="lastProgressDescription"
          [status]="pollingStatus"
          [selectedImageDataUri]="selectedImageDataUri"
          [isCodeGenerationComplete]="getPromptBarEnabledState()"
          [isUIDesignLoading]="shouldShowUIDesignLoadingIndicator()"
          [isCodeGenerationLoading]="shouldShowCodeGenerationLoadingIndicator()"
          [codeRegenerationProgressDescription]="codeRegenerationProgressDescription"
          [projectId]="projectId || ''"
          [jobId]="jobId || ''"
          [useApi]="true"
          (iconClicked)="handleIconClick($event)"
          (enterPressed)="handleUIDesignPromptSubmission()"
          (regenerationPayload)="handleRegenerationPayload($event)"
          (userMessageData)="handleUserMessageData($event)"
          (retryStep)="onRetryClick()">
        </app-chat-window>
      </div>
      <div class="border history-container" *ngIf="isHistoryActive | async">
        <div class="history-content">
          <div class="history-header">
            <awe-icons iconName="awe_arrow_back_left" (click)="toggleHistoryView()"></awe-icons>
            <h4>Project History</h4>
          </div>

          <div class="history-cards-container"></div>
        </div>
      </div>
    </div>
  </awe-leftpanel>

  <awe-rightpanel [hasHeader]="true" awe-rightpanel>
    <div awe-rightpanel-header>
      <!-- Custom right panel header with tabs and organized icons -->
      <div class="custom-header" [ngClass]="(currentTheme | async) + '-theme'">
        <div class="header-left">
          <!-- Home icon that only shows when left panel is collapsed -->
          <awe-icons
            *ngIf="isLeftPanelCollapsed | async"
            iconName="awe_home"
            iconColor="neutralIcon"
            class="cursor-pointer"
            (click)="navigateToHome()"
            title="Navigate to home page"></awe-icons>
          <awe-icons
            class="dockToRight"
            *ngIf="isLeftPanelCollapsed | async"
            (click)="toggleLeftPanel()"
            iconName="awe_dock_to_right"
            iconColor="neutralIcon"
            title="Toggle left panel"></awe-icons>

          <!-- Tabs instead of toggle switch -->
          <div class="tabs-container">
            <!-- UI Design Mode - Overview and Preview Tabs -->
            <ng-container *ngIf="isUIDesignMode | async">
              <!-- Overview tab - shown when UI design response is received -->
              <div
                *ngIf="showUIDesignOverviewTab | async"
                class="custom-button"
                [class.active]="(currentView | async) === 'overview'"
                (click)="onTabClick('overview')"
                [title]="'View UI Design in Mobile Frame'">
                <span>Overview</span>
              </div>
              <!-- Preview tab - canvas view -->
              <div
                class="custom-button"
                [class.active]="(currentView | async) === 'preview' || !(showUIDesignOverviewTab | async)"
                (click)="onTabClick('preview')"
                [title]="'View UI Design Canvas'">
                <span>Preview</span>
              </div>
            </ng-container>

            <!-- Standard Mode - All Tabs -->
            <ng-container *ngIf="!(isUIDesignMode | async)">
              <!-- Enhanced Preview tab - Always visible with proper state management -->
              <div
                *ngIf="isNewPreviewTabEnabled"
                class="custom-button preview-tab"
                [class.active]="(currentView | async) === 'preview' && (currentPreviewTabState.isEnabled || currentPreviewTabState.hasError)"
                [class.disabled]="!currentPreviewTabState.isEnabled && !currentPreviewTabState.hasError"
                [class.loading]="currentPreviewTabState.isLoading"
                [class.error-tab]="currentPreviewTabState.hasError"
                [class.enabled]="currentPreviewTabState.isEnabled"
                (click)="handleEnhancedPreviewTabClick()"
                [title]="currentPreviewTabState.tooltipMessage">

                <!-- Tab content with loading indicator -->
                <span class="tab-text">{{ (previewTabName | async) || 'Preview' }}</span>

                <!-- Loading spinner for preview tab -->
                <i *ngIf="currentPreviewTabState.isLoading"
                   class="bi bi-arrow-repeat tab-status spinning preview-loading-spinner"></i>

                <!-- Error indicator with retry option -->
                <i *ngIf="currentPreviewTabState.hasError"
                   class="bi bi-exclamation-triangle tab-status error-indicator"
                   title="Click tab to view error details"></i>
              </div>
              <!-- Enhanced Code tab - Always visible with proper state management -->
              <div
                class="custom-button code-tab"
                [class.active]="(currentView | async) === 'editor' && (currentCodeTabState.isEnabled || currentCodeTabState.hasError)"
                [class.disabled]="!currentCodeTabState.isEnabled && !currentCodeTabState.hasError"
                [class.loading]="currentCodeTabState.isLoading"
                [class.error-tab]="currentCodeTabState.hasError"
                [class.enabled]="currentCodeTabState.isEnabled"
                (click)="handleEnhancedCodeTabClick()"
                [title]="currentCodeTabState.tooltipMessage">

                <span class="tab-text">Code</span>

                <!-- Loading indicator -->
                <i *ngIf="currentCodeTabState.isLoading"
                   class="bi bi-arrow-clockwise tab-status code-loading-spinner"
                   title="Code is being generated"></i>

                <!-- Error indicator with retry option -->
                <i *ngIf="currentCodeTabState.hasError"
                   class="bi bi-exclamation-triangle tab-status error-indicator"
                   title="Click tab to view error details"></i>
              </div>
              <!-- Artefacts tab - always shown but disabled when no artefacts are available -->
              <!-- ENHANCED: Auto-enabled when logs are available, includes logs functionality -->
              <div
                class="custom-button"
                [class.active]="isArtifactsActive | async"
                [class.disabled]="!isArtifactsTabEnabledWithLogs"
                (click)="isArtifactsTabEnabledWithLogs && onTabClick('artifacts')"
                [title]="getArtifactsTabTooltip()">
                <span>Artefacts</span>
                <i *ngIf="isStreamingLogs && hasLogs" class="bi bi-arrow-repeat tab-status spinning"></i>
              </div>
              <!-- Export tab - shown like other tabs -->
            </ng-container>
          </div>
        </div>

        <div class="header-right">
          <div class="icon-group">
            <div
              *ngIf="
                isCodeGenerationComplete &&
                !(previewError | async) &&
                (currentView | async) === 'editor'
              "
              class="custom-button"
              [class.active]="isExperienceStudioModalOpen | async"
              (click)="toggleExportModal()"
              title="Export project">
              <span>Export</span>
            </div>
            <!-- Export icon removed as it's now in the tabs -->
            <!-- ENHANCED: Hide Edit button during regeneration process -->
            <awe-icons
              *ngIf="
                (currentView | async) === 'preview' &&
                isCodeGenerationComplete &&
                !(previewError | async) &&
                !(isRegenerationInProgress$ | async)
              "
              iconName="awe_edit"
              iconColor="neutralIcon"
              title="Select element to edit"
              [class.active]="isElementSelectionMode"
              (click)="onEditButtonClick()"
              [disabled]="true"></awe-icons>
            <!-- ENHANCED: Hide Fullscreen button during regeneration process -->
            <awe-icons
              *ngIf="
                (currentView | async) === 'preview' &&
                isCodeGenerationComplete &&
                !(previewError | async) &&
                (deployedUrl | async) &&
                !(isRegenerationInProgress$ | async)
              "
              iconName="awe_external_link"
              iconColor="neutralIcon"
              title="Open preview in new tab"
              (click)="openPreviewInNewTab()"></awe-icons>
          </div>
        </div>
      </div>
    </div>

    <div awe-rightpanel-content>
      <!--Injected Code editor on right content-->
      <!-- Editor View - only show when editor is active AND logs are not active AND artifacts are not active -->
      <div
        *ngIf="
          (currentView | async) === 'editor' &&
          !(isArtifactsActive | async)
        "
        class="editor-view">
        <!-- Debug info for template conditions (remove in production) -->
        <!--
        Debug: isCodeGenerationComplete = {{ isCodeGenerationComplete }}
        Debug: currentCodeTabState.isEnabled = {{ currentCodeTabState.isEnabled }}
        Debug: files length = {{ (files | async)?.length || 0 }}
        Debug: currentView = {{ currentView | async }}
        -->

        <!-- Show loading animation when code tab content cannot be displayed -->
        <app-loading-animation
          *ngIf="!canShowCodeTabContent"
          [messages]="loadingMessages"
          [theme]="(currentTheme | async) || 'light'"></app-loading-animation>

        <!-- Show code viewer when code generation is complete OR when template files are available -->
        <app-code-viewer
          *ngIf="canShowCodeTabContent"
          [theme]="(currentTheme | async) || 'light'"
          [files]="(files | async) || []"
          [showFileExplorer]="true"></app-code-viewer>

        <!-- Fallback message when code tab content cannot be displayed -->
        <div
          *ngIf="!canShowCodeTabContent"
          class="code-tab-waiting-message">
          <p>Waiting for seed project files to load...</p>
          <p><small>Code generation complete: {{ isCodeGenerationComplete }}, Template files available: {{ isTemplateFilesAvailable }}, Files: {{ (files | async)?.length || 0 }}</small></p>
        </div>
      </div>

      <!-- Overview View - only show when overview is active AND logs are not active AND artifacts are not active -->
      <div
        *ngIf="
          (currentView | async) === 'overview' &&
          !(isArtifactsActive | async) &&
          (isUIDesignMode | async)
        "
        class="overview-view">
        <app-mobile-frame
          [pages]="(uiDesignPages | async) || []"
          [currentPageIndex]="(currentUIDesignPageIndex | async) || 0"
          [theme]="(currentTheme | async) || 'light'"
          (pageChange)="onUIDesignPageChange($event)"
          (fullscreenRequest)="onUIDesignFullscreenRequest($event)">
        </app-mobile-frame>
      </div>

      <!-- Preview View - only show when preview is active AND logs are not active AND artifacts are not active -->
      <div
        *ngIf="
          (currentView | async) === 'preview' &&
          !(isArtifactsActive | async)
        "
        class="preview-view">

        <!-- UI Design Canvas Mode -->
        <div *ngIf="isUIDesignMode | async" class="ui-design-canvas-container" #uiDesignCanvas>
          <!-- Viewport Debug Panel -->

          <!-- Canvas Info Panel -->
          <app-canvas-info></app-canvas-info>

          <!-- Canvas Selection Tooltip -->
          <div
            *ngIf="showCanvasTooltip | async"
            class="canvas-selection-tooltip"
            [ngClass]="(currentTheme | async) + '-theme'">
            <div class="tooltip-content">
              <i class="bi bi-cursor-fill"></i>
              <span>Single click to select a page for editing</span>
            </div>
          </div>

          <!-- Canvas Controls -->
          <div class="canvas-controls">
            <div class="zoom-controls">
              <button
                class="canvas-control-btn"
                (click)="zoomOutCanvas()"
                [disabled]="isCanvasAtMinZoom()"
                title="Zoom Out">
                <!-- Custom Zoom Out SVG Icon -->
                <svg class="zoom-icon" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="6.5" cy="6.5" r="5" stroke-width="1.5"/>
                  <path d="M10.5 10.5l4 4" stroke-width="1.5"/>
                  <path d="M4 6.5h5" stroke-width="1.2"/>
                </svg>
              </button>
              <span class="zoom-display">{{ getCanvasZoomPercentage() }}%</span>
              <button
                class="canvas-control-btn"
                (click)="zoomInCanvas()"
                [disabled]="isCanvasAtMaxZoom()"
                title="Zoom In">
                <!-- Custom Zoom In SVG Icon -->
                <svg class="zoom-icon" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="6.5" cy="6.5" r="5" stroke-width="1.5"/>
                  <path d="M10.5 10.5l4 4" stroke-width="1.5"/>
                  <path d="M6.5 4v5M4 6.5h5" stroke-width="1.2"/>
                </svg>
              </button>
            </div>
            <div class="view-controls">
              <button
                class="canvas-control-btn"
                (click)="resetCanvasView()"
                title="Reset View">
                <i class="bi bi-arrow-clockwise"></i>
                Reset
              </button>
              <button
                class="canvas-control-btn"
                (click)="fitCanvasToView()"
                title="Fit to View">
                <i class="bi bi-arrows-fullscreen"></i>
                Fit
              </button>
            </div>
            <!-- Multi-Selection Controls - Only visible after wireframe generation success -->
            <div
              class="selection-controls"
              *ngIf="(wireframeGenerationStateService.canShowSelectionControls$ | async) && (uiDesignNodes | async) && (uiDesignNodes | async)!.length > 0">
              <button
                class="canvas-control-btn selection-btn"
                (click)="selectAllNodes()"
                title="Select All Pages"
                [attr.aria-label]="'Select all ' + (uiDesignNodes | async)!.length + ' pages'">
                <i class="bi bi-check-square"></i>
                Select All
              </button>
              <button
                class="canvas-control-btn selection-btn"
                (click)="clearAllSelection()"
                [disabled]="getSelectedNodesCount() === 0"
                title="Clear Selection"
                [attr.aria-label]="'Clear selection of ' + getSelectedNodesCount() + ' pages'">
                <i class="bi bi-square"></i>
                Clear
              </button>
              <div class="selection-info" *ngIf="getSelectedNodesCount() > 0">
                <span class="selection-count">{{ getSelectedNodesCount() }} selected</span>
              </div>
            </div>
          </div>

          <!-- Canvas Content -->
          <div
            class="canvas-content"
            [style.transform]="getCanvasTransformStyle()"
            (mousedown)="onCanvasMouseDown($event)"
            (mousemove)="onCanvasMouseMove($event)"
            (mouseup)="onCanvasMouseUp($event)"
            (wheel)="onCanvasWheel($event)"
            #canvasContent>

            <!-- UI Design Nodes -->
            <div
              *ngFor="let node of (uiDesignNodes | async); trackBy: trackByUIDesignNode"
              class="ui-design-node"
              [style.left.px]="node.position.x"
              [style.top.px]="node.position.y"
              [style.width.px]="node.data.width"
              [style.height.px]="node.data.height"
              [style.z-index]="node.data.zIndex || 100"
              [ngClass]="uiDesignVisualFeedbackService.getEnhancedNodeClasses(node.id)"
              [class.dragging]="node.dragging"
              [class.loading]="node.data.isLoading"
              [attr.data-page-name]="node.data.displayTitle || node.data.title"
              [attr.aria-selected]="isNodeSelectedForEditing(node.id)"
              (click)="onUIDesignNodeSelect(node, $event)"
              (dblclick)="onUIDesignNodeDoubleClick(node)">

              <div class="node-header">
                <h4
                  [class.highlighted-title]="uiDesignVisualFeedbackService.isNodeSelected(node.id)"
                  [attr.aria-label]="node.data.displayTitle || node.data.title">
                  {{ node.data.displayTitle || node.data.title }}
                </h4>
              </div>

              <div class="node-content">
                <!-- Show loader when isLoading is true -->
                <div *ngIf="node.data.isLoading" class="node-loader">
                  <div class="loader-spinner"></div>
                  <div class="loader-text">Generating wireframe...</div>
                </div>

                <!-- Show iframe content when not loading -->
                <iframe
                  *ngIf="!node.data.isLoading"
                  [safeSrcdoc]="node.data.rawContent"
                  class="preview-iframe"
                  frameborder="0"
                  sandbox="allow-same-origin allow-scripts allow-top-navigation-by-user-activation">
                </iframe>
              </div>

              <!-- Only show overlay when not loading -->
              <div *ngIf="!node.data.isLoading" class="node-overlay">
                <i class="bi bi-arrows-fullscreen"></i>
                <span>Double-click to expand</span>
              </div>
            </div>

            <!-- Loading Nodes for Regeneration -->
            <div
              *ngFor="let loadingNode of (uiDesignLoadingNodes | async); trackBy: trackByUIDesignNode"
              class="ui-design-node loading-node"
              [style.left.px]="loadingNode.position.x"
              [style.top.px]="loadingNode.position.y"
              [style.width.px]="loadingNode.data.width"
              [style.height.px]="loadingNode.data.height"
              [style.z-index]="1000"
              [attr.data-page-name]="loadingNode.data.displayTitle || loadingNode.data.title"
              [attr.data-loading-for]="loadingNode.data.originalNodeId">

              <div class="node-header">
                <h4 [attr.aria-label]="loadingNode.data.displayTitle || loadingNode.data.title">
                  {{ loadingNode.data.displayTitle || loadingNode.data.title }}
                </h4>
              </div>

              <div class="node-content">
                <!-- Always show loader for loading nodes -->
                <div class="node-loader regeneration-loader">
                  <div class="loader-spinner"></div>
                  <div class="loader-text">{{ loadingNode.data.loadingMessage || 'Editing...' }}</div>
                </div>
              </div>

              <!-- Loading indicator overlay -->
              <div class="loading-overlay">
                <div class="loading-pulse"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Standard Preview Mode -->
        <ng-container *ngIf="!(isUIDesignMode | async)">
          <!-- Different states of the preview view - only one will be shown at a time -->

        <!-- Enhanced iframe loading with URL validation and availability checking -->
        <!-- Show URL validation loading state (but not during regeneration) -->
        <div
          *ngIf="
            isCodeGenerationComplete &&
            !(previewError | async) &&
            urlSafe &&
            !(isIframeReady | async) &&
            !(regenerationInProgress$ | async)
          "
          class="url-validation-container">
          <app-loading-animation
            [theme]="(currentTheme | async) || 'light'"
            [messages]="[
              'Validating deployment URL...',
              'Checking URL accessibility...',
              'Preparing iframe for loading...',
              'Almost ready to display your app...',
            ]">
          </app-loading-animation>

          <!-- Show validation error if any -->
          <div *ngIf="urlValidationError | async" class="url-validation-error">
            <div class="error-message">
              <i class="bi bi-exclamation-triangle"></i>
              <span>{{ urlValidationError | async }}</span>
            </div>
          </div>
        </div>

        <!-- Show iframe only when URL is validated, available, iframe is ready, and NOT during active regeneration -->
        <!-- ENHANCED: Allow iframe to show immediately after regeneration completes with valid URL -->
        <div
          *ngIf="
            isCodeGenerationComplete &&
            !(previewError | async) &&
            urlSafe &&
            (isIframeReady | async) &&
            (isUrlValidated | async) &&
            (isUrlAvailable | async) &&
            (!(regenerationInProgress$ | async) || (isNewPreviewEnabled && urlSafe))
          "
          class="iframe-container">
          <iframe
            [src]="urlSafe"
            class="preview-frame"
            frameborder="0"
            (load)="onIframeLoad($event)"
            (error)="onIframeError($event)"
            sandbox="allow-same-origin allow-scripts allow-popups allow-forms allow-modals allow-top-navigation-by-user-activation"
            referrerpolicy="no-referrer"
            allow="accelerometer; camera; encrypted-media; geolocation; gyroscope; microphone; midi">
          </iframe>
        </div>

        <!-- Show deployment loading state when code is complete but no URL yet OR during regeneration -->
        <div
          *ngIf="isCodeGenerationComplete && !(previewError | async) && (!urlSafe || (regenerationInProgress$ | async))"
          class="deployment-loading-container">
          <app-loading-animation
            [theme]="(currentTheme | async) || 'light'"
            [messages]="(regenerationInProgress$ | async) ? [
              'Processing your code changes...',
              'Applying edits to your application...',
              'Rebuilding with your modifications...',
              'Updating application structure...',
              'Deploying regenerated code...',
              'Almost ready - finalizing regeneration...',
              'Your updated preview will be available shortly...'
            ] : [
              'Preparing your preview deployment...',
              'Setting up hosting environment...',
              'Configuring deployment pipeline...',
              'Building application for preview...',
              'Deploying to preview server...',
              'Almost ready - finalizing deployment...',
              'Your preview will be available shortly...'
            ]">
          </app-loading-animation>
        </div>

        <!-- Initial loading state - show if code generation is not complete and either:
             1. No progress state exists, or
             2. We're in a state that's not LAYOUT_ANALYZED or PAGES_GENERATED, or
             3. We're in LAYOUT_ANALYZED or PAGES_GENERATED but have no valid layout data -->
        <ng-container
          *ngIf="
            !isCodeGenerationComplete &&
            (!currentProgressState ||
              (!currentProgressState.includes('LAYOUT_ANALYZED') &&
                !currentProgressState.includes('PAGES_GENERATED')) ||
              ((currentProgressState.includes('LAYOUT_ANALYZED') ||
                currentProgressState.includes('PAGES_GENERATED')) &&
                (!layoutData || layoutData.length === 0 || !layoutMapping[layoutData[0]])))
          ">
          <div
            class="loading-state-container"
            [style.display]="isCodeGenerationComplete || (previewError | async) ? 'none' : 'block'">
            <app-loading-animation
              [messages]="loadingMessages"
              [theme]="(currentTheme | async) || 'light'"></app-loading-animation>
          </div>
        </ng-container>

        <!-- Layout analyzed state - only show if code generation is not complete -->
        <ng-container
          *ngIf="
            !isCodeGenerationComplete &&
            currentProgressState &&
            currentProgressState.includes('LAYOUT_ANALYZED') &&
            !currentProgressState.includes('PAGES_GENERATED') &&
            layoutData &&
            layoutData.length > 0 &&
            layoutMapping[layoutData[0]]
          ">
          <div
            class="layout-examples-container"
            [style.display]="isCodeGenerationComplete || (previewError | async) ? 'none' : 'block'">
            <div class="layout-examples-header">
              <p *ngIf="layoutData.length > 1">
                {{ layoutData.length }} layouts have been identified for your application
              </p>
            </div>

            <!-- Show actual layout data - no shimmer/skeleton loading -->
            <div
              class="layout-examples-grid"
              [class.layout-examples-grid-fullscreen]="layoutData.length === 1"
              [class.layout-count-1]="layoutData.length === 1"
              [class.layout-count-2]="layoutData.length === 2"
              [class.layout-count-3]="layoutData.length === 3"
              [class.layout-count-4]="layoutData.length === 4">
              <!-- If we have layout data, use it and show all identified layouts -->
              <ng-container *ngIf="layoutData && layoutData.length > 0">
                <div
                  *ngFor="let layout of layoutData; trackBy: trackByLayoutId"
                  class="layout-example-item"
                  [class.layout-example-item-fullscreen]="layoutData.length === 1">
                  <div class="layout-example-card">
                    <div
                      class="layout-example-image"
                      [class.layout-example-image-fullscreen]="layoutData.length === 1">
                      <!-- Directly use the layout key to get the image with layout- prefix -->
                      <img
                        [src]="'assets/images/layout-' + layout + '.png'"
                        [alt]="layoutMapping[layout] || 'Identified Layout'"
                        loading="lazy"
                        decoding="async"
                        onerror="this.src='assets/images/01.png'" />
                    </div>
                    <div class="layout-example-title">
                      {{ layoutMapping[layout] || 'Identified Layout' }}
                    </div>
                  </div>
                </div>
              </ng-container>

              <!-- Fallback to default layout if none detected -->
              <ng-container *ngIf="!layoutData || layoutData.length === 0">
                <div class="layout-example-item layout-example-item-fullscreen">
                  <div class="layout-example-card">
                    <div class="layout-example-image layout-example-image-fullscreen">
                      <img
                        [src]="'assets/images/01.png'"
                        [alt]="'Default Layout'"
                        loading="lazy"
                        decoding="async" />
                    </div>
                    <div class="layout-example-title">Default Layout Structure</div>
                    <div class="layout-example-description">
                      A standard layout structure has been selected for your application
                    </div>
                  </div>
                </div>
              </ng-container>
            </div>
          </div>
        </ng-container>

        <!-- Pages generated state - TEMPORARILY COMMENTED OUT as requested -->
        <!--
        <ng-container
          *ngIf="
            !isCodeGenerationComplete &&
            currentProgressState &&
            currentProgressState.includes('PAGES_GENERATED') &&
            layoutData &&
            layoutData.length > 0 &&
            layoutMapping[layoutData[0]]
          ">
          <div
            class="pages-generated-container"
            [style.display]="isCodeGenerationComplete || (previewError | async) ? 'none' : 'block'">
            <div class="pages-generated-header">
              <h3>Page Inventory</h3>
              <p *ngIf="layoutData.length > 1">
                Using {{ layoutData.length }} different layout types for your pages
              </p>
            </div>

            <div
              class="pages-examples-grid"
              [class.layout-count-1]="layoutData.length === 1"
              [class.layout-count-2]="layoutData.length === 2"
              [class.layout-count-3]="layoutData.length === 3"
              [class.layout-count-4]="layoutData.length === 4">
              <ng-container *ngIf="layoutData && layoutData.length > 0">
                <div
                  *ngFor="let i of [0, 1, 2, 3, 4, 5, 6, 7]; trackBy: trackByPageIndex"
                  class="page-example-item">
                  <div class="page-example-card">
                    <div class="page-example-image">
                      <img
                        [src]="'assets/images/layout-' + getLayoutForPageIndex(i) + '.png'"
                        [alt]="getPageTitle(i, getLayoutForPageIndex(i))"
                        loading="lazy"
                        decoding="async"
                        onerror="this.src='assets/images/0' + (i + 1 > 8 ? '1' : (i + 1)) + '.png'"
                        [attr.data-index]="i + 1" />
                    </div>
                    <div class="page-example-title">
                      {{ getPageTitle(i, getLayoutForPageIndex(i)) }}
                    </div>
                  </div>
                </div>
              </ng-container>

              <ng-container *ngIf="!layoutData || layoutData.length === 0">
                <div
                  *ngFor="
                    let image of layoutExampleImages.slice(0, 8);
                    let i = index;
                    trackBy: trackByPageIndex
                  "
                  class="page-example-item">
                  <div class="page-example-card">
                    <div class="page-example-image">
                      <img
                        [src]="image"
                        [alt]="getPageTitle(i, '')"
                        loading="lazy"
                        decoding="async" />
                    </div>
                    <div class="page-example-title">
                      {{ getPageTitle(i, '') }}
                    </div>
                  </div>
                </div>
              </ng-container>
            </div>
          </div>
        </ng-container>
        -->

          <!-- Show error page if preview failed -->
          <app-error-page
            *ngIf="previewError | async"
            [theme]="(currentTheme | async) || 'light'"
            [errorDescription]="(errorDescription | async) || 'An error occurred'"
            [terminalOutput]="(errorTerminalOutput | async) || ''"
            [progressState]="pollingStatus"
            (retry)="onRetryClick()"
            (goHome)="navigateToHome()"
            (showDetails)="toggleLogsView()">
          </app-error-page>
        </ng-container>
      </div>

      <!-- REMOVED: Standalone Logs View - now integrated into Artifacts tab -->

      <!-- Artifacts View - only show when artifacts tab is active and enabled -->
      <div
        *ngIf="
          (currentView | async) === 'artifacts' &&
          (isArtifactsActive | async) &&
          isArtifactsTabEnabledWithLogs
        "
        class="artifacts-view">
        <!-- Show loading animation when artefacts/logs are not available -->
        <app-loading-animation
          *ngIf="!isArtifactsTabEnabledWithLogs || (artifactsData.length === 0 && !hasLogs)"
          [theme]="(currentTheme | async) || 'light'"
          [messages]="[
            'Loading project artefacts...',
            'Preparing file viewer...',
            'Organizing project resources...',
            'Collecting design assets...',
            'Analyzing project structure...',
            'Gathering documentation...',
            'Preparing application logs...',
          ]">
        </app-loading-animation>

        <!-- Show artefacts/logs when they are available -->
        <div *ngIf="isArtifactsTabEnabledWithLogs && (artifactsData.length > 0 || hasLogs)" class="artifacts-container">
          <!-- File viewer with split view -->
          <div class="artifacts-content">
            <!-- Main content area with file explorer and file content -->
            <div class="artifacts-main-content">
              <!-- Left sidebar with file tree -->
              <div class="file-explorer">
                <div class="file-explorer-header">
                  <span>Files</span>
                </div>
                <div class="file-list">
                  <div
                    *ngFor="let file of getFilteredArtifactsData()"
                    class="file-item"
                    [class.selected]="selectedArtifactFile === file"
                    (click)="selectArtifactFile(file)">
                    <span class="file-icon" [ngClass]="getFileIconClass(file.type)"></span>
                    <span class="file-name">{{ file.name }}</span>
                  </div>
                </div>
              </div>

              <!-- Right content area -->
            <div class="file-content">
              <!-- Show message when no file is selected -->
              <div *ngIf="!selectedArtifactFile" class="no-file-selected">
                <span>Getting your Artefacts Ready...</span>
              </div>

              <!-- Show content based on file type -->
              <ng-container *ngIf="selectedArtifactFile">
                <!-- Markdown content -->
                <div *ngIf="selectedArtifactFile.type === 'markdown'" class="markdown-content">
                  <!-- Use typewriter content for Project Overview, regular content for others -->
                  <markdown
                    [data]="selectedArtifactFile.name === 'Project Overview' ?
                            getArtifactVisibleContent(selectedArtifactFile.name) :
                            selectedArtifactFile.content">
                  </markdown>
                </div>

                <!-- Image content -->
                <div *ngIf="selectedArtifactFile.type === 'image'" class="image-content">
                  <!-- Layout Analyzed content -->
                  <div
                    *ngIf="selectedArtifactFile.name === 'Layout Analyzed'"
                    class="layout-examples-container">
                    <div class="layout-examples-header">
                      <p *ngIf="layoutAnalyzedData && layoutAnalyzedData.length > 1">
                        {{ layoutAnalyzedData.length }} layouts have been identified for your
                        application
                      </p>
                      <p *ngIf="layoutAnalyzedData && layoutAnalyzedData.length === 1">
                        A layout has been identified for your application
                      </p>
                    </div>

                    <!-- Show analyzing animation or actual layout data -->
                    <div
                      class="layout-examples-grid"
                      [class.layout-examples-grid-fullscreen]="shouldShowAnalyzingLayout() || layoutAnalyzedData.length === 1"
                      [class.layout-count-1]="shouldShowAnalyzingLayout() || layoutAnalyzedData.length === 1"
                      [class.layout-count-2]="!shouldShowAnalyzingLayout() && layoutAnalyzedData.length === 2"
                      [class.layout-count-3]="!shouldShowAnalyzingLayout() && layoutAnalyzedData.length === 3"
                      [class.layout-count-4]="!shouldShowAnalyzingLayout() && layoutAnalyzedData.length === 4">

                      <!-- Show analyzing layout animation when layout is being analyzed -->
                      <ng-container *ngIf="shouldShowAnalyzingLayout()">
                        <div class="layout-example-item layout-example-item-fullscreen">
                          <div class="layout-example-card">
                            <div class="layout-example-animation layout-example-animation-fullscreen">
                              <!-- Use the analyzing layout animation component -->
                              <app-analyzing-layout-animation
                                [theme]="(currentTheme | async) || 'light'"
                                [layoutKey]="getDefaultLayoutKeyForAnalyzing()"
                                [showAnalyzing]="true">
                              </app-analyzing-layout-animation>
                            </div>
                          </div>
                        </div>
                      </ng-container>

                      <!-- Show actual layout data when layout has been identified -->
                      <ng-container *ngIf="!shouldShowAnalyzingLayout() && layoutAnalyzedData && layoutAnalyzedData.length > 0">
                        <div
                          *ngFor="let layout of layoutAnalyzedData; trackBy: trackByLayoutId"
                          class="layout-example-item"
                          [class.layout-example-item-fullscreen]="layoutAnalyzedData.length === 1">
                          <div class="layout-example-card">
                            <div
                              class="layout-example-animation"
                              [class.layout-example-animation-fullscreen]="
                                layoutAnalyzedData.length === 1
                              ">
                              <!-- Use the layout identified animation component -->
                              <app-layout-identified-animation
                                [layoutKey]="layout.key"
                                [theme]="(currentTheme | async) || 'light'">
                              </app-layout-identified-animation>
                            </div>
                            <div
                              class="layout-example-description"
                              *ngIf="layoutAnalyzedData.length === 1">
                              <p>A layout has been identified for your application</p>
                            </div>
                          </div>
                        </div>
                      </ng-container>

                      <!-- Fallback to analyzing animation if no layout data -->
                      <ng-container *ngIf="!shouldShowAnalyzingLayout() && (!layoutAnalyzedData || layoutAnalyzedData.length === 0)">
                        <div class="layout-example-item layout-example-item-fullscreen">
                          <div class="layout-example-card">
                            <div class="layout-example-animation layout-example-animation-fullscreen">
                              <!-- Use analyzing layout animation as fallback -->
                              <app-analyzing-layout-animation
                                [theme]="(currentTheme | async) || 'light'"
                                [layoutKey]="'HB'"
                                [showAnalyzing]="true">
                              </app-analyzing-layout-animation>
                            </div>
                          </div>
                        </div>
                      </ng-container>
                    </div>
                  </div>

                  <!-- Regular image content for other image types -->
                  <img
                    *ngIf="selectedArtifactFile.name !== 'Layout Analyzed'"
                    [src]="selectedArtifactFile.content"
                    [alt]="selectedArtifactFile.name"
                    loading="lazy"
                    decoding="async" />
                </div>

                <!-- SVG content -->
                <div *ngIf="selectedArtifactFile.type === 'svg'" class="svg-content">
                  <img
                    [src]="selectedArtifactFile.content"
                    [alt]="selectedArtifactFile.name"
                    loading="lazy"
                    decoding="async" />
                </div>

                <!-- Text content -->
                <div *ngIf="selectedArtifactFile.type === 'text'" class="text-content">
                  <pre>{{ selectedArtifactFile.content }}</pre>
                </div>

                <!-- Component content (for design system) -->
                <div *ngIf="selectedArtifactFile.type === 'component'" class="component-content">
                  <!-- Design System Component -->
                  <div
                    *ngIf="selectedArtifactFile.name === 'Design System'"
                    class="design-system-container">

                    <!-- Show analyzing animation while loading design tokens -->
                    <ng-container *ngIf="shouldShowDesignTokenLoadingAnimation(); else designSystemContent">
                      <app-analyzing-design-tokens-animation
                        [theme]="(currentTheme | async) || 'light'"
                        [showAnalyzing]="true">
                      </app-analyzing-design-tokens-animation>
                    </ng-container>

                    <!-- Design System Content -->
                    <ng-template #designSystemContent>
                      <!-- Design System Header with Edit Button -->
                      <div class="design-system-header">
                        <h2>Design System</h2>
                        <button
                          class="design-token-edit-btn"
                          [class.edit-mode]="(designTokenEditState | async)?.isEditMode"
                          [disabled]="true"
                          [title]="'Edit mode will be available soon'"
                          style="opacity: 0.5; cursor: not-allowed;">
                          {{ getDesignTokenEditButtonText() }}
                        </button>
                      </div>

                      <!-- Colors Section (Only show if we have actual tokens from polling response) -->
                      <div class="design-section" *ngIf="hasActualDesignTokensFromPolling()">
                        <h3>Colours</h3>
                        <div class="color-swatches">
                          <div
                            *ngFor="let token of getTokensByCategory('Colors')"
                            class="color-swatch">
                            <div class="color-box" [style.background-color]="token.value"></div>
                            <div class="color-details">
                              <div class="color-name">{{ token.name }}</div>
                              <div class="color-value">
                                <input
                                  type="text"
                                  class="token-input"
                                  [class.disabled]="areDesignTokenInputsDisabled()"
                                  [value]="token.value"
                                  [disabled]="areDesignTokenInputsDisabled()"
                                  (change)="onTokenValueChange($event, token.id)"
                                  [attr.title]="areDesignTokenInputsDisabled() ? 'Click Edit to modify colors' : 'Edit ' + token.name + ' color value'" />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </ng-template>

                    <!-- Combined Font Style and Buttons Section (With Single Coming Soon Overlay) -->
                    <!-- <div class="design-section coming-soon-section combined-sections">
                      <div class="coming-soon-overlay">
                        <div class="coming-soon-content">
                          <div class="coming-soon-icon">🎨</div>
                          <div class="coming-soon-text">Coming Soon</div>
                          <div class="coming-soon-subtitle">
                            Font and button customization will be available soon
                          </div>
                        </div>
                      </div> -->

                      <!-- Font Style Section -->
                      <!-- <h3>Font Style Desktop</h3>
                      <div class="font-styles">
                        <div
                          *ngFor="let token of getTokensByCategory('Font Style Desktop')"
                          class="font-style-item">
                          <h2 *ngIf="token.name === 'Headline 1'" class="headline-1">Headline 1</h2>
                          <h3 *ngIf="token.name === 'Headline 2'" class="headline-2">Headline 2</h3>
                          <h4 *ngIf="token.name === 'Headline 3'" class="headline-3">Headline 3</h4>
                          <h5 *ngIf="token.name === 'Headline 4'" class="headline-4">Headline 4</h5>
                          <div class="font-details">
                            <span>{{ token.value }}</span>
                          </div>
                        </div>
                      </div> -->

                      <!-- Buttons Section -->
                      <!-- <h3>Buttons</h3>
                      <div class="button-samples">
                        <div
                          *ngFor="let token of getTokensByCategory('Buttons')"
                          class="button-sample">
                          <button [class]="'sample-button ' + token.value">
                            {{ token.name }}
                            <span *ngIf="token.value.includes('icon')" class="button-icon">✨</span>
                          </button>
                        </div>
                      </div>
                    </div> -->





                  </div>

                  <!-- Placeholder for other component types -->
                  <div
                    *ngIf="selectedArtifactFile.name !== 'Design System'"
                    class="shimmer-container">
                    <div class="shimmer-block"></div>
                    <div class="shimmer-block"></div>
                    <div class="shimmer-block"></div>
                  </div>
                </div>
              </ng-container>

              <!-- ENHANCED: Logs content when "Application Logs" file is selected -->
              <div *ngIf="selectedArtifactFile && selectedArtifactFile.name === 'Application Logs'" class="logs-file-content">
                <!-- Show loading animation when no logs are available -->
                <app-loading-animation
                  *ngIf="!hasLogs"
                  [theme]="(currentTheme | async) || 'light'"
                  [messages]="[
                    'Initializing build process...',
                    'Setting up compilation pipeline...',
                    'Preparing application logs...',
                    'Logs will appear here as they become available...'
                  ]">
                </app-loading-animation>

                <!-- Show logs when they are available -->
                <div *ngIf="hasLogs || isCodeGenerationComplete" class="logs-container">
                  <div class="logs-content">
                    <!-- New formatted logs display with letter-by-letter typing effect -->
                    <div class="formatted-logs-container">
                      <!-- Regular log entries with trackBy for more efficient rendering -->
                      <div
                        *ngFor="let log of formattedLogMessages; let i = index; trackBy: trackByLogIndex"
                        class="log-entry"
                        [class]="getLogClass(log)"
                        [class.new-log]="i >= formattedLogMessages.length - 5">

                        <!-- Code block with capsule-like container -->
                        <div
                          *ngIf="log.type === 'code'"
                          class="code-capsule"
                          [class.expanded]="isCodeExpanded(log.id)"
                          [class.dark-theme]="(currentTheme | async) === 'dark'">
                          <!-- Code header with file path, timestamp and toggle button -->
                          <div class="code-header" (click)="toggleCodeExpansion(log)">
                            <div class="code-header-left">
                              <span class="file-path">{{ log.path || 'unknown.file' }}</span>
                              <span class="log-timestamp">{{ log.timestamp }}</span>
                            </div>
                            <div class="code-header-actions">
                              <span class="toggle-icon">
                                {{ isCodeExpanded(log.id) ? '▼' : '▶' }}
                              </span>
                            </div>
                          </div>

                          <!-- Code content with typing effect - only shown when expanded -->
                          <div
                            class="code-content-wrapper"
                            [class.expanded]="isCodeExpanded(log.id)"
                            [style.--max-content-height.px]="log.maxHeight || 500">
                            <pre class="code-content">
                              <code
                                [innerHTML]="formatCodeForDisplay(log.visibleContent || '')"
                                [class.typing]="isTypingLog && i === currentLogIndex"></code>
                            </pre>
                          </div>
                        </div>

                        <!-- Regular log content with typing effect -->
                        <div
                          *ngIf="log.type !== 'code'"
                          class="log-content-wrapper">
                          <!-- Timestamp and log content on same line -->
                          <div class="log-line">
                            <span class="log-timestamp">{{ log.timestamp }}</span>
                            <span
                              class="log-content"
                              [class.typing]="isTypingLog && i === currentLogIndex">
                              <span class="log-text">{{ log.visibleContent || '' }}</span>
                            </span>
                          </div>
                        </div>
                      </div>

                      <!-- Show a message when no logs are available -->
                      <div *ngIf="formattedLogMessages.length === 0" class="no-logs-message">
                        <span>No logs available yet. Logs will appear here when the application generates them.</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            </div>
            <!-- End of artifacts-main-content -->

            <!-- ENHANCED: Application Logs Footer Section -->
            <div *ngIf="hasLogs || formattedLogMessages.length > 0" class="artifacts-logs-footer">
              <div class="logs-footer-header" (click)="toggleLogsFooter()">
                <div class="logs-footer-title">
                  <span class="logs-title-text">Application Logs</span>
                  <span class="logs-count" *ngIf="formattedLogMessages.length > 0">({{ formattedLogMessages.length }})</span>
                </div>
                <div class="logs-footer-toggle">
                  <span class="toggle-icon">{{ isLogsFooterExpanded ? '▼' : '▲' }}</span>
                </div>
              </div>

              <div class="logs-footer-content" [class.expanded]="isLogsFooterExpanded">
                <!-- Show loading animation when no logs are available -->
                <app-loading-animation
                  *ngIf="!hasLogs && formattedLogMessages.length === 0"
                  [theme]="(currentTheme | async) || 'light'"
                  [messages]="[
                    'Initializing build process...',
                    'Setting up compilation pipeline...',
                    'Preparing application logs...',
                    'Logs will appear here as they become available...'
                  ]">
                </app-loading-animation>

                <!-- Show logs when they are available -->
                <div *ngIf="hasLogs || formattedLogMessages.length > 0" class="logs-container">
                  <div class="logs-content">
                    <!-- New formatted logs display with letter-by-letter typing effect -->
                    <div class="formatted-logs-container">
                      <!-- Regular log entries with trackBy for more efficient rendering -->
                      <div
                        *ngFor="let log of formattedLogMessages; let i = index; trackBy: trackByLogIndex"
                        class="log-entry"
                        [class]="getLogClass(log)"
                        [class.new-log]="i >= formattedLogMessages.length - 5">

                        <!-- Code block with capsule-like container -->
                        <div
                          *ngIf="log.type === 'code'"
                          class="code-capsule"
                          [class.expanded]="isCodeExpanded(log.id)"
                          [class.dark-theme]="(currentTheme | async) === 'dark'">
                          <!-- Code header with file path, timestamp and toggle button -->
                          <div class="code-header" (click)="toggleCodeExpansion(log)">
                            <div class="code-header-left">
                              <span class="file-path">{{ log.path || 'unknown.file' }}</span>
                              <span class="log-timestamp">{{ log.timestamp }}</span>
                            </div>
                            <div class="code-header-actions">
                              <span class="toggle-icon">
                                {{ isCodeExpanded(log.id) ? '▼' : '▶' }}
                              </span>
                            </div>
                          </div>

                          <!-- Code content with typing effect - only shown when expanded -->
                          <div
                            class="code-content-wrapper"
                            [class.expanded]="isCodeExpanded(log.id)"
                            [style.--max-content-height.px]="log.maxHeight || 500">
                            <pre class="code-content">
                              <code
                                [innerHTML]="formatCodeForDisplay(log.visibleContent || '')"
                                [class.typing]="isTypingLog && i === currentLogIndex"></code>
                            </pre>
                          </div>
                        </div>

                        <!-- Regular log content with typing effect -->
                        <div
                          *ngIf="log.type !== 'code'"
                          class="log-content-wrapper">
                          <!-- Timestamp and log content on same line -->
                          <div class="log-line">
                            <span class="log-timestamp">{{ log.timestamp }}</span>
                            <span
                              class="log-content"
                              [class.typing]="isTypingLog && i === currentLogIndex">
                              <span class="log-text">{{ log.visibleContent || '' }}</span>
                            </span>
                          </div>
                        </div>
                      </div>

                      <!-- Show a message when no logs are available -->
                      <div *ngIf="formattedLogMessages.length === 0" class="no-logs-message">
                        <span>No logs available yet. Logs will appear here when the application generates them.</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </awe-rightpanel>
</awe-splitscreen>

<!-- Custom Modal Implementation -->
<div
  class="custom-modal-overlay"
  *ngIf="isExperienceStudioModalOpen | async"
  (click)="toggleExportModal()">
  <div class="custom-modal-content" (click)="$event.stopPropagation()">
    <div class="custom-modal-header">
      <h3>Export this project</h3>
      <button class="custom-close-button" (click)="toggleExportModal()">×</button>
    </div>
    <div class="custom-modal-body">
      <div class="sharable-link-section">
        <p class="section-label">Sharable link</p>
        <div class="link-input-container">
          <input
            class="link-input"
            type="text"
            [value]="
              (deployedUrl | async) || 'Deployment URL will be available after deployment completes'
            "
            readonly
            #sharableLink />
          <button class="copy-button" (click)="copyToClipboard(sharableLink)">Copy</button>
        </div>
      </div>

      <div class="export-options">
        <div class="export-option-row">
          <div class="export-option-item" (click)="downloadProject()" [class.loading]="isDownloadLoading | async">
            <div class="option-icon-container download">
              <!-- ENHANCEMENT: Show loading spinner during download -->
              <div *ngIf="isDownloadLoading | async" class="loading-spinner">
                <div class="spinner"></div>
              </div>
              <img
                *ngIf="!(isDownloadLoading | async)"
                src="assets/icons/awe_download.svg"
                [ngClass]="{ 'dark-icon': (currentTheme | async) === 'dark' }"
                alt="Download"
                width="32"
                height="32"
                loading="lazy"
                decoding="async" />
            </div>
            <span class="option-label">{{ (isDownloadLoading | async) ? 'Downloading...' : 'Download' }}</span>
          </div>

          <div class="export-option-item disabled"
               title="Azure export is coming soon."
               [attr.aria-label]="'Azure export - Coming soon. This feature will allow you to export your project directly to Azure once it is available.'">
            <div class="option-icon-container disabled">
              <img
                src="assets/icons/awe_azure.svg"
                width="32"
                height="32"
                loading="lazy"
                decoding="async" />
            </div>
            <span class="option-label">Azure</span>
          </div>

          <div class="export-option-item vscode-option-container">
            <div class="option-icon-container" (click)="cloneInVSCode(); $event.stopPropagation()" style="cursor:pointer;">
              <img
                src="assets/icons/awe_vscode.svg"
                width="32"
                height="32"
                loading="lazy"
                decoding="async" />
            </div>
            <span class="option-label">VSCode</span>

            <!-- VSCode sub-options -->
            <!-- <div class="vscode-sub-options">
              <button
                class="vscode-sub-option-btn"
                (click)="exportToVSCode(); $event.stopPropagation()"
                [disabled]="false">
                <i class="bi bi-download"></i>
                Export Project
              </button>
              <button
                class="vscode-sub-option-btn clone-btn"
                (click)="cloneInVSCode(); $event.stopPropagation()"
                [disabled]="(isCloneLoading | async)">
                <i class="bi bi-git" *ngIf="!(isCloneLoading | async)"></i>
                <i class="bi bi-arrow-clockwise spin" *ngIf="(isCloneLoading | async)"></i>
                <span *ngIf="!(isCloneLoading | async)">Clone in VS Code</span>
                <span *ngIf="(isCloneLoading | async)">Cloning...</span>
              </button>
            </div> -->
          </div>
        </div>

        <!-- Export Disclaimer -->
        <div class="export-disclaimer">
          <p class="disclaimer-text">
            <img
              src="assets/icons/awe_info_t.svg"
              alt="Info"
              class="info-theme-svg"
              [ngClass]="(currentTheme | async) === 'dark' ? 'dark' : 'light'"
              style="margin-right: 6px; vertical-align: middle; width: 18px; height: 18px;"
            />
            Get your Auth token to export to VS Code.
          </p>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Simple Full Screen Image Preview Overlay -->
<div class="image-overlay" *ngIf="showPreview && previewImage">
  <button
    class="close-button"
    (click)="closeImagePreview()"
    role="button"
    tabindex="0"
    aria-label="Close preview">
    ×
  </button>
  <img
    [src]="previewImage.url"
    [alt]="previewImage.name"
    loading="eager"
    decoding="async"
    fetchpriority="high" />
</div>

<!-- UI Design Full-Screen Modal -->
<div
  class="ui-design-fullscreen-overlay"
  *ngIf="isUIDesignFullScreenOpen | async"
  [class.modal-fullscreen]="isUIDesignModalFullScreen | async"
  (click)="closeUIDesignFullScreen()">
  <div class="ui-design-fullscreen-modal"
       [class.modal-fullscreen]="isUIDesignModalFullScreen | async"
       (click)="$event.stopPropagation()">
    <!-- Modal Header -->
    <div class="ui-design-modal-header">
      <div class="modal-title">
        <h3>{{ (selectedUIDesignNode | async)?.data?.displayTitle || (selectedUIDesignNode | async)?.data?.title || 'UI Design Preview' }}</h3>
      </div>
      <div class="modal-controls">
        <!-- View Mode Toggle -->
        <div class="view-mode-toggle">
          <button
            class="view-mode-btn"
            [class.active]="(uiDesignViewMode | async) === 'mobile'"
            (click)="switchUIDesignViewMode('mobile')"
            title="Mobile View">
            <i class="bi bi-phone"></i>
            Mobile
          </button>
          <button
            class="view-mode-btn"
            [class.active]="(uiDesignViewMode | async) === 'web'"
            (click)="switchUIDesignViewMode('web')"
            title="Web View">
            <i class="bi bi-laptop"></i>
            Web
          </button>
        </div>

        <!-- Control Buttons -->
        <div class="modal-control-buttons">
          <!-- Open in New Tab Button -->
          <button
            class="control-btn new-tab-btn"
            (click)="openUIDesignInNewTab()"
            title="Open in New Tab">
            <!-- Custom External Link SVG Icon -->
            <svg class="new-tab-icon" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M10 2h4v4M14 2l-6 6M8 2H3a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h10a1 1 0 0 0 1-1V8" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>

          <!-- View Code Button -->
          <button
            class="control-btn view-code-btn"
            (click)="openUIDesignCodeViewer()"
            title="View Code">
            <img src="assets/icons/code.svg" alt="View Code" class="control-icon">
          </button>

          <!-- Fullscreen Toggle Button -->
          <button
            class="control-btn fullscreen-btn"
            (click)="toggleUIDesignModalFullScreen()"
            [title]="(isUIDesignModalFullScreen | async) ? 'Exit Fullscreen' : 'Enter Fullscreen'">
            <!-- Custom Fullscreen SVG Icon -->
            <svg class="fullscreen-icon" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g *ngIf="!(isUIDesignModalFullScreen | async)">
                <!-- Expand to fullscreen icon -->
                <path d="M1.5 1.5h4M1.5 1.5v4M1.5 1.5l4 4M14.5 1.5h-4M14.5 1.5v4M14.5 1.5l-4 4M1.5 14.5h4M1.5 14.5v-4M1.5 14.5l4-4M14.5 14.5h-4M14.5 14.5v-4M14.5 14.5l-4-4" stroke-width="1.5" stroke-linecap="round"/>
              </g>
              <g *ngIf="isUIDesignModalFullScreen | async">
                <!-- Exit fullscreen icon -->
                <path d="M5.5 1.5h-4M5.5 1.5v4M5.5 1.5l-4 4M10.5 1.5h4M10.5 1.5v4M10.5 1.5l4 4M5.5 14.5h-4M5.5 14.5v-4M5.5 14.5l-4-4M10.5 14.5h4M10.5 14.5v-4M10.5 14.5l4-4" stroke-width="1.5" stroke-linecap="round"/>
              </g>
            </svg>
          </button>

          <!-- Close Button -->
          <button
            class="control-btn close-btn"
            (click)="closeUIDesignFullScreen()"
            title="Close Preview">
            <!-- Custom Close SVG Icon -->
            <svg class="close-icon" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 4L4 12M4 4l8 8" stroke-width="1.5" stroke-linecap="round"/>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Modal Content -->
    <div class="ui-design-modal-content">
      <div
        class="ui-design-preview-container"
        [class.mobile-view]="(uiDesignViewMode | async) === 'mobile'"
        [class.web-view]="(uiDesignViewMode | async) === 'web'">
        <iframe
          *ngIf="selectedUIDesignNode | async"
          [safeSrcdoc]="(selectedUIDesignNode | async)?.data?.rawContent || ''"
          class="ui-design-preview-iframe"
          frameborder="0"
          sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-downloads allow-top-navigation-by-user-activation">
        </iframe>
      </div>
    </div>
  </div>
</div>

<!-- UI Design Code Viewer Modal -->
<div
  class="code-viewer-overlay"
  *ngIf="isUIDesignCodeViewerOpen | async"
  (click)="closeUIDesignCodeViewer()">
  <div class="code-viewer-modal" (click)="$event.stopPropagation()">
    <!-- VS Code-like Title Bar -->
    <div class="code-viewer-titlebar">
      <div class="titlebar-left">
        <div class="file-tab">
          <img src="assets/icons/code.svg" alt="HTML" class="file-icon">
          <span class="file-name">{{ codeFileName }}</span>
        </div>
      </div>
      <div class="titlebar-center">
        <span class="app-title">Code View</span>
      </div>
      <div class="titlebar-right">
        <button class="action-button copy-btn" (click)="copyUIDesignCodeToClipboard()" title="Copy to Clipboard">
          <img src="assets/icons/copy.svg" alt="Copy" class="action-icon">
          <span>Copy</span>
        </button>
        <button class="action-button download-btn" (click)="downloadUIDesignCode()" title="Download HTML">
          <img src="assets/icons/download.svg" alt="Download" class="action-icon">
          <span>Download</span>
        </button>
        <button class="action-button close-btn" (click)="closeUIDesignCodeViewer()" title="Close">
          <img src="assets/icons/close.svg" alt="Close" class="action-icon">
        </button>
      </div>
    </div>

    <!-- Code Editor Area -->
    <div class="code-editor-container">
      <!-- Line Numbers -->
      <div class="line-numbers" #lineNumbers>
        <div class="line-number" *ngFor="let lineNumber of codeLineNumbers; trackBy: trackByLineNumber">{{ lineNumber }}</div>
      </div>

      <!-- Code Content -->
      <div class="code-content" #codeContent (scroll)="onCodeScroll($event)">
        <pre class="code-display"><code [innerHTML]="highlightedCode"></code></pre>
      </div>
    </div>


  </div>
</div>
