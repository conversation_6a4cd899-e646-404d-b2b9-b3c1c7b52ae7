import { Component, Input, OnInit, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-layout-identified-animation',
  standalone: true,
  imports: [CommonModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    <div class="layout-identified-container" [ngClass]="theme + '-theme'">
      <div class="layout-animation-wrapper">
        <!-- Layout Animation based on layout key -->
        <div [ngClass]="getLayoutClasses()" class="layout-preview active">
          <!-- HB Layout -->
          <ng-container *ngIf="layoutKey === 'HB'">
            <div class="header"></div>
            <div class="body"></div>
            <div class="responsive-indicator"></div>
          </ng-container>

          <!-- HBF Layout -->
          <ng-container *ngIf="layoutKey === 'HBF'">
            <div class="header"></div>
            <div class="body"></div>
            <div class="footer"></div>
            <div class="responsive-indicator"></div>
          </ng-container>

          <!-- HLSB Layout -->
          <ng-container *ngIf="layoutKey === 'HLSB'">
            <div class="header"></div>
            <div class="hlsb-content">
              <div class="sidebar"></div>
              <div class="body"></div>
            </div>
            <div class="responsive-indicator"></div>
          </ng-container>

          <!-- HLSBF Layout -->
          <ng-container *ngIf="layoutKey === 'HLSBF'">
            <div class="header"></div>
            <div class="hlsbf-content">
              <div class="sidebar"></div>
              <div class="body"></div>
            </div>
            <div class="footer"></div>
            <div class="responsive-indicator"></div>
          </ng-container>

          <!-- HBRS Layout -->
          <ng-container *ngIf="layoutKey === 'HBRS'">
            <div class="header"></div>
            <div class="hbrs-content">
              <div class="body"></div>
              <div class="sidebar"></div>
            </div>
            <div class="responsive-indicator"></div>
          </ng-container>

          <!-- HBRSF Layout -->
          <ng-container *ngIf="layoutKey === 'HBRSF'">
            <div class="header"></div>
            <div class="hbrsf-content">
              <div class="body"></div>
              <div class="sidebar"></div>
            </div>
            <div class="footer"></div>
            <div class="responsive-indicator"></div>
          </ng-container>

          <!-- HLSBRS Layout -->
          <ng-container *ngIf="layoutKey === 'HLSBRS'">
            <div class="header"></div>
            <div class="hlsbrs-content">
              <div class="sidebar"></div>
              <div class="body"></div>
              <div class="sidebar"></div>
            </div>
            <div class="responsive-indicator"></div>
          </ng-container>

          <!-- HLSBRSF Layout -->
          <ng-container *ngIf="layoutKey === 'HLSBRSF'">
            <div class="header"></div>
            <div class="hlsbrsf-content">
              <div class="sidebar"></div>
              <div class="body"></div>
              <div class="sidebar"></div>
            </div>
            <div class="footer"></div>
            <div class="responsive-indicator"></div>
          </ng-container>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./layout-identified-animation.component.scss']
})
export class LayoutIdentifiedAnimationComponent implements OnInit {
  @Input() layoutKey: string = 'HB';
  @Input() theme: 'light' | 'dark' = 'light';

  ngOnInit(): void {

  }

  getLayoutClasses(): string {
    return `${this.layoutKey.toLowerCase()}`;
  }
}
