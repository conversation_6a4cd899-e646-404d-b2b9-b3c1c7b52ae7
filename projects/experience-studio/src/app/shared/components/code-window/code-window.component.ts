import {
  Component,
  HostListener,
  On<PERSON><PERSON>t,
  On<PERSON><PERSON>roy,
  ChangeDetectorRef,
  Input,
  NgZone,
  ViewChild,
  ElementRef,
  AfterViewInit,
  ChangeDetectionStrategy,
  inject,
  DestroyRef,
  signal,
} from '@angular/core';
import { Subscription, Subject, BehaviorSubject, Observable, combineLatest, firstValueFrom } from 'rxjs';
import { takeUntil, take, filter, timeout } from 'rxjs/operators';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { DomSanitizer } from '@angular/platform-browser';
import { HttpClient, HttpParams, HttpErrorResponse } from '@angular/common/http';

import { UIDesignCanvasService } from './services/ui-design-canvas.service';
import { UIDesignNode, UIDesignNodeService } from './services/ui-design-node.service';
import { UIDesignViewportService } from './services/ui-design-viewport.service';
import { CodeWindowStateService } from './services/code-window-state.service';
import { CodeWindowChatService } from './services/code-window-chat.service';
import { SafeSrcdocDirective } from '../../directives/safe-srcdoc.directive';
import {
  IconsComponent,
  LeftPanelComponent,
  RightPanelComponent,
  SplitScreenComponent,
} from '@awe/play-comp-library';
import { FileModel } from '../code-viewer/code-viewer.component';
import { SafeResourceUrl } from '@angular/platform-browser';
import { CodeGenerationService } from '../../services/code-generation.service';
import { CodeSharingService } from '../../services/code-sharing.service';

import { PollingService } from '../../services/polling.service';

import { EnhancedSSEService } from '../../services/enhanced-sse.service';
import { SSEDataProcessorService } from '../../services/sse-data-processor.service';
import { RegenerationCheckpointService } from '../../services/regeneration-checkpoint.service';
import { ThemeService } from '../../services/theme-service/theme.service';
import { AppStateService } from '../../services/app-state.service';
import { PromptBarService } from '../../services/prompt-bar-services/prompt-bar.service';
import { PromptSubmissionService } from '../../services/prompt-submission.service';
import { UserSignatureService } from '../../services/user-signature.service';
import { TextTransformationService } from '../../services/text-transformation.service';
import { StepperStateService } from '../../services/stepper-state.service';
import { ToastService } from '../../services/toast.service';
import { NewPollingResponseProcessorService } from '../../services/new-polling-response-processor.service';
import {
  VSCodeExportService,
  VSCodeExportResult,
} from '../../services/vscode-export/vscode-export.service';
import { FileTreePersistenceService } from '../../services/file-tree-persistence.service';
import { MonacoStateManagementService } from '../../services/monaco-state-management.service';
import { ArtifactsService } from '../../services/artifacts.service';
import { SSEConnectionDeduplicationService } from '../../services/sse-connection-deduplication.service';


import {
  GenerateUIDesignService,
  UIDesignResponseData,
} from '../../services/generate-ui-design.service';
import {
  UIDesignSelectionService,
  MultiSelectedNodeData,
} from '../../services/ui-design-selection.service';
import { UIDesignEditService } from '../../services/ui-design-edit.service';
import { UIDesignNodePositioningService } from './services/ui-design-node-positioning.service';
import { UIDesignVisualFeedbackService } from '../../services/ui-design-visual-feedback.service';
import { WireframeGenerationStateService } from '../../services/wireframe-generation-state.service';
import { GenerationStateService } from '../../services/generation-state.service';
import { TemplateLoadingService } from '../../services/template-loading.service';
import { FilenameNormalizationService } from '../../services/filename-normalization.service';
import { UIDesignFilenameTransformerService } from '../../services/ui-design-filename-transformer.service';
import { WireframeNodeManagementService } from '../../services/wireframe-node-management.service';
import { UIDesignIntroService, IntroMessageState } from '../../services/ui-design-intro.service';
import { CodeGenerationIntroService } from '../../services/code-generation-intro.service';

import { SequentialRegenerationService } from '../../services/sequential-regeneration.service';
import { FileOpeningService } from '../../services/file-opening.service';

import { RegenerationIntegrationService } from '../../services/regeneration-integration.service';
import { GenerationResult } from '../generation-accordion/generation-accordion.component';

import { CodeViewerComponent } from '../code-viewer/code-viewer.component';
import { LoadingAnimationComponent } from './loading-animation/loading-animation.component';
import { LayoutIdentifiedAnimationComponent } from './layout-animation/layout-identified-animation.component';
import { AnalyzingLayoutAnimationComponent } from '../analyzing-layout-animation/analyzing-layout-animation.component';
import { AnalyzingDesignTokensAnimationComponent } from '../analyzing-design-tokens-animation/analyzing-design-tokens-animation.component';
import { MarkdownModule } from 'ngx-markdown';
import { ChatWindowComponent } from '../chat-window/chat-window.component';
import { ErrorPageComponent } from '../error-page/error-page.component';
import { CanvasInfoComponent } from './components/canvas-info/canvas-info.component';
import { MobileFrameComponent, MobilePage } from '../mobile-frame/mobile-frame.component';

import {
  ProgressState,
  StatusType,
  FileData,
  DesignTokensData,
  ProjectInfo,
} from '../../models/polling-response.interface';
import {
  DesignTokenEditState,
  DesignTokenLoadingState
} from '../../models/design-tokens.model';

import JSZip from 'jszip';
import {
  ALL_DESIGN_TOKENS,
  isValidHexColor as validateHexColor,
} from '../../data/design-tokens.data';
import { environment } from '../../../../environments/environment';

type IconStatus = 'default' | 'active' | 'disable';

export interface UIDesignAPIResponse {
  pageName: string;
  content: string;
}

export interface WireframeAPIResponse {
  fileName: string;
  content: string;
}

export interface UIDesignPageData {
  fileName: string;
  content: string;
}

export interface PreviewTabState {
  isVisible: boolean;
  isEnabled: boolean;
  isLoading: boolean;
  hasError: boolean;
  errorMessage?: string;
  loadingMessage?: string;
  tooltipMessage: string;
}

export interface CodeTabState {
  isVisible: boolean;
  isEnabled: boolean;
  isLoading: boolean;
  hasError: boolean;
  errorMessage?: string;
  loadingMessage?: string;
  tooltipMessage: string;
}

export enum PreviewTabStatus {
  HIDDEN = 'hidden',
  DISABLED = 'disabled',
  LOADING = 'loading',
  ENABLED = 'enabled',
  ERROR = 'error'
}

interface DesignToken {
  id: string;
  name: string;
  value: string;
  type: 'color' | 'typography' | 'spacing' | 'size';
  category: string;
  editable: boolean;
}

@Component({
  selector: 'app-code-window',
  standalone: true,
  imports: [
    CommonModule,
    SplitScreenComponent,
    LeftPanelComponent,
    RightPanelComponent,
    IconsComponent,
    CodeViewerComponent,
    LoadingAnimationComponent,
    LayoutIdentifiedAnimationComponent,
    AnalyzingLayoutAnimationComponent,
    AnalyzingDesignTokensAnimationComponent,
    ChatWindowComponent,
    ErrorPageComponent,
    MarkdownModule,
    CanvasInfoComponent,
    MobileFrameComponent,
    SafeSrcdocDirective,
  ],
  templateUrl: './code-window.component.html',
  styleUrls: ['./code-window.component.scss', './artifacts-view.scss', './code-viewer.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CodeWindowComponent implements OnInit, AfterViewInit, OnDestroy {
  private destroy$ = new Subject<void>();

  private files$ = new BehaviorSubject<FileModel[]>([]);
  private isResizing$ = new BehaviorSubject<boolean>(false);
  private currentView$ = new BehaviorSubject<
    'loading' | 'editor' | 'preview' | 'overview' | 'logs' | 'artifacts'
  >('preview');
  private isPreviewLoading$ = new BehaviorSubject<boolean>(false);
  private previewIcon$ = new BehaviorSubject<string>('bi-code-slash');
  private previewError$ = new BehaviorSubject<boolean>(false);
  private errorDescription$ = new BehaviorSubject<string>('Please try again later.');
  private errorTerminalOutput$ = new BehaviorSubject<string>('');
  private currentTheme$ = new BehaviorSubject<'light' | 'dark'>('light');

  public isRegenerationInProgress$ = new BehaviorSubject<boolean>(false);
  private regenerationStartTime: number = 0;

  private isRegenerationCallInProgress = false;

  private currentCheckpointSession = signal<string | null>(null);
  private checkpointProcessedEvents = signal<number>(0);

  private regenerationTimeoutTimer: number | null = null;
  private readonly REGENERATION_TIMEOUT_MS = 1800000;

  private isCodeGenerationLoading$ = new BehaviorSubject<boolean>(false);

  codeRegenerationProgressDescription: string = '';

  private lastUserRequest: string = '';

  private generationVersionCounter: number = 0;
  private hasAddedInitialGenerationAccordion: boolean = false;

  private deployedUrl$ = new BehaviorSubject<string | null>('');
  private isPanelCollapsed$ = new BehaviorSubject<boolean>(false);
  private isLoading$ = new BehaviorSubject<boolean>(true);
  private isHistoryActive$ = new BehaviorSubject<boolean>(false);
  private isCodeActive$ = new BehaviorSubject<boolean>(false);
  private isPreviewActive$ = new BehaviorSubject<boolean>(true);

  private isArtifactsActive$ = new BehaviorSubject<boolean>(false);
  private isLeftPanelCollapsed$ = new BehaviorSubject<boolean>(false);
  private minWidth$ = new BehaviorSubject<string>('300px');
  private isExperienceStudioModalOpen$ = new BehaviorSubject<boolean>(false);
  private checkSessionStorageInterval: any;
  private shouldHideProjectName$ = new BehaviorSubject<boolean>(false);

  // ENHANCEMENT: Download loading state for server-side download with JSZip fallback
  private isDownloadLoading$ = new BehaviorSubject<boolean>(false);

  // ENHANCEMENT: Clone in VS Code loading state
  private isCloneLoading$ = new BehaviorSubject<boolean>(false);

  private isUIDesignMode$ = new BehaviorSubject<boolean>(false);
  private uiDesignNodes$ = new BehaviorSubject<UIDesignNode[]>([]);
  private selectedUIDesignNode$ = new BehaviorSubject<UIDesignNode | null>(null);
  private isUIDesignFullScreenOpen$ = new BehaviorSubject<boolean>(false);
  private uiDesignViewMode$ = new BehaviorSubject<'mobile' | 'web'>('mobile');
  private isUIDesignModalFullScreen$ = new BehaviorSubject<boolean>(false);
  private isUIDesignCodeViewerOpen$ = new BehaviorSubject<boolean>(false);

  private isUIDesignGenerating$ = new BehaviorSubject<boolean>(false);
  private uiDesignError$ = new BehaviorSubject<string | null>(null);
  private uiDesignApiInProgress$ = new BehaviorSubject<boolean>(false);
  private isWireframeGenerationComplete$ = new BehaviorSubject<boolean>(false);

  private isUIDesignRegenerating$ = new BehaviorSubject<boolean>(false);
  private uiDesignLoadingNodes$ = new BehaviorSubject<UIDesignNode[]>([]);

  private showCanvasTooltip$ = new BehaviorSubject<boolean>(true);
  private isEditingUIDesign$ = new BehaviorSubject<boolean>(false);

  private introMessageState$ = new BehaviorSubject<IntroMessageState>({
    isLoading: false,
    text: '',
    isTyping: false,
    hasError: false,
    shouldReplaceText: false,
    introAPICompleted: false,
    mainAPIInProgress: false,
    showLoadingIndicator: false,
    loadingPhase: 'intro',
    messageType: 'generation',
  });

  private activeAIMessageIds = new Set<string>();
  private currentActiveMessageId: string | null = null;

  private regenerationSessionCounter = 0;
  private activeRegenerationSessions = new Map<
    string,
    {
      aiMessageId: string;
      timestamp: number;
      isActive: boolean;
      sessionId?: string;
      messageId?: string;
      prompt?: string;
      selectedNodes?: MultiSelectedNodeData[];
    }
  >();

  private showUIDesignOverviewTab$ = new BehaviorSubject<boolean>(false);
  private uiDesignPages$ = new BehaviorSubject<MobilePage[]>([]);
  private currentUIDesignPageIndex$ = new BehaviorSubject<number>(0);

  private isUIDesignLoading$ = new BehaviorSubject<boolean>(false);

  private useSSE = true;
  private sseSubscription: Subscription | null = null;
  private sseDataProcessorSubscription: Subscription | null = null;

  readonly shouldHideProjectName = this.shouldHideProjectName$.asObservable();
  readonly isUIDesignMode = this.isUIDesignMode$.asObservable();
  readonly uiDesignNodes = this.uiDesignNodes$.asObservable();
  readonly selectedUIDesignNode = this.selectedUIDesignNode$.asObservable();
  readonly isUIDesignFullScreenOpen = this.isUIDesignFullScreenOpen$.asObservable();
  readonly uiDesignViewMode = this.uiDesignViewMode$.asObservable();
  readonly isUIDesignModalFullScreen = this.isUIDesignModalFullScreen$.asObservable();
  readonly isUIDesignCodeViewerOpen = this.isUIDesignCodeViewerOpen$.asObservable();

  readonly isUIDesignGenerating = this.isUIDesignGenerating$.asObservable();
  readonly uiDesignError = this.uiDesignError$.asObservable();
  readonly uiDesignApiInProgress = this.uiDesignApiInProgress$.asObservable();
  readonly isWireframeGenerationComplete = this.isWireframeGenerationComplete$.asObservable();
  readonly isUIDesignRegenerating = this.isUIDesignRegenerating$.asObservable();
  readonly uiDesignLoadingNodes = this.uiDesignLoadingNodes$.asObservable();
  readonly isUIDesignLoading = this.isUIDesignLoading$.asObservable();

  readonly showCanvasTooltip = this.showCanvasTooltip$.asObservable();
  readonly isEditingUIDesign = this.isEditingUIDesign$.asObservable();

  readonly introMessageState = this.introMessageState$.asObservable();

  readonly showUIDesignOverviewTab = this.showUIDesignOverviewTab$.asObservable();
  readonly uiDesignPages = this.uiDesignPages$.asObservable();
  readonly currentUIDesignPageIndex = this.currentUIDesignPageIndex$.asObservable();

  private userSelectedTab: boolean = false;

  readonly files = this.files$.asObservable();
  readonly isResizing = this.isResizing$.asObservable();
  readonly currentView = this.currentView$.asObservable();
  readonly isPreviewLoading = this.isPreviewLoading$.asObservable();
  readonly previewIcon = this.previewIcon$.asObservable();
  readonly previewError = this.previewError$.asObservable();
  readonly errorDescription = this.errorDescription$.asObservable();
  readonly errorTerminalOutput = this.errorTerminalOutput$.asObservable();
  readonly currentTheme = this.currentTheme$.asObservable();
  readonly deployedUrl = this.deployedUrl$.asObservable();
  readonly isPanelCollapsed = this.isPanelCollapsed$.asObservable();
  readonly isLoading = this.isLoading$.asObservable();
  readonly isHistoryActive = this.isHistoryActive$.asObservable();
  readonly isCodeActive = this.isCodeActive$.asObservable();
  readonly isPreviewActive = this.isPreviewActive$.asObservable();

  readonly isArtifactsActive = this.isArtifactsActive$.asObservable();
  readonly isLeftPanelCollapsed = this.isLeftPanelCollapsed$.asObservable();
  readonly minWidth = this.minWidth$.asObservable();
  readonly isExperienceStudioModalOpen = this.isExperienceStudioModalOpen$.asObservable();

  projectName: string | null = null;
  isProjectNameLoading: boolean = true;

  isArtifactsTabEnabled: boolean = false;

  artifactsData: any[] = [];

  isArtifactsTabEnabledWithLogs: boolean = false;

  isLogsFooterExpanded: boolean = false;

  readonly tabTransitionInProgress = signal(false);
  readonly currentTabState = signal<{
    activeTab: string;
    isTransitioning: boolean;
    lastError: string | null;
  }>({
    activeTab: 'preview',
    isTransitioning: false,
    lastError: null
  });

  private destroyRef!: DestroyRef;

  private loadedArtifacts: Set<string> = new Set();

  private persistentArtifacts: Map<string, any> = new Map();

  isArtifactsTabVisible: boolean = true;

  hasLayoutAnalyzed: boolean = false;
  hasDesignSystem: boolean = false;
  selectedArtifactFile: any = null;

  isAnalyzingLayout: boolean = true;
  hasLayoutBeenDetected: boolean = false;

  private typewriterTimeouts: { [key: string]: any } = {};
  private artifactTypingSpeed: number = 3; // Ultra-fast 3ms for artifacts content

  artifactTypewriterStates: {
    [key: string]: {
      visibleContent: string;
      isTyping: boolean;
      fullContent: string;
    };
  } = {};

  designSystemData: any = null;
  designTokens: DesignToken[] = [];
  layoutAnalyzedData: any[] = [];

  private designTokenEditState$ = new BehaviorSubject<DesignTokenEditState>({
    isEditMode: false,
    editButtonText: 'Edit',
    hasUnsavedChanges: false,
    originalValues: new Map<string, string>()
  });

  private designTokenLoadingState$ = new BehaviorSubject<DesignTokenLoadingState>({
    isLoading: false,
    showAnalyzingAnimation: false,
    hasReceivedTokens: false,
    loadingMessage: 'Analyzing Design Tokens...'
  });

  readonly designTokenEditState = this.designTokenEditState$.asObservable();
  readonly designTokenLoadingState = this.designTokenLoadingState$.asObservable();

  currentProgressState: string = '';

  lastProgressDescription: string = '';
  pollingStatus: string = 'PENDING';

  currentProgress: ProgressState | null = null;
  currentStatus: StatusType | null = null;
  newProgressDescription: string = '';
  newLogContent: string = '';
  newArtifactData: any = null;
  newFileList: string[] = [];
  newCodeFiles: FileData[] = [];
  newPreviewUrl: string = '';
  isNewPreviewEnabled: boolean = false;

  isPromptBarEnabled: boolean = false;

  private hasNewPollingResponseUrl = false;

  private isIframeReady$ = new BehaviorSubject<boolean>(false);
  private isUrlValidated$ = new BehaviorSubject<boolean>(false);
  private urlValidationError$ = new BehaviorSubject<string>('');
  private isUrlAvailable$ = new BehaviorSubject<boolean>(false);

  previewUrl: string = '';

  currentProjectInfo: ProjectInfo | null = null;

  private autoSwitchToLogsTimer: any;

  layoutExampleImages: string[] = [
    'assets/images/01.png',
    'assets/images/02.png',
    'assets/images/03.png',
    'assets/images/04.png',
    'assets/images/05.png',
    'assets/images/06.png',
    'assets/images/07.png',
    'assets/images/08.png',
  ];

  layoutMapping: { [key: string]: string } = {
    HB: 'Header + Body (No Sidebars, No Footer)',
    HBF: 'Header + Body + Footer (No Sidebars)',
    HLSB: 'Header + Left Sidebar + Body (No Footer)',
    HLSBF: 'Header + Left Sidebar + Body + Footer',
    HBRS: 'Header + Body + Right Sidebar (No Footer)',
    HBRSF: 'Header + Body + Right Sidebar + Footer',
    HLSBRS: 'Header + Left Sidebar + Body + Right Sidebar (No Footer)',
    HLSBRSF: 'Header + Left Sidebar + Body + Right Sidebar + Footer',
  };

  layoutData: string[] = ['HB', 'HBF'];

  isLayoutLoading: boolean = true;

  private detectedLayoutFromPrevMetadata: string | null = null;
  private previousProgressState: string | null = null;
  private shouldShowLayoutArtifact = false;

  public isFailedState = false;
  private previewTabName$ = new BehaviorSubject<string>('Preview');

  private previewTabState$ = new BehaviorSubject<PreviewTabState>({
    isVisible: true,
    isEnabled: false,
    isLoading: false,
    hasError: false,
    tooltipMessage: 'Preview will be available once the application is deployed'
  });
  private previewTabStatus$ = new BehaviorSubject<PreviewTabStatus>(PreviewTabStatus.DISABLED);

  private codeTabState$ = new BehaviorSubject<CodeTabState>({
    isVisible: true,
    isEnabled: false,
    isLoading: false,
    hasError: false,
    tooltipMessage: 'Code will be available once generated'
  });

  selectedImageDataUri: string = '';

  lightMessages: {
    id?: string;
    text: string;
    from: 'user' | 'ai';
    theme: 'light';
    hasSteps?: boolean;
    imageDataUri?: string;
    isError?: boolean;
    errorDetails?: string;
    isErrorExpanded?: boolean;
    showIntroMessage?: boolean;
    originalText?: string;

    showLoadingIndicator?: boolean;
    loadingPhase?: 'intro' | 'main' | 'completed' | 'error';
    mainAPIInProgress?: boolean;

    isTyping?: boolean;
  }[] = [];

  darkMessages: {
    text: string;
    from: 'user' | 'ai';
    theme: 'dark';
  }[] = [];

  lightPrompt: string = '';
  darkPrompt: string = '';

  projectId: string | null = null;
  jobId: string | null = null;
  isPolling = false;
  isCodeGenerationComplete = false;
  isCodeTabEnabled = false;
  isCodeTabPermanentlyEnabled = false; // Flag to track permanent enablement after SEED_PROJECT_INITIALIZED
  isTemplateFilesAvailable = false; // Flag to track when seed project template files are available for viewing
  isPreviewTabEnabled = false;
  isLogsTabEnabled = false;
  image = 'assets/images/history_card.png';
  title = 'Version 1 Application';
  appName: string | null = null;

  hasLogs: boolean = false;
  logMessages: string[] = [];
  formattedLogMessages: any[] = [];
  isStreamingLogs: boolean = false;
  isTypingLog: boolean = false;
  currentLogIndex: number = 0;
  private logStreamTimer: any;

  private processedLogHashes: Set<string> = new Set();
  private lastProgressState: string = '';
  private expandedCodeLogs: Set<string> = new Set();
  private logsTabAutoEnabled: boolean = false;

  loadingMessages = [
    'Analyzing your requirements and design specifications',
    'Identifying key UI/UX patterns from your requirements',
    'Mapping component relationships and dependencies',
    'Generating component architecture and file structure',
    'Planning optimal folder organization for scalability',
    'Creating responsive UI components with modern best practices',
    'Implementing accessibility standards for inclusive design',
    'Building reusable component library for consistency',
    'Implementing interactive elements and event handlers',
    'Adding form validation and user input handling',
    'Optimizing code for performance and maintainability',
    'Implementing lazy loading for improved initial load time',
    'Adding CSS styling and layout configurations',
    'Creating responsive breakpoints for all device sizes',
    'Implementing data flow and state management',
    'Setting up API integration and data fetching logic',
    'Configuring error handling and fallback states',
    'Ensuring cross browser compatibility and responsive design',
    'Implementing animations and transitions for better UX',
    'Optimizing assets and resources for faster loading',
    'Finalizing code with proper documentation and comments',
    'Running code quality checks and optimizations',
    'Preparing preview deployment for your application',
    'Setting up build configuration for optimal performance',
    'Finalizing application bundle for deployment',
  ];

  private transformLoadingMessages(): void {

    this.loadingMessages = this.textTransformationService.transformMessages(this.loadingMessages);
  }

  generatedCode: any;
  editorReady = false;
  private subscription: Subscription | null = null;

  selectedElement: any = null;
  showEditorIcon = false;
  urlSafe?: SafeResourceUrl;

  isElementSelectionMode = false;
  selectedElementHTML: string | null = null;
  selectedElementCSS: string | null = null;
  selectedElementPath: string | null = null;
  selectedElementId: string | null = null;
  selectedElementTagName: string | null = null;

  showPreview: boolean = false;
  previewImage: { url: string; name: string } | null = null;

  @ViewChild(ChatWindowComponent) chatWindow!: ChatWindowComponent;

  @ViewChild(CodeViewerComponent) codeViewer!: CodeViewerComponent;

  @ViewChild('uiDesignCanvas', { static: false }) uiDesignCanvas!: ElementRef;

  @ViewChild('canvasContent', { static: false }) canvasContent!: ElementRef;

  private resizeTimeout: any;
  private canvasResizeObserver?: ResizeObserver;
  private wheelEventListener?: (event: WheelEvent) => void;

  readonly isIframeReady = this.isIframeReady$.asObservable();
  readonly isUrlValidated = this.isUrlValidated$.asObservable();
  readonly urlValidationError = this.urlValidationError$.asObservable();
  readonly isUrlAvailable = this.isUrlAvailable$.asObservable();

  constructor(
    private codeSharingService: CodeSharingService,
    private codeGenerationService: CodeGenerationService,
    private themeService: ThemeService,
    private cdr: ChangeDetectorRef,
    public sanitizer: DomSanitizer,
    private pollingService: PollingService,

    private enhancedSSEService: EnhancedSSEService,
    private sseDataProcessor: SSEDataProcessorService,
    private newPollingResponseProcessor: NewPollingResponseProcessorService,

    private appStateService: AppStateService,
    private ngZone: NgZone,
    private promptService: PromptBarService,
    private promptSubmissionService: PromptSubmissionService,
    private router: Router,
    private userSignatureService: UserSignatureService,
    private textTransformationService: TextTransformationService,
    private stepperStateService: StepperStateService,
    private toastService: ToastService,
    private vscodeExportService: VSCodeExportService,
    private fileTreePersistenceService: FileTreePersistenceService,
    private monacoStateManagementService: MonacoStateManagementService,
    private artifactsService: ArtifactsService,
    private sseConnectionDeduplicationService: SSEConnectionDeduplicationService,
    private codeWindowStateService: CodeWindowStateService,
    private codeWindowChatService: CodeWindowChatService,
    private uiDesignNodeService: UIDesignNodeService,

    private route: ActivatedRoute,
    private uiDesignCanvasService: UIDesignCanvasService,
    private uiDesignViewportService: UIDesignViewportService,
    private generateUIDesignService: GenerateUIDesignService,
    private uiDesignSelectionService: UIDesignSelectionService,
    private uiDesignEditService: UIDesignEditService,
    private uiDesignNodePositioningService: UIDesignNodePositioningService,
    public uiDesignVisualFeedbackService: UIDesignVisualFeedbackService,
    public wireframeGenerationStateService: WireframeGenerationStateService,
    private generationStateService: GenerationStateService,
    private templateLoadingService: TemplateLoadingService,
    private filenameNormalizationService: FilenameNormalizationService,
    private uiDesignFilenameTransformerService: UIDesignFilenameTransformerService,
    private wireframeNodeManagementService: WireframeNodeManagementService,
    private uiDesignIntroService: UIDesignIntroService,
    private codeGenerationIntroService: CodeGenerationIntroService,

    private sequentialRegenerationService: SequentialRegenerationService,
    private regenerationCheckpointService: RegenerationCheckpointService,
    private fileOpeningService: FileOpeningService,
    private regenerationIntegrationService: RegenerationIntegrationService,
    // ENHANCEMENT: HttpClient for server-side download API
    private http: HttpClient
  ) {

    this.destroyRef = inject(DestroyRef);

    this.currentTheme$.next(this.themeService.getCurrentTheme());

    this.setupAutomaticErrorHandling();

    this.transformLoadingMessages();
  }

  navigateToHome(): void {
    try {
      // Step 1: Stop SSE connections first to prevent new data from coming in
      this.cleanupSSEConnections();

      // Step 2: Stop polling and reset processors
      this.pollingService.resetLogs();
      if (this.isPolling) {
        this.pollingService.stopPolling();
        this.isPolling = false;
      }

      // Reset polling response processors to clear cached data
      this.resetPollingProcessors();

      // Step 3: Reset UI state services (canvas, nodes, design)
      this.cleanupUIStateServices();

      // Step 4: Clear editor and file state
      this.cleanupEditorAndFileState();

      // Step 5: Clear chat and communication state
      this.cleanupChatState();

      // Step 6: Reset component-level state
      this.resetComponentState();

      // Step 7: Reset global application state
      this.cleanupGlobalState();

      // Step 8: Clear toast notifications
      this.toastService.clear();

      // Step 9: Show navigation feedback and navigate
      this.toastService.info('Returning to home page');
      this.router.navigate(['/experience']);

    } catch (error) {

      // Still navigate even if cleanup fails
      this.toastService.error('Some cleanup operations failed, but navigation will continue');
      this.router.navigate(['/experience']);
    }
  }

  /**
   * Step 1: Clean up SSE connections to prevent new data from coming in
   */
  private cleanupSSEConnections(): void {
    try {
      // Stop enhanced SSE service monitoring
      if (this.enhancedSSEService.isMonitoring()) {
        this.enhancedSSEService.stopMonitoring();
      }

      // Close all SSE connection deduplication service connections
      this.sseConnectionDeduplicationService.closeAllConnections('Navigation to home');

    } catch (error) {
    }
  }

  /**
   * Step 3: Reset UI state services (canvas, nodes, design)
   */
  private cleanupUIStateServices(): void {
    try {
      // Clear UI design nodes
      this.uiDesignNodeService.clearAll();

      // Reset UI design canvas and viewport
      this.uiDesignCanvasService.clear();
      this.uiDesignCanvasService.resetViewport();

      // Reset code window state service completely
      this.codeWindowStateService.resetComponentState();

    } catch (error) {
    }
  }

  /**
   * Step 4: Clear editor and file state
   */
  private cleanupEditorAndFileState(): void {
    try {
      // Clear Monaco editor state
      this.monacoStateManagementService.reset();

      // Reset file tree persistence
      this.fileTreePersistenceService.reset();
      this.fileTreePersistenceService.clearPersistedState();

    } catch (error) {
    }
  }

  /**
   * Step 5: Clear chat and communication state
   */
  private cleanupChatState(): void {
    try {
      // Clear code window chat service
      this.codeWindowChatService.clearChatHistory();

      // Clear chat window component messages
      if (this.chatWindow) {
        this.chatWindow.clearAllMessages();
      }

      // Reset stepper state
      this.stepperStateService.triggerStepperReset();

    } catch (error) {
    }
  }

  /**
   * Step 7: Reset global application state
   */
  private cleanupGlobalState(): void {
    try {
      // Reset application state
      this.appStateService.resetProjectState();

      // Reset prompt submission service
      this.promptSubmissionService.resetSubmissionState();

      // Reset code sharing service
      this.codeSharingService.resetState();

      // Reset prompt service
      this.promptService.setImage(null);
      this.promptService.setPrompt('');

      // Reset artifacts service
      this.artifactsService.resetState();

      // Clear session storage and local storage data
      this.clearPersistedStorageData();

    } catch (error) {
    }
  }

  /**
   * Reset polling response processors to clear cached data
   */
  private resetPollingProcessors(): void {
    try {
      // Reset new polling response processor (don't preserve artifacts)
      this.newPollingResponseProcessor.reset(false);

      // Clear processed artifacts cache
      this.newPollingResponseProcessor.clearProcessedArtifacts(false);

      // Reset SSE data processor if available
      if (this.sseDataProcessor) {
        this.sseDataProcessor.reset();
      }

    } catch (error) {
    }
  }

  /**
   * Clear persisted storage data that might remain between sessions
   */
  private clearPersistedStorageData(): void {
    try {
      // Clear session storage keys related to code window state
      const sessionStorageKeys = [
        'codeWindowState',
        'artifactsData',
        'designSystemData',
        'layoutAnalyzedData',
        'projectInfo',
        'previewUrl',
        'deployedUrl',
        'codeFiles',
        'fileTree',
        'monacoState',
        'editorState',
        'uiDesignNodes',
        'canvasState'
      ];

      sessionStorageKeys.forEach(key => {
        try {
          sessionStorage.removeItem(key);
        } catch (error) {
          // Ignore individual key removal errors
        }
      });

      // Clear local storage keys that might persist code window data
      const localStorageKeys = [
        'codeWindowPreferences',
        'artifactsPreferences',
        'designTokens',
        'layoutPreferences'
      ];

      localStorageKeys.forEach(key => {
        try {
          localStorage.removeItem(key);
        } catch (error) {
          // Ignore individual key removal errors
        }
      });

    } catch (error) {
    }
  }

  private resetComponentState(): void {

    this.files$.next([]);
    this.isCodeGenerationComplete = false;
    this.isTemplateFilesAvailable = false; // Reset template files availability
    this.isPreviewLoading$.next(true);
    this.previewError$.next(false);
    this.layoutData = [];
    this.generatedCode = null;
    this.editorReady = false;

    this.logMessages = [];
    this.formattedLogMessages = [];
    this.hasLogs = false;
    this.isStreamingLogs = false;
    this.isTypingLog = false;
    this.currentLogIndex = 0;

    this.processedLogHashes.clear();
    this.lastProgressState = '';
    this.expandedCodeLogs.clear();
    this.logsTabAutoEnabled = false;

    this.selectedImageDataUri = '';
    this.currentProgressState = '';
    this.lastProgressDescription = '';
    this.pollingStatus = 'PENDING';
    this.userSelectedTab = false;
    this.isResizing$.next(false);
    this.isPanelCollapsed$.next(false);

    this.isPromptBarEnabled = false;
    this.isLoading$.next(true);
    this.isHistoryActive$.next(false);
    this.isLeftPanelCollapsed$.next(false);
    this.isExperienceStudioModalOpen$.next(false);

    this.isPreviewActive$.next(true);
    this.isCodeActive$.next(false);

    this.isArtifactsActive$.next(false);
    this.isPreviewTabEnabled = true;
    this.isLogsTabEnabled = true;
    this.isCodeTabEnabled = false;
    this.isCodeTabPermanentlyEnabled = false; // Reset permanent enablement for new project
    this.isArtifactsTabEnabled = false;
    this.isArtifactsTabEnabledWithLogs = false;

    // Reset artifacts data completely (not with persistence)
    this.resetArtifactsCompletely();
    this.selectedArtifactFile = null;
    this.designSystemData = null;
    this.designTokens = [];

    // Reset project name display
    this.projectName = null;
    this.isProjectNameLoading = true;
    this.shouldHideProjectName$.next(false);

    // Reset preview state completely
    this.resetPreviewStateCompletely();

    // Reset layout analysis state
    this.isAnalyzingLayout = true;
    this.hasLayoutBeenDetected = false;
    this.layoutAnalyzedData = [];
    this.detectedLayoutFromPrevMetadata = null;
    this.shouldShowLayoutArtifact = false;

    // Reset design system state
    this.hasDesignSystem = false;
    this.hasLayoutAnalyzed = false;

    // Reset design token states
    this.designTokenEditState$.next({
      isEditMode: false,
      editButtonText: 'Edit',
      hasUnsavedChanges: false,
      originalValues: new Map<string, string>()
    });

    this.designTokenLoadingState$.next({
      isLoading: false,
      showAnalyzingAnimation: false,
      hasReceivedTokens: false,
      loadingMessage: 'Analyzing Design Tokens...'
    });

    // Reset preview tab states
    this.previewTabState$.next({
      isVisible: true,
      isEnabled: false,
      isLoading: false,
      hasError: false,
      tooltipMessage: 'Preview will be available once the application is deployed'
    });

    this.previewTabName$.next('Preview');
    this.isFailedState = false;

    // Reset typewriter states
    this.artifactTypewriterStates = {};
    Object.keys(this.typewriterTimeouts).forEach(key => {
      clearTimeout(this.typewriterTimeouts[key]);
    });
    this.typewriterTimeouts = {};

    this.isAnalyzingLayout = true;
    this.hasLayoutBeenDetected = false;

    this.detectedLayoutFromPrevMetadata = null;
    this.previousProgressState = null;
    this.shouldShowLayoutArtifact = false;

    this.isFailedState = false;
    this.previewTabName$.next('Preview');

    this.setPreviewTabDisabled('Preview will be available once the application is deployed');

    this.setCodeTabDisabled('Code will be available once generated');

    this.hasNewPollingResponseUrl = false;

    this.currentView$.next('preview');

    this.lightMessages = [];
    this.darkMessages = [];
    this.lightPrompt = '';
    this.darkPrompt = '';

    this.selectedImageDataUri = '';

    this.previewError$.next(false);
    this.errorDescription$.next(
      'Even the best systems have bad days sometimes. Please try again later.'
    );
    this.errorTerminalOutput$.next('');

    this.projectId = null;
    this.jobId = null;
    this.appName = null;
    this.projectName = null;
    this.isProjectNameLoading = true;

    this.selectedElement = null;
    this.showEditorIcon = false;
    this.isElementSelectionMode = false;
    this.selectedElementHTML = null;
    this.selectedElementCSS = null;
    this.selectedElementPath = null;
    this.selectedElementId = null;
    this.selectedElementTagName = null;

    if (this.autoSwitchToLogsTimer) {
      clearTimeout(this.autoSwitchToLogsTimer);
      this.autoSwitchToLogsTimer = null;
    }

    if (this.logStreamTimer) {
      clearInterval(this.logStreamTimer);
      this.logStreamTimer = null;
    }

    if (this.designTokensUpdateTimer) {
      clearTimeout(this.designTokensUpdateTimer);
      this.designTokensUpdateTimer = null;
    }
  }

  openPreviewInNewTab(): void {
    const currentUrl = this.deployedUrl$.value;
    if (currentUrl) {
      window.open(currentUrl, '_blank');
      this.toastService.info('Opening preview in new tab');
    } else {
      this.toastService.warning('Preview URL is not available yet');
    }
  }

  debugUrlState(): void {
  }

  showImagePreview(imageUrl: string, imageName: string = 'Image'): void {
    this.previewImage = { url: imageUrl, name: imageName };
    this.showPreview = true;
  }

  closeImagePreview(): void {
    this.showPreview = false;
    this.previewImage = null;
  }

  private processArtifactData(artifactData: any): void {
    if (!artifactData) {
      return;
    }

    switch (artifactData.type) {
      case 'readme':
        this.processProjectOverviewArtifact(artifactData);
        break;
      case 'layout':
        this.processLayoutAnalyzedArtifact(artifactData);

        // ENHANCED: Store layout artifact persistently to preserve during FAILED events
        this.storePersistentArtifact('layout_analyzed', artifactData);
        break;
      case 'design-tokens':
        this.processDesignSystemArtifact(artifactData);

        // ENHANCED: Store design system artifact persistently to preserve during FAILED events
        this.storePersistentArtifact('design_system', artifactData);
        break;
      default:
    }

  }

  private storePersistentArtifact(key: string, artifactData: any): void {
    console.log('🛡️ Storing persistent artifact:', key);
    this.persistentArtifacts.set(key, {
      ...artifactData,
      timestamp: Date.now(),
      persistent: true
    });
    console.log('✅ Persistent artifact stored successfully:', key);
    console.log('📊 Total persistent artifacts:', this.persistentArtifacts.size);
  }

  /**
   * Reset artifacts data completely without restoring persistent artifacts
   * Used when navigating back to home to ensure clean state
   */
  private resetArtifactsCompletely(): void {
    // Clear all artifacts data
    this.artifactsData = [];
    this.loadedArtifacts.clear();
    this.hasLayoutAnalyzed = false;
    this.hasDesignSystem = false;

    // Clear persistent artifacts map completely
    this.persistentArtifacts.clear();

    // Reset artifact-related flags
    this.isArtifactsTabVisible = true;
    this.selectedArtifactFile = null;

    // Clear layout analysis data
    this.layoutAnalyzedData = [];

    // Clear design system data
    this.designSystemData = null;
    this.designTokens = [];
  }

  /**
   * Reset preview state completely including URLs and iframe state
   * Used when navigating back to home to ensure clean preview state
   */
  private resetPreviewStateCompletely(): void {
    // Reset preview URLs
    this.deployedUrl$.next(null);
    this.urlSafe = undefined;
    this.previewUrl = '';
    this.newPreviewUrl = '';

    // Reset preview flags
    this.hasNewPollingResponseUrl = false;
    this.isNewPreviewEnabled = false;

    // Reset preview loading and error states
    this.isPreviewLoading$.next(true);
    this.previewError$.next(false);
    this.errorDescription$.next('Please try again later.');
    this.errorTerminalOutput$.next('');

    // Reset preview icon
    this.previewIcon$.next('bi-code-slash');

    // Reset preview tab states
    this.isPreviewTabEnabled = true;

    // Reset iframe validation states
    this.isIframeReady$.next(false);
    this.isUrlValidated$.next(false);
    this.isUrlAvailable$.next(false);
    this.urlValidationError$.next('');
  }

  private resetArtifactsWithPersistence(): void {
    console.log('🔄 Resetting artifacts with persistence - preserving layout data');

    // Clear current artifacts data but preserve persistent artifacts Map
    this.artifactsData = [];
    this.loadedArtifacts.clear();
    this.hasLayoutAnalyzed = false;
    this.hasDesignSystem = false;

    // ENHANCED: Always restore persistent artifacts to maintain layout data
    // This ensures layout analyzed data remains available even during error scenarios
    if (this.persistentArtifacts.size > 0) {
      console.log('🛡️ Restoring persistent artifacts to preserve layout data');
      this.restorePersistentArtifacts();
    } else {
      console.warn('⚠️ No persistent artifacts available to restore');
    }
  }

  private initializePersistentArtifacts(): void {

    if (!this.persistentArtifacts) {
      this.persistentArtifacts = new Map();
    }

  }



  private restorePersistentArtifacts(): void {
    console.log('🔄 Restoring persistent artifacts from storage');
    console.log('📊 Persistent artifacts count:', this.persistentArtifacts.size);

    for (const [key, artifactData] of this.persistentArtifacts.entries()) {
      console.log('🛡️ Restoring persistent artifact:', key);

      switch (key) {
        case 'layout_analyzed':
          this.processLayoutAnalyzedArtifact(artifactData);
          console.log('✅ Layout analyzed artifact restored');
          break;
        case 'design_system':
          // ENHANCED: Add support for design system artifact restoration
          if (artifactData && typeof artifactData === 'object') {
            this.designSystemData = artifactData;
            this.hasDesignSystem = true;
            console.log('✅ Design system artifact restored');
          }
          break;
        default:
          console.log('⚠️ Unknown persistent artifact type:', key);
      }
    }

    console.log('✅ Persistent artifacts restoration completed');
  }

  private enableArtifactsTabIfNeeded(): void {

    this.removeDuplicateArtifacts();

    const hasArtifacts = this.artifactsData.length > 0;
    const hasLogsAvailable = this.hasLogs || this.formattedLogMessages.length > 0;

    this.addLogsFileEntry(hasLogsAvailable);

    this.isArtifactsTabEnabled = hasArtifacts || hasLogsAvailable;
    this.isArtifactsTabEnabledWithLogs = hasArtifacts || hasLogsAvailable;

    if (hasLogsAvailable && !hasArtifacts) {
    }

    this.ensureLogsFileAtBottom();

    this.cdr.detectChanges();
  }

  private removeDuplicateArtifacts(): void {
    const seen = new Set<string>();
    const originalLength = this.artifactsData.length;

    this.artifactsData = this.artifactsData.filter(artifact => {
      if (seen.has(artifact.name)) {
        return false;
      }
      seen.add(artifact.name);
      return true;
    });

    if (this.artifactsData.length !== originalLength) {

      this.ensureLogsFileAtBottom();

      this.cdr.detectChanges();
    }
  }

  private addLogsFileEntry(hasLogsAvailable: boolean): void {
    const logsFileName = 'Application Logs';
    const existingLogsIndex = this.artifactsData.findIndex(artifact => artifact.name === logsFileName);

    if (existingLogsIndex !== -1) {
      this.artifactsData.splice(existingLogsIndex, 1);

      if (this.selectedArtifactFile && this.selectedArtifactFile.name === logsFileName) {
        this.selectedArtifactFile = this.artifactsData.length > 0 ? this.artifactsData[0] : null;
      }
    }

    if (hasLogsAvailable) {
    }
  }

  private setupAutomaticErrorHandling(): void {

    this.previewError$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(hasError => {
        if (hasError) {
          this.handleAutomaticErrorTabSwitch();
        }
      });

    this.enhancedSSEService.connectionError$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((error: any) => {
        if (error) {
          this.previewError$.next(true);
          // ENHANCED: Extract proper error message from SSE data if available
          const errorMessage = this.extractErrorMessageFromSSEData(error) ||
                              error.message ||
                              'An error occurred during SSE connection';
          this.errorDescription$.next(errorMessage);
        }
      });
  }

  /**
   * ENHANCED: Extract error message from SSE event data
   * Handles different error message formats and provides fallback
   * CRITICAL: Prioritizes data.log field as specified in requirements
   */
  private extractErrorMessageFromSSEData(errorData: any): string | null {
    if (!errorData) return null;

    // CRITICAL: First try to extract from data.log field as specified in requirements
    if (errorData.log && typeof errorData.log === 'string') {
      try {
        // Try to parse JSON log
        const logData = JSON.parse(errorData.log);
        if (logData.message) {
          return logData.message;
        }
        return errorData.log;
      } catch {
        // Return raw log if not JSON
        return errorData.log;
      }
    }

    // Try to extract from errorMessage field as fallback
    if (errorData.errorMessage && typeof errorData.errorMessage === 'string') {
      return errorData.errorMessage.trim();
    }

    // Try to extract from progress_description field as fallback
    if (errorData.progress_description && typeof errorData.progress_description === 'string') {
      return errorData.progress_description.trim();
    }

    return null;
  }

  private handleAutomaticErrorTabSwitch(): void {

    this.tabTransitionInProgress.set(true);

    this.currentTabState.set({
      activeTab: 'preview',
      isTransitioning: true,
      lastError: this.errorDescription$.value
    });

    this.ngZone.run(() => {
      this.currentView$.next('preview');
      this.isPreviewActive$.next(true);
      this.isCodeActive$.next(false);
      this.isArtifactsActive$.next(false);

      setTimeout(() => {
        this.tabTransitionInProgress.set(false);
        this.currentTabState.update(state => ({
          ...state,
          isTransitioning: false
        }));
        this.cdr.detectChanges();
      }, 200);
    });
  }

  /**
   * ENHANCEMENT: Automatic tab switching when code files are received
   * Switches to code tab when code files are available and user hasn't manually selected a tab
   */
  private handleAutomaticCodeTabSwitch(): void {
    // Don't auto-switch if user has manually selected a tab
    if (this.userSelectedTab) {
      return;
    }

    // Don't switch if a tab transition is already in progress
    if (this.tabTransitionInProgress()) {
      return;
    }

    // Don't switch if we're in UI Design mode
    if (this.isUIDesignMode$.value) {
      return;
    }

    // Start tab transition using Angular Signals
    this.tabTransitionInProgress.set(true);

    this.currentTabState.set({
      activeTab: 'code',
      isTransitioning: true,
      lastError: null
    });

    this.ngZone.run(() => {
      this.currentView$.next('editor');
      this.isCodeActive$.next(true);
      this.isPreviewActive$.next(false);
      this.isArtifactsActive$.next(false);

      setTimeout(() => {
        this.tabTransitionInProgress.set(false);
        this.currentTabState.update(state => ({
          ...state,
          isTransitioning: false
        }));
        this.cdr.detectChanges();
      }, 200);
    });
  }

  /**
   * ENHANCEMENT: Automatic tab switching when preview URL is received
   * Switches to preview tab when deployed preview URL is available and user hasn't manually selected a tab
   */
  private handleAutomaticPreviewTabSwitch(): void {
    // Don't auto-switch if user has manually selected a tab
    if (this.userSelectedTab) {
      return;
    }

    // Don't switch if a tab transition is already in progress
    if (this.tabTransitionInProgress()) {
      return;
    }

    // Don't switch if we're in UI Design mode
    if (this.isUIDesignMode$.value) {
      return;
    }

    // Start tab transition using Angular Signals
    this.tabTransitionInProgress.set(true);

    this.currentTabState.set({
      activeTab: 'preview',
      isTransitioning: true,
      lastError: null
    });

    this.ngZone.run(() => {
      this.currentView$.next('preview');
      this.isPreviewActive$.next(true);
      this.isCodeActive$.next(false);
      this.isArtifactsActive$.next(false);

      setTimeout(() => {
        this.tabTransitionInProgress.set(false);
        this.currentTabState.update(state => ({
          ...state,
          isTransitioning: false
        }));
        this.cdr.detectChanges();
      }, 200);
    });
  }

  /**
   * ENHANCEMENT: Automatic tab switching when FAILED status/progress is received
   * Switches to preview tab to show error details when deployment fails
   */
  private handleAutomaticFailedTabSwitch(): void {
    // Don't auto-switch if user has manually selected a tab
    if (this.userSelectedTab) {
      return;
    }

    // Don't switch if a tab transition is already in progress
    if (this.tabTransitionInProgress()) {
      return;
    }

    // Start tab transition using Angular Signals
    this.tabTransitionInProgress.set(true);

    this.currentTabState.set({
      activeTab: 'preview',
      isTransitioning: true,
      lastError: this.errorDescription$.value
    });

    this.ngZone.run(() => {
      this.currentView$.next('preview');
      this.isPreviewActive$.next(true);
      this.isCodeActive$.next(false);
      this.isArtifactsActive$.next(false);

      setTimeout(() => {
        this.tabTransitionInProgress.set(false);
        this.currentTabState.update(state => ({
          ...state,
          isTransitioning: false
        }));
        this.cdr.detectChanges();
      }, 200);
    });
  }

  getFilteredArtifactsData(): any[] {
    return this.artifactsData.filter(artifact => artifact.name !== 'Application Logs');
  }

  private startTabTransition(targetTab: string): void {
    this.tabTransitionInProgress.set(true);
    this.currentTabState.update(state => ({
      ...state,
      isTransitioning: true,
      activeTab: targetTab
    }));
  }

  private completeTabTransition(): void {
    setTimeout(() => {
      this.tabTransitionInProgress.set(false);
      this.currentTabState.update(state => ({
        ...state,
        isTransitioning: false
      }));
      this.cdr.detectChanges();
    }, 150);
  }

  toggleLogsFooter(): void {
    this.isLogsFooterExpanded = !this.isLogsFooterExpanded;
    this.cdr.detectChanges();
  }

  private ensureLogsFileAtBottom(): void {

  }

  getArtifactsTabTooltip(): string {
    const hasArtifacts = this.artifactsData.length > 0;
    const hasLogsAvailable = this.hasLogs || this.formattedLogMessages.length > 0;

    if (hasArtifacts && hasLogsAvailable) {
      return 'View project artefacts and application logs';
    } else if (hasArtifacts) {
      return 'View project artefacts';
    } else if (hasLogsAvailable) {
      return 'View application logs and debugging information';
    } else {
      return 'Artefacts and logs will be available when generated';
    }
  }

  private shouldPreventTabSwitching(tab: string): boolean {

    if (tab === 'artifacts' && this.isArtifactsTabEnabledWithLogs) {
      return false;
    }

    if (this.currentProgressState &&
        (this.currentProgressState.includes('BUILD') ||
         this.currentProgressState.includes('DEPLOY') ||
         this.currentProgressState.includes('COMPLETED'))) {
      return false;
    }

    if (tab === 'preview') {
      return false;
    }

    if (tab === 'overview' && this.isUIDesignMode$.value) {
      return false;
    }

    return true;
  }

  private processProjectOverviewArtifact(artifactData: any): void {
    const artifactName = 'Project Overview';

    const existingIndex = this.artifactsData.findIndex(item => item.name === artifactName);

    const artifactItem = {
      name: artifactName,
      type: 'markdown',
      content: artifactData.content || artifactData.data || 'Project overview content',
    };

    if (existingIndex !== -1) {

      this.artifactsData[existingIndex] = artifactItem;
    } else {

      this.artifactsData.unshift(artifactItem);

      this.ensureLogsFileAtBottom();
    }

    if (this.selectedArtifactFile && this.selectedArtifactFile.name === artifactName) {
      const existingState = this.artifactTypewriterStates[artifactName];

      if (
        !existingState ||
        (!existingState.isTyping && existingState.fullContent !== artifactItem.content)
      ) {
        this.startArtifactTypewriter(artifactName, artifactItem.content);
      }
    }

    this.loadedArtifacts.add(artifactName);

    this.enableArtifactsTabIfNeeded();

    if (!this.selectedArtifactFile) {
      this.selectArtifactFile(artifactItem);
    }
  }

  private preventDuplicateLayoutAnalyzed(): boolean {
    const layoutAnalyzedItems = this.artifactsData.filter(item => item.name === 'Layout Analyzed');
    if (layoutAnalyzedItems.length > 1) {

      const firstIndex = this.artifactsData.findIndex(item => item.name === 'Layout Analyzed');
      this.artifactsData = this.artifactsData.filter(
        (item, index) => item.name !== 'Layout Analyzed' || index === firstIndex
      );

      this.cdr.detectChanges();
      return true;
    }
    return false;
  }

  private processLayoutAnalyzedArtifact(artifactData: any): void {
    const artifactName = 'Layout Analyzed';

    const existingLayoutArtifact = this.artifactsData.find(item => item.name === artifactName);
    if (existingLayoutArtifact) {
      return;
    }

    const currentProgress = this.newPollingResponseProcessor.getCurrentProgress();

    if (currentProgress === 'LAYOUT_ANALYZED' || currentProgress === 'Layout Analyzed') {

      this.detectedLayoutFromPrevMetadata = artifactData.layoutKey || artifactData.layoutCode;
      this.shouldShowLayoutArtifact = false;

      let layoutKey = artifactData.layoutKey || artifactData.layoutCode;
      if (!layoutKey || !this.layoutMapping[layoutKey]) {
        layoutKey = 'HB';
      }
      this.layoutData = [layoutKey];

      return;
    }

    if (!this.detectedLayoutFromPrevMetadata) {
      return;
    }

    const existingIndex = this.artifactsData.findIndex(item => item.name === artifactName);

    let layoutKey = this.detectedLayoutFromPrevMetadata;

    if (!layoutKey || !this.layoutMapping[layoutKey]) {
      layoutKey = 'HB';
    } else {
    }

    this.layoutData = [layoutKey];

    const layoutImageUrl = `assets/images/layout-${layoutKey}.png`;
    const layoutName = this.layoutMapping[layoutKey];

    const artifactItem = {
      name: artifactName,
      type: 'image',
      content: layoutImageUrl,
    };

    if (existingIndex !== -1) {

      this.artifactsData[existingIndex] = artifactItem;
    } else {

      this.artifactsData.push(artifactItem);
    }

    this.ensureLogsFileAtBottom();

    this.layoutAnalyzedData = [
      {
        key: layoutKey,
        name: layoutName,
        imageUrl: layoutImageUrl,
      },
    ];

    this.loadedArtifacts.add(artifactName);
    this.hasLayoutAnalyzed = true;
    this.shouldShowLayoutArtifact = true;

    this.stopLayoutAnalyzing();

    this.enableArtifactsTabIfNeeded();

  }

  private processDesignSystemArtifact(artifactData: any): void {
    const artifactName = 'Design System';

    const existingIndex = this.artifactsData.findIndex(item => item.name === artifactName);

    const artifactItem = {
      name: artifactName,
      type: 'component',
      content: 'Design system tokens and components',
      tokens: artifactData.tokens,
    };

    if (existingIndex !== -1) {

      this.artifactsData[existingIndex] = artifactItem;
    } else {

      this.artifactsData.push(artifactItem);
    }

    this.ensureLogsFileAtBottom();

    this.loadedArtifacts.add(artifactName);
    this.hasDesignSystem = true;

    this.enableArtifactsTabIfNeeded();

    if (artifactData.tokens) {

      if (artifactData.isNewStructure && artifactData.tokens.colors) {
        this.designSystemData = artifactData.tokens;

        this.stopDesignTokenLoading();
      } else {
        this.designSystemData = artifactData.tokens;
      }

      if (artifactData.isFallback) {

        this.startDesignTokenLoading();
      }

      this.initializeDesignTokens();
    } else {

      this.startDesignTokenLoading();
      this.initializeDesignTokens();
    }
  }





  ngOnInit() {

    this.validateAndResetRegenerationState('Component initialization');

    this.sequentialRegenerationService.reset();
    this.regenerationIntegrationService.completeRegeneration();

    this.resetComponentState();

    this.initializePersistentArtifacts();

    this.initializeDefaultPrompt();

    this.initializeAppState();

    if (this.artifactsData.length > 0 && !this.selectedArtifactFile) {
      const designSystemFile = this.artifactsData.find(file => file.name === 'Design System');
      if (designSystemFile) {
        this.selectArtifactFile(designSystemFile);
      } else {
        this.selectArtifactFile(this.artifactsData[0]);
      }
    }

    this.startDesignTokenLoading();
    this.initializeDesignTokens();

    this.subscribeToNewPollingProcessor();

    this.subscribeToSSEDataProcessor();

    this.initializeUIDesignMode();

    this.subscribeToUIDesignService();

    this.subscribeToFileOpeningService();

    this.subscribeToTemplateLoadingService();

    this.setupRegenerationIntegration();

    this.isProjectNameLoading = true;
    setTimeout(() => {
      this.isProjectNameLoading = false;
      this.cdr.detectChanges();
    }, 10000);

  }

  private initializeUIDesignMode(): void {

    this.route.data.pipe(takeUntil(this.destroy$)).subscribe(data => {
      const isUIDesignMode = data['cardType'] === 'Generate UI Design';
      const wasUIDesignMode = this.isUIDesignMode$.value;

      if (wasUIDesignMode && !isUIDesignMode) {
        this.cleanupUIDesignMode();
      }

      this.isUIDesignMode$.next(isUIDesignMode);

      if (isUIDesignMode) {
        this.setupUIDesignMode();
      } else {
      }
    });
  }

  private shouldBlockUIDesignData(dataType: string): boolean {

    const isActualUIDesignWorkflow = this.isUIDesignMode$.value && this.isUIDesignWorkflowActive();

    if (!isActualUIDesignWorkflow) {
      return false;
    }

    const allowedDataTypes = ['logs', 'artifacts', 'status', 'progress'];

    return !allowedDataTypes.includes(dataType);
  }

  private isUIDesignWorkflowActive(): boolean {

    const hasUIDesignCanvas = !!document.querySelector('.ui-design-canvas');
    const hasUIDesignOverview = this.showUIDesignOverviewTab$.value;

    return hasUIDesignCanvas || hasUIDesignOverview;
  }

  private setupUIDesignMode(): void {

    this.isLogsTabEnabled = false;
    this.isArtifactsTabEnabled = false;
    this.isArtifactsTabEnabledWithLogs = false;
    this.isCodeActive$.next(false);

    this.isArtifactsActive$.next(false);

    this.currentView$.next('preview');
    this.isPreviewActive$.next(true);

    this.isPolling = false;
    this.pollingStatus = 'PENDING';
    this.currentProgressState = '';
    this.lastProgressDescription = '';

    this.isCodeGenerationComplete = false;
    this.isTemplateFilesAvailable = false; // Reset template files availability
    this.files$.next([]);
    this.artifactsData = [];

    this.isUIDesignMode$.next(true);
    this.isUIDesignGenerating$.next(false);
    this.uiDesignError$.next(null);
    this.uiDesignApiInProgress$.next(false);
    this.isWireframeGenerationComplete$.next(false);
    this.isUIDesignRegenerating$.next(false);

    this.clearAllLoadingNodes();

    this.wireframeGenerationStateService.reset();

    this.initializeUIDesignChatMessages();

    this.initializeUIDesignSelection();

    this.initializeUIDesignCanvas();

    this.startUIDesignGeneration();

    setTimeout(() => {
      this.setupAutoCanvasCentering();
    }, 200);

  }

  private cleanupUIDesignMode(): void {

    this.isUIDesignMode$.next(false);
    this.isUIDesignGenerating$.next(false);
    this.uiDesignError$.next(null);
    this.uiDesignApiInProgress$.next(false);
    this.isUIDesignRegenerating$.next(false);

    this.clearAllLoadingNodes();

    this.uiDesignIntroService.resetIntroState();

    this.uiDesignNodes$.next([]);
    this.selectedUIDesignNode$.next(null);
    this.isUIDesignFullScreenOpen$.next(false);

    this.uiDesignSelectionService.reset();
    this.showCanvasTooltip$.next(true);
    this.isEditingUIDesign$.next(false);

    this.isLogsTabEnabled = true;
    this.isArtifactsTabEnabled = true;

    this.enableArtifactsTabIfNeeded();

  }

  private initializeUIDesignSelection(): void {

    this.uiDesignSelectionService.reset();
    this.isEditingUIDesign$.next(false);

    this.uiDesignSelectionService.showCanvasTooltip
      .pipe(takeUntil(this.destroy$))
      .subscribe(showTooltip => {
        this.showCanvasTooltip$.next(showTooltip);
      });

    this.uiDesignSelectionService.selectedNodes
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.updateNodeSelectionVisuals();
      });

    this.uiDesignSelectionService.selectedNode
      .pipe(takeUntil(this.destroy$))
      .subscribe(selectedNode => {

        if (selectedNode) {
        }
      });

    this.uiDesignSelectionService.isEditingInProgress
      .pipe(takeUntil(this.destroy$))
      .subscribe(isEditing => {
        this.isEditingUIDesign$.next(isEditing);
      });

  }

  private subscribeToFileOpeningService(): void {

    this.fileOpeningService.tabSwitchRequests
      .pipe(takeUntil(this.destroy$))
      .subscribe(request => {
        this.onTabClick(request.tab);
      });

  }

  /**
   * Subscribe to template loading service for seed project files
   * FEATURE: Load template files into Monaco Editor when available
   * ENHANCED: Comprehensive error handling and logging for SSE-triggered template loading
   */
  private subscribeToTemplateLoadingService(): void {
    // Subscribe to template files from the template loading service
    this.templateLoadingService.templateFiles$
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (templateFiles) => {
          try {
            if (templateFiles && templateFiles.length > 0) {
              console.log('🏗️ Template files received from SSE-triggered loading:', {
                fileCount: templateFiles.length,
                fileNames: templateFiles.map(f => f.name),
                totalSize: templateFiles.reduce((sum, f) => sum + (f.content?.length || 0), 0)
              });

              // Convert template files to the format expected by code viewer
              const fileModels: FileModel[] = templateFiles.map(file => ({
                name: file.name,
                type: 'file' as const,
                content: file.content || '',
                fileName: file.fileName || file.name
              }));

              // Validate file models before loading
              const validFileModels = fileModels.filter(file => {
                const isValid = file.name && file.content !== undefined;
                if (!isValid) {
                  console.warn('⚠️ Invalid file model filtered out:', file);
                }
                return isValid;
              });

              if (validFileModels.length > 0) {
                // ENHANCEMENT: Initialize Monaco Editor with seed project template files
                this.initializeMonacoEditorWithSeedProjectFiles(validFileModels);

              } else {
                console.error('❌ No valid template files to load into Monaco Editor');
                this.setCodeTabError('Template loading failed', 'No valid files received');
              }
            } else {
              console.log('📭 No template files received or empty array');
            }
          } catch (error) {
            console.error('❌ Error processing template files for Monaco Editor:', error);
            this.setCodeTabError('Template processing failed', 'Error processing template files');
          }
        },
        error: (error) => {
          console.error('❌ Error in template loading service subscription:', error);
          this.setCodeTabError('Template loading failed', 'Service subscription error');
        }
      });
  }

  /**
   * ENHANCEMENT: Initialize Monaco Editor with seed project template files
   * Handles proper Monaco Editor initialization when SSE SEED_PROJECT_INITIALIZED + COMPLETED occurs
   * @param validFileModels Template files ready for Monaco Editor
   */
  private initializeMonacoEditorWithSeedProjectFiles(validFileModels: FileModel[]): void {
    try {
      console.log('🎯 Initializing Monaco Editor with seed project template files:', {
        fileCount: validFileModels.length,
        files: validFileModels.map(f => ({ name: f.name, size: f.content?.length || 0 }))
      });

      // Load template files into the code viewer
      this.files$.next(validFileModels);

      // Initialize file tree persistence for template files
      this.fileTreePersistenceService.initializeBaseline(validFileModels);

      // Enable code tab for template viewing (but don't set permanent flag yet)
      this.setCodeTabEnabled('View seed project template');
      this.isCodeTabEnabled = true;

      // ENHANCEMENT: Set template files availability flag for Monaco editor display
      this.isTemplateFilesAvailable = true;

      // Ensure Monaco Editor is properly initialized
      this.ensureMonacoEditorInitialization(validFileModels);

      // Force change detection to ensure UI updates
      this.cdr.detectChanges();

      console.log('✅ Monaco Editor successfully initialized with seed project template files:', {
        validFiles: validFileModels.length,
        fileNames: validFileModels.map(f => f.name)
      });

    } catch (error) {
      console.error('❌ Error initializing Monaco Editor with seed project template files:', error);
      this.setCodeTabError('Monaco Editor initialization failed', 'Error initializing editor with template files');
    }
  }

  /**
   * ENHANCEMENT: Ensure Monaco Editor is properly initialized with template files
   * Verifies Monaco Editor state and handles initialization if needed
   * @param templateFiles Template files to initialize with
   */
  private ensureMonacoEditorInitialization(templateFiles: FileModel[]): void {
    // Verify Monaco Editor state after loading
    setTimeout(() => {
      const currentFiles = this.files$.value;
      console.log('🔍 Monaco Editor state verification:', {
        filesInSubject: currentFiles.length,
        fileNames: currentFiles.map(f => f.name),
        codeViewerReady: !!this.codeViewer,
        templateFilesCount: templateFiles.length
      });

      // If Monaco Editor is ready and has files, try to select the first file
      if (this.codeViewer && currentFiles.length > 0) {
        console.log('🎯 Monaco Editor ready - attempting to select first template file');

        // Find a good default file to open (prefer index.html, App.tsx, main.js, etc.)
        const defaultFile = this.findDefaultTemplateFile(currentFiles);
        if (defaultFile) {
          console.log('📄 Opening default template file:', defaultFile.name);
          // The code viewer will handle opening the file
        }
      } else if (!this.codeViewer) {
        console.warn('⚠️ Monaco Editor (code viewer) not ready yet');
      }
    }, 200); // Increased timeout to ensure Monaco Editor is fully initialized
  }

  /**
   * ENHANCEMENT: Find the best default file to open in Monaco Editor
   * Prioritizes common entry point files for better UX
   * @param files Available template files
   * @returns Default file to open or null
   */
  private findDefaultTemplateFile(files: FileModel[]): FileModel | null {
    // Priority order for default files
    const priorities = [
      'index.html',
      'App.tsx',
      'App.jsx',
      'main.js',
      'index.js',
      'main.ts',
      'index.ts',
      'app.component.ts',
      'app.component.html'
    ];

    // Try to find a priority file
    for (const priority of priorities) {
      const found = files.find(f =>
        f.name.toLowerCase().includes(priority.toLowerCase()) ||
        f.fileName?.toLowerCase().includes(priority.toLowerCase())
      );
      if (found) {
        return found;
      }
    }

    // If no priority file found, return the first file
    return files.length > 0 ? files[0] : null;
  }

  private setupRegenerationIntegration(): void {

    this.regenerationCheckpointService.getCheckpointProgress()
      .pipe(takeUntil(this.destroy$))
      .subscribe(progress => {
        if (progress) {

          this.checkpointProcessedEvents.update(count => count + 1);
        }
      });

    this.regenerationCheckpointService.getSessionStateChanges()
      .pipe(takeUntil(this.destroy$))
      .subscribe(sessionState => {
        if (sessionState) {
        }
      });

    this.regenerationCheckpointService.getEventProcessingErrors()
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
      });

    this.regenerationIntegrationService.fileUpdates$
      .pipe(takeUntil(this.destroy$))
      .subscribe(update => {

        if (update.replaceAll) {

        } else {

        }
      });

    this.regenerationIntegrationService.accordionCreate$
      .pipe(takeUntil(this.destroy$))
      .subscribe(accordionData => {
        this.createRegenerationAccordion(accordionData);
      });

    this.regenerationIntegrationService.uiStateUpdates$
      .pipe(takeUntil(this.destroy$))
      .subscribe(stateUpdate => {
        this.handleRegenerationUIStateUpdate(stateUpdate);
      });

  }

  private subscribeToUIDesignService(): void {

    this.generateUIDesignService.uiDesignResponse$
      .pipe(takeUntil(this.destroy$))
      .subscribe(responseData => {
        if (responseData) {
          this.handleUIDesignResponseForOverview(responseData);
        }
      });

    this.generateUIDesignService.showOverviewTab$.pipe(takeUntil(this.destroy$)).subscribe(show => {
      this.showUIDesignOverviewTab$.next(show);
      if (show) {

        this.currentView$.next('overview');
      }
    });
  }

  private handleUIDesignResponseForOverview(responseData: any): void {

    try {

      const pages: MobilePage[] = [];

      if (responseData.pages && Array.isArray(responseData.pages)) {
        responseData.pages.forEach((page: any) => {
          if (page.fileName && page.content) {
            pages.push({
              fileName: page.fileName,
              content: page.content,
            });
          }
        });
      }

      if (pages.length > 0) {
        this.uiDesignPages$.next(pages);
        this.currentUIDesignPageIndex$.next(0);
        this.showUIDesignOverviewTab$.next(true);
      } else {
      }
    } catch (error) {
    }
  }

  private initializeUIDesignChatMessages(): void {

    this.lightMessages = this.lightMessages.filter(msg => {
      const isTemporaryLoadingMessage =
        msg.text.includes('Generating') || msg.text.includes('Processing');
      const isAIMessage =
        msg.from === 'ai' &&
        msg.id &&
        (msg.id.startsWith('ai-generation-') || msg.id.startsWith('ai-regeneration-'));

      return !isTemporaryLoadingMessage || isAIMessage;
    });

    this.cleanupLegacyEditMessages();

    let userPrompt = '';

    const uiDesignPrompt = this.generateUIDesignService.getPromptData();
    if (uiDesignPrompt && uiDesignPrompt.trim()) {
      userPrompt = uiDesignPrompt;
    }

    if (!userPrompt) {
      const projectState = this.appStateService.getState().project;
      if (projectState.prompt && projectState.prompt.trim()) {
        userPrompt = projectState.prompt;
      }
    }

    if (!userPrompt) {
      try {

        this.promptService.promptData$.pipe(take(1)).subscribe(promptData => {
          if (promptData && typeof promptData === 'object' && 'prompt' in promptData) {
            const prompt = (promptData as any).prompt;
            if (prompt && typeof prompt === 'string' && prompt.trim()) {
              userPrompt = prompt;
            }
          }
        });
      } catch (error) {
      }
    }

    if (userPrompt) {

      const existingUserMessage = this.lightMessages.find(
        msg => msg.from === 'user' && msg.text === userPrompt
      );

      if (!existingUserMessage) {

        const userMessageId = `user-prompt-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

        this.lightMessages.push({
          id: userMessageId,
          text: userPrompt,
          from: 'user',
          theme: 'light',
        });
      } else {
      }

      const aiMessageId = `ai-generation-${Date.now()}`;

      const existingAIMessage = this.lightMessages.find(
        msg =>
          msg.from === 'ai' &&
          msg.id &&
          msg.id.startsWith('ai-generation-') &&
          this.activeAIMessageIds.has(msg.id)
      );

      if (!existingAIMessage) {

        this.lightMessages.push({
          id: aiMessageId,
          text: '',
          from: 'ai',
          theme: 'light',
          showIntroMessage: true,
          showLoadingIndicator: true,
          loadingPhase: 'intro',
          mainAPIInProgress: true,
        });

        this.activeAIMessageIds.add(aiMessageId);
        this.currentActiveMessageId = aiMessageId;

      } else {
        this.currentActiveMessageId = existingAIMessage.id || null;
      }

    } else {

    }

    this.isUIDesignLoading$.next(true);

    this.cdr.detectChanges();
  }

  shouldShowUIDesignLoadingIndicator(): boolean {
    const isUIDesignMode = this.isUIDesignMode$.value;
    const isGenerating = this.isUIDesignGenerating$.value;
    const isRegenerating = this.isUIDesignRegenerating$.value;
    const isApiInProgress = this.uiDesignApiInProgress$.value;

    return isUIDesignMode && (isGenerating || isRegenerating || isApiInProgress);
  }

  shouldShowCodeGenerationLoadingIndicator(): boolean {
    const isUIDesignMode = this.isUIDesignMode$.value;
    const isCodeGenerationLoading = this.isCodeGenerationLoading$.value;

    return !isUIDesignMode && isCodeGenerationLoading;
  }

  private subscribeToRegenerationProgress(): void {

    this.subscription?.add(
      this.newPollingResponseProcessor.progressDescription$
        .pipe(takeUntil(this.destroy$))
        .subscribe((progressDescription: string) => {
          if (progressDescription && typeof progressDescription === 'string' && progressDescription.trim() !== '') {

            this.codeRegenerationProgressDescription = this.getRegenerationProgressText(progressDescription);
            this.cdr.detectChanges();
          }
        })
    );

  }

  private getRegenerationProgressText(rawProgress: string): string {

    return rawProgress || 'Processing your request...';

  }







  private getLastMessage(): any {
    return this.lightMessages.length > 0 ? this.lightMessages[this.lightMessages.length - 1] : null;
  }





  private prepareForRegeneration(): void {
    if (this.projectId && this.jobId) {

      this.enhancedSSEService.prepareForRegeneration(this.projectId, this.jobId);

      const sessionKey = this.regenerationCheckpointService.startCheckpointSession(
        this.projectId,
        this.jobId
      );

      this.currentCheckpointSession.set(sessionKey);
      this.checkpointProcessedEvents.set(0);

    }
  }



  private createRegenerationSuccessResult(codeFiles: FileData[]): void {

    if (!codeFiles || codeFiles.length === 0) {
      return;
    }









    this.addRegenerationGenerationAccordionToChat(codeFiles);

    this.cdr.detectChanges();
  }

  private createRegenerationErrorResult(errorMessage: string): void {

    this.generationVersionCounter++;

    const projectName = this.projectName || 'Untitled Project';

    const generationResult: GenerationResult = {
      type: 'error',
      version: this.generationVersionCounter,
      projectName: projectName,
      errorMessage: errorMessage || 'An unexpected error occurred during regeneration.',
      timestamp: new Date()
    };

    if (this.chatWindow) {
      this.chatWindow.addGenerationResult(generationResult);
    } else {
    }

    this.cdr.detectChanges();
  }







  private isIntroMessage(message: any): boolean {
    if (!message || !message.text) {
      return false;
    }

    const text = message.text.toLowerCase();

    const introPatterns = [
      'preparing your code',
      'getting ready to',
      'setting up code',
      'initializing code',
      'analyzing your request',
      'understanding your changes',
      'reviewing your code',
      'processing your edit request',
      'examining the codebase',
      'evaluating your modifications'
    ];

    const isIntro = introPatterns.some(pattern => text.includes(pattern));

    const completionPatterns = [
      'edit completed',
      'regeneration completed',
      'successfully updated',
      'processing complete'
    ];

    const isCompletion = completionPatterns.some(pattern => text.includes(pattern));

    return isIntro && !isCompletion;
  }

  private addCompletionMessageWithTypewriter(message: string): void {

    const messageId = `ai-completion-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    const completionMessage = {
      id: messageId,
      text: '',
      from: 'ai' as const,
      theme: 'light' as const,
      isTyping: true,
      showLoadingIndicator: false,
      loadingPhase: 'completed' as const,
      mainAPIInProgress: false
    };

    this.lightMessages.push(completionMessage);
    this.cdr.markForCheck();

    this.startTypewriterEffectForMessage(message, messageId);
  }

  private startTypewriterEffectForMessage(fullText: string, messageId: string): void {
    // ULTRA-FAST TYPEWRITER EFFECT - 3ms per character for generate application
    const ULTRA_FAST_TYPING_SPEED = 3;
    let charIndex = 0;

    const typeNextCharacter = () => {
      const targetMessage = this.lightMessages.find(msg => msg.id === messageId);
      if (!targetMessage) {
        return;
      }

      if (charIndex >= fullText.length) {
        targetMessage.isTyping = false;
        this.cdr.markForCheck();
        return;
      }

      targetMessage.text = fullText.substring(0, charIndex + 1);
      charIndex++;

      this.cdr.markForCheck();

      setTimeout(typeNextCharacter, ULTRA_FAST_TYPING_SPEED);
    };

    typeNextCharacter();
  }



  private startUIDesignGeneration(): void {

    this.isUIDesignGenerating$.next(true);
    this.uiDesignError$.next(null);
    this.uiDesignApiInProgress$.next(false);

    this.uiDesignNodes$.next([]);

    this.createLoadingNodes();

    const uiDesignData = this.generateUIDesignService.getUIDesignData();

    if (uiDesignData) {

      this.initiateParallelUIDesignAPICalls(uiDesignData);
    } else {
      this.showUIDesignError('No UI Design data found. Please go back and submit a prompt.');
    }
  }

  private createLoadingNodes(): void {

    const loadingNodes: UIDesignNode[] = [
      {
        id: 'ui-design-loading-node',
        type: 'ui-design',
        data: {
          title: 'Generating UI Design...',
          htmlContent: this.sanitizer.bypassSecurityTrustHtml(''),
          rawContent: '',
          width: 300,
          height: 400,
          isLoading: true,
        },
        position: { x: 0, y: 0 },
        selected: false,
        dragging: false,
        visible: true,
      },
    ];

    this.uiDesignNodes$.next(loadingNodes);
  }

  private initiateParallelUIDesignAPICalls(uiDesignData: any): void {

    this.uiDesignApiInProgress$.next(true);
    this.uiDesignError$.next(null);

    this.wireframeGenerationStateService.startGeneration();

    const apiRequest = this.generateUIDesignService.buildAPIRequest();

    const mainAPICall = this.generateUIDesignService.generateUIDesign(apiRequest);

    const userRequest = uiDesignData.prompt || '';

    this.uiDesignIntroService
      .executeParallelGeneration(userRequest, mainAPICall, this.currentActiveMessageId || undefined)
      .subscribe({
        next: result => {

          if (result.mainAPISuccess) {
            this.handleUIDesignSuccess(result.mainAPIResult);

            this.uiDesignIntroService.completeTextReplacement();
          } else {
            this.handleUIDesignFailure(new Error('Main API failed'));
          }
        },
        error: error => {
          this.handleUIDesignFailure(error);
        },
      });

    this.uiDesignIntroService.introMessageState$
      .pipe(takeUntil(this.destroy$))
      .subscribe((state: IntroMessageState) => {
        this.introMessageState$.next(state);

        if (state.shouldReplaceText && state.targetMessageId && state.text) {
          this.handleChatMessageTextReplacement(state);
        }

        this.cdr.markForCheck();
      });

  }

  private handleChatMessageTextReplacement(state: IntroMessageState): void {

    if (!state.targetMessageId || !state.text) {
      return;
    }

    if (!this.activeAIMessageIds.has(state.targetMessageId)) {
      return;
    }

    const targetMessage = this.lightMessages.find(msg => msg.id === state.targetMessageId);
    if (!targetMessage) {
      return;
    }

    if (!targetMessage.originalText) {
      targetMessage.originalText = targetMessage.text || '';
    }

    targetMessage.text = state.text;
    targetMessage.showLoadingIndicator = state.showLoadingIndicator;
    targetMessage.loadingPhase = state.loadingPhase;
    targetMessage.mainAPIInProgress = state.mainAPIInProgress;

    this.cdr.markForCheck();
  }



  private cleanupRegenerationSession(sessionId: string): void {
    if (this.activeRegenerationSessions.has(sessionId)) {
      const session = this.activeRegenerationSessions.get(sessionId);
      if (session) {

        this.activeAIMessageIds.delete(session.aiMessageId);

        if (this.currentActiveMessageId === session.aiMessageId) {
          this.currentActiveMessageId = null;
        }

        this.activeRegenerationSessions.delete(sessionId);

      }
    }
  }

  private restoreOriginalChatMessageText(): void {
    if (!this.currentActiveMessageId) {
      return;
    }

    const targetMessage = this.lightMessages.find(msg => msg.id === this.currentActiveMessageId);
    if (targetMessage && targetMessage.originalText) {

      targetMessage.text = targetMessage.originalText;
      delete targetMessage.originalText;

      targetMessage.showLoadingIndicator = false;
      targetMessage.loadingPhase = 'completed';
      targetMessage.mainAPIInProgress = false;

      if (this.currentActiveMessageId.startsWith('ai-regeneration-')) {

        const sessionId = this.currentActiveMessageId.replace('ai-regeneration-', '');
        this.cleanupRegenerationSession(sessionId);
      } else {

        this.activeAIMessageIds.delete(this.currentActiveMessageId);
        this.currentActiveMessageId = null;
      }

      this.cdr.markForCheck();
    }
  }



  private handleUIDesignSuccess(response: any): void {

    this.isUIDesignGenerating$.next(false);
    this.uiDesignApiInProgress$.next(false);
    this.uiDesignError$.next(null);
    this.isUIDesignLoading$.next(false);

    this.restoreOriginalChatMessageText();

    try {

      if (this.isUIDesignResponse(response)) {
        this.processUIDesignResponse(response);
      } else {
        this.showUIDesignError('Invalid response format from wireframe generation API');
      }
    } catch (error) {
      this.showUIDesignError('Failed to process wireframe generation response');
    }
  }

  private handleUIDesignFailure(error: any): void {

    this.isUIDesignGenerating$.next(false);
    this.uiDesignApiInProgress$.next(false);
    this.isWireframeGenerationComplete$.next(false);
    this.isUIDesignLoading$.next(false);

    const errorMessage = error?.message || error?.error?.message || 'Wireframe generation failed';
    this.uiDesignError$.next(errorMessage);

    this.wireframeGenerationStateService.setError(errorMessage);

    this.showUIDesignError(errorMessage);
  }

  private showUIDesignError(message: string): void {

    this.clearAllLoadingNodes();

    this.uiDesignSelectionService.setEditingInProgress(false);
    this.isUIDesignRegenerating$.next(false);
    this.isUIDesignLoading$.next(false);

    this.toastService.error(message);

    const errorNode: UIDesignNode = {
      id: 'error-node',
      type: 'ui-design',
      data: {
        title: 'Error',
        htmlContent: this.sanitizer.bypassSecurityTrustHtml(`
          <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; padding: 20px; text-align: center;">
            <div style="font-size: 48px; color: #ef4444; margin-bottom: 16px;">⚠️</div>
            <div style="font-size: 16px; color: #374151; margin-bottom: 8px;">Generation Failed</div>
            <div style="font-size: 14px; color: #6b7280;">${message}</div>
          </div>
        `),
        rawContent: '',
        width: 400,
        height: 250,
        isLoading: false,
      },
      position: { x: -200, y: -125 },
      selected: false,
      dragging: false,
      visible: true,
    };

    this.uiDesignNodes$.next([errorNode]);
  }

  private initializeUIDesignCanvas(): void {

  }

  onUIDesignNodeDoubleClick(node: UIDesignNode): void {
    this.selectedUIDesignNode$.next(node);
    this.isUIDesignFullScreenOpen$.next(true);

    const userApplicationTarget = this.generateUIDesignService.getApplicationTarget();
    this.uiDesignViewMode$.next(userApplicationTarget || 'mobile');

  }

  closeUIDesignFullScreen(): void {
    this.isUIDesignFullScreenOpen$.next(false);
    this.selectedUIDesignNode$.next(null);
  }

  switchUIDesignViewMode(mode: 'mobile' | 'web'): void {
    this.uiDesignViewMode$.next(mode);
  }

  toggleUIDesignModalFullScreen(): void {
    const isCurrentlyFullScreen = this.isUIDesignModalFullScreen$.value;
    this.isUIDesignModalFullScreen$.next(!isCurrentlyFullScreen);
  }

  exitUIDesignModalFullScreen(): void {
    this.isUIDesignModalFullScreen$.next(false);
  }

  openUIDesignInNewTab(): void {
    const selectedNode = this.selectedUIDesignNode$.value;
    if (!selectedNode?.data?.rawContent) {
      return;
    }

    try {

      const newWindow = window.open('', '_blank');

      if (newWindow) {

        newWindow.document.write(selectedNode.data.rawContent);
        newWindow.document.close();

        const title =
          selectedNode.data.displayTitle || selectedNode.data.title || 'UI Design Preview';
        newWindow.document.title = title;

      } else {

        this.openUIDesignWithBlobUrl(
          selectedNode.data.rawContent
        );
      }
    } catch (error) {

      this.openUIDesignWithBlobUrl(
        selectedNode.data.rawContent
      );
    }
  }

  private openUIDesignWithBlobUrl(content: string): void {
    try {

      const blob = new Blob([content], { type: 'text/html' });
      const blobUrl = URL.createObjectURL(blob);

      const newWindow = window.open(blobUrl, '_blank');

      if (newWindow) {

        setTimeout(() => {
          URL.revokeObjectURL(blobUrl);
        }, 1000);

      } else {
      }
    } catch (error) {
    }
  }

  openUIDesignCodeViewer(): void {
    const selectedNode = this.selectedUIDesignNode$.value;
    if (!selectedNode?.data?.rawContent) {
      this.toastService.warning('No code content available to view');
      return;
    }

    this.updateCodeData();

    this.isUIDesignCodeViewerOpen$.next(true);
  }

  copyUIDesignCodeToClipboard(): void {
    const selectedNode = this.selectedUIDesignNode$.value;
    if (!selectedNode?.data?.rawContent) {
      this.toastService.warning('No code content available to copy');
      return;
    }

    try {
      navigator.clipboard.writeText(selectedNode.data.rawContent).then(() => {
        this.toastService.success('Code copied to clipboard');
      }).catch(() => {
        this.toastService.error('Failed to copy code to clipboard');
      });
    } catch (error) {
      this.toastService.error('Failed to copy code to clipboard');
    }
  }

  downloadUIDesignCode(): void {
    const selectedNode = this.selectedUIDesignNode$.value;
    if (!selectedNode?.data?.rawContent) {
      this.toastService.warning('No code content available to download');
      return;
    }

    try {

      const title = selectedNode.data.displayTitle || selectedNode.data.title || 'ui-design';
      const filename = `${title.toLowerCase().replace(/[^a-z0-9]/g, '-')}.html`;

      const blob = new Blob([selectedNode.data.rawContent], { type: 'text/html' });

      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();

      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      this.toastService.success(`Code downloaded as ${filename}`);
    } catch (error) {
      this.toastService.error('Failed to download code');
    }
  }

  private _codeLines: string[] = [];
  private _lastProcessedContent: string = '';

  public codeLineNumbers: number[] = [];
  public highlightedCode: string = '';
  public codeFileName: string = '';

  getCodeFileName(): string {
    return this.codeFileName;
  }

  private updateCodeData(): void {
    const selectedNode = this.selectedUIDesignNode$.value;
    if (!selectedNode?.data?.rawContent) {
      this.codeLineNumbers = [];
      this.highlightedCode = '';
      this.codeFileName = 'ui-design.html';
      return;
    }

    const currentContent = selectedNode.data.rawContent;

    if (this._lastProcessedContent !== currentContent) {
      this._codeLines = currentContent.split('\n');
      this.codeLineNumbers = Array.from({ length: this._codeLines.length }, (_, i) => i + 1);
      this.highlightedCode = this.generateHighlightedCode(currentContent);

      const title = selectedNode.data.displayTitle || selectedNode.data.title || 'ui-design';
      this.codeFileName = `${title.toLowerCase().replace(/[^a-z0-9]/g, '-')}.html`;

      this._lastProcessedContent = currentContent;

    }
  }

  private generateHighlightedCode(content: string): string {
    if (!content) {
      return '';
    }

    let code = content
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');

    // Apply syntax highlighting with optimized regex
    code = code
      // HTML tags
      .replace(/(&lt;\/?)([a-zA-Z][a-zA-Z0-9]*)(.*?)(&gt;)/g,
        '<span class="html-tag">$1</span><span class="html-tag-name">$2</span><span class="html-attributes">$3</span><span class="html-tag">$4</span>')
      // Attributes
      .replace(/(\w+)(=)(&quot;[^&]*&quot;)/g,
        '<span class="html-attr-name">$1</span><span class="html-operator">$2</span><span class="html-attr-value">$3</span>')
      // Comments
      .replace(/(&lt;!--.*?--&gt;)/g, '<span class="html-comment">$1</span>')
      // Doctype
      .replace(/(&lt;!DOCTYPE.*?&gt;)/gi, '<span class="html-doctype">$1</span>');

    return code;
  }

  /**
   * TrackBy function for line numbers optimization
   */
  trackByLineNumber(_index: number, lineNumber: number): number {
    return lineNumber;
  }

  /**
   * Handle code content scroll to sync line numbers
   */
  onCodeScroll(event: Event): void {
    const codeContent = event.target as HTMLElement;
    const lineNumbers = codeContent.parentElement?.querySelector('.line-numbers') as HTMLElement;

    if (lineNumbers) {
      lineNumbers.scrollTop = codeContent.scrollTop;
    }
  }

  /**
   * Clear cached code data when modal closes
   */
  closeUIDesignCodeViewer(): void {
    this.isUIDesignCodeViewerOpen$.next(false);

    // Clear cache for memory optimization
    this._codeLines = [];
    this._lastProcessedContent = '';

    // Clear public properties
    this.codeLineNumbers = [];
    this.highlightedCode = '';
    this.codeFileName = '';

  }

  /**
   * Process UI Design API response and create nodes
   * Handles both formats:
   * - UI Design: [{"pageName": "...", "content": "<html>..."}]
   * - Wireframe: [{"fileName": "Login_Page.html", "content": "<html>..."}]
   */
  processUIDesignResponse(response: string | UIDesignAPIResponse[] | WireframeAPIResponse[]): void {

    // 🧹 CRITICAL FIX: Clear all loading nodes immediately when processing response
    // This handles both initial generation and regeneration loading nodes
    this.clearAllLoadingNodes();

    try {
      let pages: any[] = [];

      // Parse response if it's a string
      if (typeof response === 'string') {
        try {
          pages = JSON.parse(response);
        } catch (parseError) {
          this.showUIDesignError('Failed to parse response data');
          return;
        }
      } else if (Array.isArray(response)) {
        pages = response;
      } else {
        this.showUIDesignError('Invalid response format');
        return;
      }

      // Validate response structure
      if (!Array.isArray(pages) || pages.length === 0) {
        this.showUIDesignError('No pages found in response');
        return;
      }

      // Convert API response to UI Design page data
      const uiDesignPages: UIDesignPageData[] = pages.map((page, index) => {
        let pageName: string;
        let content: string;

        // Handle different response formats
        if ('fileName' in page) {
          // Wireframe format: {"fileName": "Login_Page.html", "content": "..."}
          pageName = this.extractPageNameFromFileName(page.fileName);
          content = page.content;
        } else if ('pageName' in page) {
          // UI Design format: {"pageName": "...", "content": "..."}
          pageName = page.pageName?.trim() || `Page ${index + 1}`;
          content = page.content;
        } else {
          // Fallback for unknown format
          pageName = `Page ${index + 1}`;
          content = page.content || '<html><body><p>No content available</p></body></html>';
        }

        return {
          fileName: pageName,
          content: content || '<html><body><p>No content available</p></body></html>',
        };
      });

      // Create UI Design nodes from pages
      this.createUIDesignNodes(uiDesignPages);
    } catch (error) {
      this.showUIDesignError('Failed to process response data');
    }
  }

  /**
   * Extract clean page name from fileName using UI Design filename transformation
   * ENHANCED: Now uses dedicated UI Design transformer with "Page" suffix
   * Examples:
   * - "login.html" → "Login Page"
   * - "dashboard.html" → "Dashboard Page"
   * - "user-profile.html" → "User Profile Page"
   * - "loginPage.html" → "Login Page Page" (handled by duplicate detection)
   * - "user_settings.html" → "User Settings Page"
   */
  private extractPageNameFromFileName(fileName: string): string {
    if (!fileName) {
      return 'Untitled Page';
    }

    try {
      // ENHANCED: Use the dedicated UI Design filename transformer service
      const transformResult = this.uiDesignFilenameTransformerService.transformFileName(fileName);

      // Return the display title which includes the "Page" suffix
      const displayTitle = transformResult.displayTitle || 'Untitled Page';

      return displayTitle;
    } catch (error) {
      return 'Untitled Page';
    }
  }

  /**
   * Create UI Design nodes from page data and display in canvas
   * Enhanced with robust filename normalization and intelligent node management
   */
  private async createUIDesignNodes(pages: UIDesignPageData[]): Promise<void> {

    try {
      // 🧹 SAFEGUARD: Clear all loading nodes before creating new nodes
      // This ensures no loading nodes persist when new content is created
      this.clearAllLoadingNodes();

      // Get current nodes for intelligent update/create decisions
      const currentNodes = this.uiDesignNodes$.value;

      // Convert pages to wireframe page data format for the node management service
      const wireframePages = pages.map(page => ({
        fileName: page.fileName,
        content: page.content,
        pageName: page.fileName, // Alternative field name for compatibility
      }));

      // Create position calculator function for new nodes
      const positionCalculator = (count: number, _existingNodes: any[]) => {
        const positioningResult =
          this.uiDesignNodePositioningService.calculateInitialGenerationPositions(count);
        return positioningResult.positions;
      };

      // Use the robust node management service to process the regeneration
      const result = await this.wireframeNodeManagementService.processRegenerationResponse(
        wireframePages,
        currentNodes,
        positionCalculator
      );

      // Update the nodes with the processed result
      this.uiDesignNodes$.next(result.updatedNodes);

      // Enable UI Design mode and disable history view
      this.isUIDesignMode$.next(true);
      this.isHistoryActive$.next(false);

      // Calculate optimal viewport position to show all nodes
      if (result.updatedNodes.length > 0) {
        const nodePositions = result.updatedNodes.map(node => node.position);
        const optimalViewport =
          this.uiDesignNodePositioningService.calculateOptimalViewport(nodePositions);

        // Auto-center the viewport to show all nodes with optimal zoom
        setTimeout(() => {
          this.centerCanvasOnNodesWithViewport(optimalViewport);
        }, 100);
      }

      // ===== Trigger Overview Tab =====
      // Convert UIDesignPageData to MobilePage format for overview tab
      const mobilePages: MobilePage[] = pages.map(page => ({
        fileName: page.fileName,
        content: page.content,
      }));

      // Set the UI Design response data in the service to trigger overview tab
      const responseData = {
        pages: mobilePages,
        jobId: 'ui-design-' + Date.now(), // Generate a temporary job ID
        projectId: 'ui-design-project-' + Date.now(), // Generate a temporary project ID
      };

      this.generateUIDesignService.setUIDesignResponse(responseData);

      // Update chat messages with success (enhanced with operation details)
      this.updateUIDesignChatMessagesWithSummary(result.summary);

      // Set nodes created state to show tooltip
      this.uiDesignSelectionService.setNodesCreated(true);

      // Set wireframe generation complete state to enable selection controls
      this.isWireframeGenerationComplete$.next(true);
      this.wireframeGenerationStateService.completeGeneration(result.updatedNodes.length);
    } catch (error) {
      this.showUIDesignError(
        'Failed to create design nodes: ' +
          (error instanceof Error ? error.message : 'Unknown error')
      );
    }
  }

  /**
   * Update chat messages for successful UI Design generation
   * Enhanced with AI message preservation and typewriting effects
   */


  /**
   * Update chat messages with enhanced operation summary
   * Enhanced with AI message preservation
   */
  private updateUIDesignChatMessagesWithSummary(summary: any): void {
    // CRITICAL: DO NOT remove AI messages - only remove specific loading messages
    this.lightMessages = this.lightMessages.filter(msg => {
      const isTemporaryLoadingMessage =
        msg.text.includes('Generating') || msg.text.includes('Processing');
      const isAIMessage =
        msg.from === 'ai' &&
        msg.id &&
        (msg.id.startsWith('ai-generation-') || msg.id.startsWith('ai-regeneration-'));

      // Keep all AI messages, remove only temporary loading messages
      return !isTemporaryLoadingMessage || isAIMessage;
    });

    // Create detailed success message based on operations performed
    let message = 'Wireframe processing completed successfully! ';

    if (summary.created > 0 && summary.updated > 0) {
      message += `Created ${summary.created} new page${summary.created > 1 ? 's' : ''} and updated ${summary.updated} existing page${summary.updated > 1 ? 's' : ''}.`;
    } else if (summary.created > 0) {
      message += `Created ${summary.created} new page${summary.created > 1 ? 's' : ''}.`;
    } else if (summary.updated > 0) {
      message += `Updated ${summary.updated} existing page${summary.updated > 1 ? 's' : ''}.`;
    } else {
      message += `Processed ${summary.totalProcessed} page${summary.totalProcessed > 1 ? 's' : ''}.`;
    }

    message +=
      ' Single-click to select a page for editing, or double-click to view in full-screen mode.';

    // Add success message with typewriting effect
    const messageId = `ai-success-summary-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

    this.lightMessages.push({
      id: messageId,
      text: '', // Start with empty text for typewriting effect
      from: 'ai',
      theme: 'light',
    });

    // Start typewriting effect for the success message
    this.startTypewriterEffectForMessage(message, messageId);

    this.cdr.markForCheck();
  }

  /**
   * Process UI Design response from polling or direct API call
   * This method serves as the main entry point for UI Design response processing
   */
  handleUIDesignResponse(response: any): void {

    // Check if this is a UI Design response
    if (this.isUIDesignResponse(response)) {
      this.processUIDesignResponse(response);
    } else {
    }
  }

  /**
   * Check if response is a valid UI Design or Wireframe response
   */
  private isUIDesignResponse(response: any): boolean {
    // Check for array format
    if (Array.isArray(response)) {
      return response.every(item => {
        if (typeof item !== 'object' || !item.content) {
          return false;
        }

        // Check for UI Design format: {"pageName": "...", "content": "..."}
        const hasPageName = 'pageName' in item;

        // Check for Wireframe format: {"fileName": "...", "content": "..."}
        const hasFileName = 'fileName' in item;

        // Must have either pageName or fileName
        return hasPageName || hasFileName;
      });
    }

    // Check for string format that can be parsed
    if (typeof response === 'string') {
      try {
        const parsed = JSON.parse(response);
        return this.isUIDesignResponse(parsed);
      } catch {
        return false;
      }
    }

    return false;
  }

  /**
   * Check if artifact data contains UI Design response
   */
  private isUIDesignArtifact(artifactData: any): boolean {
    // Check if artifact contains UI Design data
    if (!artifactData || !artifactData.content) {
      return false;
    }

    // Check if the content is a UI Design response
    return this.isUIDesignResponse(artifactData.content);
  }

  /**
   * Handle canvas mouse down event
   */
  onCanvasMouseDown(event: MouseEvent): void {
    if (event.button === 0) {
      // Left mouse button
      this.uiDesignCanvasService.startDragging(event.clientX, event.clientY);
    }
  }

  /**
   * Handle canvas mouse move event
   */
  onCanvasMouseMove(event: MouseEvent): void {
    const viewport = this.uiDesignCanvasService.getViewport();

    if (viewport.isDragging) {
      const deltaX = event.clientX - viewport.lastMouseX;
      const deltaY = event.clientY - viewport.lastMouseY;

      this.uiDesignCanvasService.panCanvas(deltaX, deltaY);
      this.uiDesignCanvasService.updateViewport({
        lastMouseX: event.clientX,
        lastMouseY: event.clientY,
      });
    }
  }

  /**
   * Handle canvas mouse up event
   */
  onCanvasMouseUp(_event: MouseEvent): void {
    this.uiDesignCanvasService.stopDragging();
  }

  /**
   * Handle node selection for editing (enhanced with multi-selection support)
   */
  onUIDesignNodeSelect(node: UIDesignNode, event?: MouseEvent): void {
    if (!this.isUIDesignMode$.value) {
      return;
    }

    // Determine if this is a multi-select operation
    const isMultiSelect = event && (event.ctrlKey || event.metaKey || event.shiftKey);

    // Create enhanced selection data with metadata
    const selectionData: MultiSelectedNodeData = {
      nodeId: node.id,
      fileName: node.data.displayTitle || node.data.title, // Use formatted display title for API
      htmlContent: node.data.htmlContent as string,
      rawContent: node.data.rawContent,
      selectedImages: [], // Initialize empty, can be populated later
      metadata: {
        nodePosition: node.position,
        nodeDimensions: { width: node.data.width, height: node.data.height },
        selectionTimestamp: Date.now(),
      },
    };

    // Update selection service with multi-selection support
    this.uiDesignSelectionService.selectMultipleNodes(
      node.id,
      selectionData,
      isMultiSelect || false
    );

    // Update visual feedback service for enhanced selection visibility
    this.uiDesignVisualFeedbackService.toggleNodeSelection(node.id, isMultiSelect || false);

  }

  /**
   * Update visual selection state for nodes (enhanced for multi-selection)
   * Now only triggers change detection since selection state is managed by the service
   */
  private updateNodeSelectionVisuals(): void {
    // Selection state is now managed entirely by the uiDesignSelectionService
    // and accessed via isNodeSelectedForEditing() method in the template
    // We only need to trigger change detection to update the UI
    this.cdr.markForCheck();

  }

  /**
   * Clear node selection
   */
  onClearNodeSelection(): void {

    this.uiDesignSelectionService.clearSelection();

    // Clear visual feedback service selection
    this.uiDesignVisualFeedbackService.clearSelection();

    // Clear visual selection
    const currentNodes = this.uiDesignNodes$.value;
    const updatedNodes = currentNodes.map(node => ({
      ...node,
      selected: false,
    }));

    this.uiDesignNodes$.next(updatedNodes);
    this.cdr.markForCheck();

  }

  /**
   * Generate unique regeneration session ID
   */
  private generateRegenerationSessionId(): string {
    this.regenerationSessionCounter++;
    return `regen-session-${Date.now()}-${this.regenerationSessionCounter}-${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * ENHANCED: Track regeneration session to prevent cross-updating of messages
   * Maintains isolation between different regeneration cycles
   */








  /**
   * Handle special commands like /intro and /wireframe-generation
   * @param prompt - The user input prompt
   * @returns true if a special command was handled, false otherwise
   */
  private handleSpecialCommands(prompt: string): boolean {
    const trimmedPrompt = prompt.trim().toLowerCase();

    if (trimmedPrompt.startsWith('/intro')) {
      this.handleIntroCommand(prompt);
      return true;
    }

    if (trimmedPrompt.startsWith('/wireframe-generation')) {
      this.handleWireframeGenerationCommand(prompt);
      return true;
    }

    return false;
  }

  /**
   * Handle /intro command - creates intro message with shimmer loading
   */
  private handleIntroCommand(prompt: string): void {
    console.info('🎭 Handling /intro command');

    // Extract the actual request after the command
    const userRequest = prompt.replace(/^\/intro\s*/i, '').trim() || 'Generate an intro message';

    // Add user message to chat
    this.lightMessages.push({
      text: prompt,
      from: 'user',
      theme: 'light'
    });

    // Use the code generation intro service with shimmer loading
    this.codeGenerationIntroService.createAndDisplayIntroCardWithShimmer(
      userRequest,
      [], // No code files for intro command
      this.chatWindow
    ).subscribe({
      next: (messageId) => {
        console.info('✅ Intro command completed successfully:', messageId);
      },
      error: (error) => {
        console.error('❌ Intro command failed:', error);
        this.toastService.error('Failed to generate intro message. Please try again.');
      }
    });
  }

  /**
   * Handle /wireframe-generation command - creates wireframe intro with shimmer loading
   */
  private handleWireframeGenerationCommand(prompt: string): void {
    console.info('🎯 Handling /wireframe-generation command');

    // Extract the actual request after the command
    const userRequest = prompt.replace(/^\/wireframe-generation\s*/i, '').trim() || 'Generate a wireframe';

    // Add user message to chat
    this.lightMessages.push({
      text: prompt,
      from: 'user',
      theme: 'light'
    });

    // Use the UI design intro service with shimmer loading
    this.uiDesignIntroService.createAndDisplayIntroCardWithShimmer(
      userRequest,
      this.chatWindow
    ).subscribe({
      next: (messageId) => {
        console.info('✅ Wireframe generation command completed successfully:', messageId);
      },
      error: (error) => {
        console.error('❌ Wireframe generation command failed:', error);
        this.toastService.error('Failed to generate wireframe intro message. Please try again.');
      }
    });
  }

  /**
   * Handle edit prompt submission for selected nodes (enhanced for multi-selection with loading nodes)
   * Ensures clean message state without legacy messages and shows loading nodes immediately
   */
  onUIDesignEditPrompt(prompt: string): void {
    if (!this.isUIDesignMode$.value) {
      return;
    }

    // Check for special commands
    if (this.handleSpecialCommands(prompt)) {
      return;
    }

    const selectedNodes = this.uiDesignSelectionService.getSelectedNodes();
    if (selectedNodes.length === 0) {
      this.toastService.error('Please select one or more pages to edit');
      return;
    }

    // Clean up any legacy messages before starting edit process
    this.cleanupLegacyEditMessages();

    // Create and show loading nodes immediately
    this.createLoadingNodesForRegeneration(selectedNodes);

    // Set editing and regeneration state
    this.uiDesignSelectionService.setEditingInProgress(true);
    this.isUIDesignRegenerating$.next(true);
    this.isUIDesignLoading$.next(true);

    // ENHANCED: Generate unique session ID for this regeneration
    const sessionId = this.generateRegenerationSessionId();

    // FIXED: User message is now handled by handleUserMessageData method to prevent duplicates
    // No need to add user message here as it's already added by the chat-window component

    // Generate unique AI message ID for this specific regeneration
    const aiMessageId = `ai-regeneration-${sessionId}`;

    // ENHANCED: Always create a new AI message for each regeneration (no duplicate prevention)
    // This ensures each regeneration gets its own separate message thread
    this.lightMessages.push({
      id: aiMessageId,
      text: '', // Start with empty text - will be populated by intro API
      from: 'ai' as const,
      theme: 'light' as const,
      showIntroMessage: true,
      showLoadingIndicator: true,
      loadingPhase: 'intro',
      mainAPIInProgress: true,
    });

    // Track this regeneration session
    this.activeRegenerationSessions.set(sessionId, {
      aiMessageId: aiMessageId,
      timestamp: Date.now(),
      isActive: true,
      sessionId,
      prompt,
      selectedNodes: [...selectedNodes],
    });

    // Track the AI message for lifecycle management
    this.activeAIMessageIds.add(aiMessageId);
    this.currentActiveMessageId = aiMessageId;

    // Build edit request (now supports multi-selection)
    const editRequest = this.uiDesignSelectionService.buildEditRequest(prompt);
    if (!editRequest) {
      this.handleEditFailure('Failed to build edit request');
      return;
    }

    // Create the main edit API call observable
    const mainEditAPICall = this.uiDesignEditService.editUIDesignPage(editRequest);

    // Execute parallel API calls (main edit + intro) for regeneration with text replacement
    this.uiDesignIntroService
      .executeParallelRegeneration(
        prompt,
        selectedNodes,
        mainEditAPICall,
        this.currentActiveMessageId || undefined
      )
      .subscribe({
        next: result => {

          if (result.mainAPISuccess) {
            this.handleEditSuccess(result.mainAPIResult, selectedNodes);
            // CRITICAL FIX: Do NOT complete text replacement here
            // Wait for SSE events to complete before finalizing intro message
          } else {
            this.handleEditFailure('Main edit API failed');
          }
        },
        error: error => {
          this.handleEditFailure(error.message || 'Parallel regeneration API calls failed');
        },
      });

    this.cdr.markForCheck();
  }

  /**
   * Create loading nodes for regeneration operations
   */
  private createLoadingNodesForRegeneration(selectedNodes: MultiSelectedNodeData[]): void {

    const currentNodes = this.uiDesignNodes$.value;
    const loadingNodes: UIDesignNode[] = [];

    // Generate context-specific loading message
    const loadingMessage = this.generateLoadingMessage(selectedNodes);

    selectedNodes.forEach((selectedNode, index) => {
      // Find the existing node to get its position and dimensions
      const existingNode = currentNodes.find(node => node.id === selectedNode.nodeId);
      if (!existingNode) {
        return;
      }

      // Create loading node with same position and dimensions as original
      const loadingNode: UIDesignNode = {
        id: `loading-${selectedNode.nodeId}-${Date.now()}-${index}`,
        type: 'ui-design',
        data: {
          title: selectedNode.fileName,
          displayTitle: selectedNode.fileName,
          htmlContent: '',
          rawContent: '',
          width: existingNode.data.width,
          height: existingNode.data.height,
          isLoading: true,
          loadingMessage: loadingMessage,
          originalNodeId: selectedNode.nodeId, // Track which node this is loading for
        },
        position: { ...existingNode.position }, // Copy position
        selected: false,
        dragging: false,
        visible: true,
      };

      loadingNodes.push(loadingNode);
    });

    // Update loading nodes state
    this.uiDesignLoadingNodes$.next(loadingNodes);

  }

  /**
   * Generate context-specific loading message based on selected nodes
   */
  private generateLoadingMessage(selectedNodes: MultiSelectedNodeData[]): string {
    if (selectedNodes.length === 1) {
      return `Editing ${selectedNodes[0].fileName}...`;
    } else {
      return `Editing ${selectedNodes.length} selected pages...`;
    }
  }

  /**
   * Clear loading nodes when regeneration completes
   */
  private clearLoadingNodes(): void {
    this.uiDesignLoadingNodes$.next([]);
  }

  /**
   * Clear all types of loading nodes (comprehensive cleanup)
   * Handles both initial generation and regeneration loading nodes
   */
  private clearAllLoadingNodes(): void {

    // Clear regeneration loading nodes (separate BehaviorSubject)
    this.uiDesignLoadingNodes$.next([]);

    // Clear any loading nodes that might be in the main nodes array
    // This handles initial generation loading nodes
    const currentNodes = this.uiDesignNodes$.value;
    const nonLoadingNodes = currentNodes.filter(
      node =>
        !node.data.isLoading &&
        !node.id.startsWith('loading-') &&
        node.id !== 'ui-design-loading-node'
    );

    // Only update if we actually removed loading nodes
    if (nonLoadingNodes.length !== currentNodes.length) {
      this.uiDesignNodes$.next(nonLoadingNodes);
    }

  }

  /**
   * Handle successful edit response (enhanced for multi-selection with loading node cleanup)
   */
  private handleEditSuccess(response: any, selectedNodes: MultiSelectedNodeData[]): void {

    // Clear loading nodes first
    this.clearLoadingNodes();

    // Validate response
    const validatedResponse = this.uiDesignSelectionService.validateEditResponse(response);
    if (!validatedResponse) {
      this.handleEditFailure('Invalid response format from edit API');
      return;
    }

    // Update multiple nodes based on response
    this.updateMultipleNodesContent(validatedResponse, selectedNodes);

    // Update chat messages
    const fileNames = selectedNodes.map(node => node.fileName);
    this.updateEditChatMessages(true, fileNames);

    // Restore original text in chat messages if text replacement was used
    this.restoreOriginalChatMessageText();

    // Clear editing and regeneration state
    this.uiDesignSelectionService.setEditingInProgress(false);
    this.isUIDesignRegenerating$.next(false);
    this.isUIDesignLoading$.next(false);

    // CRITICAL FIX: Clear regeneration in progress state and complete regeneration process
    if (this.isRegenerationInProgress$.value) {
      this.completeDirectRegenerationResponse();
    }

  }

  /**
   * Handle edit failure (enhanced with comprehensive loading node cleanup)
   */
  private handleEditFailure(errorMessage: string): void {

    // Clear ALL loading nodes (comprehensive cleanup)
    this.clearAllLoadingNodes();

    // Update chat messages
    this.updateEditChatMessages(false, '', errorMessage);

    // Clear editing and regeneration state
    this.uiDesignSelectionService.setEditingInProgress(false);
    this.isUIDesignRegenerating$.next(false);
    this.isUIDesignLoading$.next(false);

    // CRITICAL FIX: Clear regeneration in progress state on failure
    if (this.isRegenerationInProgress$.value) {
      this.handleRegenerationFailure();
    }

    // Show error toast
    this.toastService.error(errorMessage);

    this.cdr.markForCheck();
  }

  /**
   * Identify truly new files that don't already exist in the canvas
   * Enhanced with robust filename normalization service
   */
  public identifyTrulyNewFiles(
    currentNodes: UIDesignNode[],
    responseMap: Map<string, string>,
    selectedNodes: MultiSelectedNodeData[]
  ): {
    newFileNames: string[];
    existingFileNames: string[];
    responseFileNames: string[];
    duplicatesFiltered: string[];
    analysisDetails: any;
  } {

    // Get all response file names
    const responseFileNames = Array.from(responseMap.keys());

    // Create simplified node data for the normalization service
    const existingNodeData = currentNodes.map(node => ({
      id: node.id,
      title: node.data.title,
      displayTitle: node.data.displayTitle,
    }));

    // Analyze each response file using the robust normalization service
    const analysisResults = responseFileNames.map(fileName => {
      // Check if this is a file from a selected node (these should update, not create new)
      const isSelectedNodeFile = selectedNodes.some(
        selected =>
          selected.fileName === fileName ||
          this.filenameNormalizationService.normalizeFilename(selected.fileName).canonicalKey ===
            this.filenameNormalizationService.normalizeFilename(fileName).canonicalKey
      );

      if (isSelectedNodeFile) {
        return {
          fileName,
          isDuplicate: true,
          reason: 'File belongs to selected node - should update existing node',
          isSelectedNodeFile: true,
        };
      }

      // Use the robust filename normalization service to find matches
      const matchResult = this.filenameNormalizationService.findMatchingNode(
        fileName,
        existingNodeData
      );

      return {
        fileName,
        isDuplicate: matchResult.isMatch,
        reason: matchResult.reason,
        matchType: matchResult.matchType,
        confidence: matchResult.confidence,
        matchedNodeId: matchResult.matchedNodeId,
        isSelectedNodeFile: false,
      };
    });

    // Filter out duplicates and get truly new files
    const newFileNames = analysisResults
      .filter(result => !result.isDuplicate)
      .map(result => result.fileName);

    const duplicatesFiltered = analysisResults
      .filter(result => result.isDuplicate)
      .map(result => result.fileName);

    const result = {
      newFileNames,
      existingFileNames: existingNodeData.map(node => node.title),
      responseFileNames,
      duplicatesFiltered,
      analysisDetails: {
        totalAnalyzed: responseFileNames.length,
        newFilesFound: newFileNames.length,
        duplicatesFiltered: duplicatesFiltered.length,
        individualAnalysis: analysisResults,
        normalizationServiceUsed: true,
      },
    };

    return result;
  }









  /**
   * Update multiple nodes content in canvas (enhanced for dynamic node creation)
   */
  private updateMultipleNodesContent(
    validatedResponse: any[],
    selectedNodes: MultiSelectedNodeData[]
  ): void {
    const currentNodes = this.uiDesignNodes$.value;
    let updatedCount = 0;

    // Create a map of fileName to response content for quick lookup
    const responseMap = new Map<string, string>();
    validatedResponse.forEach(file => {
      responseMap.set(file.fileName, file.content);
    });

    // Enhanced duplicate detection to prevent adding existing nodes
    const duplicateAnalysis = this.identifyTrulyNewFiles(currentNodes, responseMap, selectedNodes);
    const newFileNames = duplicateAnalysis.newFileNames;

    // Update existing nodes
    const updatedNodes = currentNodes.map(node => {
      // Check if this node was selected and has updated content
      const selectedNode = selectedNodes.find(selected => selected.nodeId === node.id);
      if (selectedNode && responseMap.has(selectedNode.fileName)) {
        const updatedContent = responseMap.get(selectedNode.fileName)!;

        // Enhance the updated HTML content
        const enhancedContent = this.enhanceHTMLWithCSS(updatedContent);
        const sanitizedContent = this.sanitizer.bypassSecurityTrustHtml(enhancedContent);

        updatedCount++;
        return {
          ...node,
          data: {
            ...node.data,
            htmlContent: sanitizedContent,
            rawContent: enhancedContent,
          },
        };
      }
      return node;
    });

    // Create new nodes for additional pages if any
    const newNodes = this.createNewNodesFromEditResponse(
      newFileNames,
      responseMap,
      updatedNodes.length
    );

    // Combine updated existing nodes with new nodes
    const finalNodes = [...updatedNodes, ...newNodes];

    this.uiDesignNodes$.next(finalNodes);
    this.cdr.markForCheck();

    // CRITICAL: Synchronize Overview Preview tab with updated canvas nodes
    this.synchronizeOverviewPreviewWithCanvasNodes(finalNodes, selectedNodes);
  }

  /**
   * Create new nodes from edit response for dynamic node creation
   * Enhanced with intelligent positioning using the positioning service
   */
  private createNewNodesFromEditResponse(
    newFileNames: string[],
    responseMap: Map<string, string>,
    existingNodeCount: number
  ): UIDesignNode[] {
    if (newFileNames.length === 0) {
      return [];
    }

    // Get existing nodes for positioning calculations
    const existingNodes = this.uiDesignNodes$.value.map(node => ({
      id: node.id,
      position: node.position,
      dimensions: { width: node.data.width, height: node.data.height },
    }));

    // Get selected node for intelligent positioning
    const selectedNodes = this.uiDesignSelectionService.getSelectedNodes();
    const selectedNodeId = selectedNodes.length > 0 ? selectedNodes[0].nodeId : '';

    // Calculate intelligent positions for new nodes using positioning service
    const positioningResult = this.uiDesignNodePositioningService.calculateRegenerationPositions(
      existingNodes,
      selectedNodeId,
      newFileNames.length
    );

    const newNodes: UIDesignNode[] = newFileNames.map((fileName, index) => {
      const content = responseMap.get(fileName) || '';

      // Enhance the HTML content
      const enhancedContent = this.enhanceHTMLWithCSS(content);
      const sanitizedContent = this.sanitizer.bypassSecurityTrustHtml(enhancedContent);

      // Use intelligent position from positioning service or fallback
      const position =
        positioningResult.positions[index] ||
        this.calculateNewNodePosition(
          existingNodeCount + index,
          existingNodeCount + newFileNames.length
        );

      const newNode: UIDesignNode = {
        id: this.generateNodeId(),
        type: 'ui-design',
        data: {
          title: fileName, // Keep original fileName for API compatibility
          displayTitle: this.formatFileName(fileName), // Formatted title for display
          htmlContent: sanitizedContent,
          rawContent: enhancedContent,
          width: 420, // Mobile device width
          height: 720, // Mobile device height
          isLoading: false,
        },
        position: position,
        selected: false,
        dragging: false,
        visible: true,
      };

      return newNode;
    });

    return newNodes;
  }

  /**
   * Calculate position for new nodes in the canvas grid
   */
  private calculateNewNodePosition(
    nodeIndex: number,
    totalNodes: number
  ): { x: number; y: number } {
    const columnsPerRow = 2;
    const gridSpacing = { x: 480, y: 780 }; // Mobile node spacing

    const row = Math.floor(nodeIndex / columnsPerRow);
    const col = nodeIndex % columnsPerRow;

    // Center the grid
    const totalCols = Math.min(totalNodes, columnsPerRow);
    const gridWidth = totalCols * gridSpacing.x;
    const startX = -gridWidth / 2 + gridSpacing.x / 2;

    const totalRows = Math.ceil(totalNodes / columnsPerRow);
    const gridHeight = totalRows * gridSpacing.y;
    const startY = -gridHeight / 2 + gridSpacing.y / 2;

    return {
      x: startX + col * gridSpacing.x,
      y: startY + row * gridSpacing.y,
    };
  }

  /**
   * Generate unique node ID for new nodes
   */
  private generateNodeId(): string {
    return `ui-design-node-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Format file name for display using UI Design filename transformation
   * ENHANCED: Now uses dedicated UI Design transformer with "Page" suffix
   * Examples:
   * - "landing_page.html" → "Landing Page Page" (handled by duplicate detection)
   * - "dashboard.html" → "Dashboard Page"
   * - "user-profile.html" → "User Profile Page"
   */
  private formatFileName(fileName: string): string {
    if (!fileName) return '';

    try {
      // ENHANCED: Use the dedicated UI Design filename transformer service
      const transformResult = this.uiDesignFilenameTransformerService.transformFileName(fileName);

      // Return the display title which includes the "Page" suffix
      const displayTitle = transformResult.displayTitle || 'Untitled Page';

      return displayTitle;
    } catch (error) {

      // Fallback to basic transformation if service fails
      const nameWithoutExtension = fileName.replace(/\.[^/.]+$/, '');
      return (
        nameWithoutExtension
          .replace(/[_-]/g, ' ')
          .replace(/([a-z])([A-Z])/g, '$1 $2')
          .split(' ')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
          .join(' ')
          .trim() || 'Untitled Page'
      );
    }
  }

  /**
   * Synchronize Overview Preview tab with updated canvas nodes
   * CRITICAL: This ensures the Overview Preview tab shows the same updated content as canvas nodes
   */
  private synchronizeOverviewPreviewWithCanvasNodes(
    updatedNodes: UIDesignNode[],
    _selectedNodes: MultiSelectedNodeData[]
  ): void {

    try {
      // Get current UI Design response data
      const currentResponseData = this.generateUIDesignService.getUIDesignResponse();

      if (!currentResponseData) {
        this.createNewUIDesignResponseFromNodes(updatedNodes);
        return;
      }

      // Create updated pages array with new content from canvas nodes
      const updatedPages: MobilePage[] = updatedNodes.map(node => ({
        fileName: node.data.displayTitle || node.data.title, // Use formatted display title
        content: node.data.rawContent,
      }));

      // Create new response data with updated pages
      const updatedResponseData: UIDesignResponseData = {
        ...currentResponseData,
        pages: updatedPages,
      };

      // Update the UI Design service with new response data
      this.generateUIDesignService.setUIDesignResponse(updatedResponseData);

      // Update the local uiDesignPages observable
      this.uiDesignPages$.next(updatedPages);

      // Force change detection to ensure Overview Preview tab updates
      this.cdr.detectChanges();

    } catch (error) {
    }
  }

  /**
   * Create new UI Design response data from canvas nodes
   */
  private createNewUIDesignResponseFromNodes(nodes: UIDesignNode[]): void {
    const pages: MobilePage[] = nodes.map(node => ({
      fileName: node.data.displayTitle || node.data.title, // Use formatted display title
      content: node.data.rawContent,
    }));

    const responseData: UIDesignResponseData = {
      pages: pages,
      jobId: 'ui-design-' + Date.now(),
      projectId: 'ui-design-project-' + Date.now(),
    };

    this.generateUIDesignService.setUIDesignResponse(responseData);
    this.uiDesignPages$.next(pages);

  }



  /**
   * Clean up any legacy edit messages that might be present
   * Removes outdated or problematic messages from previous implementations
   */
  private cleanupLegacyEditMessages(): void {

    // Remove any legacy messages that might interfere with current edit workflow
    this.lightMessages = this.lightMessages.filter(msg => {
      const text = msg.text.toLowerCase();
      const isLegacyMessage =
        text.includes('hang tight') ||
        text.includes('working on edit') ||
        text.includes('edit is coming') ||
        text.includes('edit coming up') ||
        text.includes('we are working on') ||
        text.includes('processing your edit') ||
        text.includes('regenerating your') ||
        text.includes('updating your design') ||
        text.includes('modifying your page');

      if (isLegacyMessage) {
        return false;
      }
      return true;
    });

    this.cdr.markForCheck();
  }

  /**
   * Update chat messages for edit operations (enhanced for multi-selection)
   * Ensures clean message state and removes any legacy messages
   */
  private updateEditChatMessages(
    success: boolean,
    fileNames: string | string[],
    _errorMessage?: string
  ): void {
    // CRITICAL: DO NOT remove AI messages - only remove specific loading/editing messages
    this.lightMessages = this.lightMessages.filter(msg => {
      const isTemporaryEditingMessage =
        msg.text.includes('editing') ||
        msg.text.includes('Please wait') ||
        msg.text.includes('Hang tight') ||
        msg.text.includes('working on edit') ||
        msg.text.includes('edit is coming') ||
        msg.text.toLowerCase().includes('processing') ||
        msg.text.toLowerCase().includes('regenerating');

      const isAIMessage =
        msg.from === 'ai' &&
        msg.id &&
        (msg.id.startsWith('ai-generation-') || msg.id.startsWith('ai-regeneration-'));

      // Keep all AI messages, remove only temporary editing messages
      return !isTemporaryEditingMessage || isAIMessage;
    });

    if (success) {
      // Handle both single and multiple file names
      let successMessage: string;
      if (Array.isArray(fileNames)) {
        if (fileNames.length === 1) {
          successMessage = `Successfully updated "${fileNames[0]}"! The changes are now visible in the canvas.`;
        } else {
          successMessage = `Successfully updated ${fileNames.length} pages! The changes are now visible in the canvas.`;
        }
      } else {
        successMessage = `Successfully updated "${fileNames}"! The changes are now visible in the canvas.`;
      }

      // Add success message with typewriting effect
      const messageId = `ai-edit-success-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

      this.lightMessages.push({
        id: messageId,
        text: '', // Start with empty text for typewriting effect
        from: 'ai',
        theme: 'light',
      });

      // Start typewriting effect for the success message
      this.startTypewriterEffectForMessage(successMessage, messageId);
    } else {
      // Add error message
      // this.lightMessages.push({
      //   text: `❌ Failed to update the page: ${errorMessage}. Please try again.`,
      //   from: 'ai',
      //   theme: 'light'
      // });
    }

    this.cdr.markForCheck();
  }

  /**
   * Enhance HTML content with CSS frameworks for proper iframe rendering
   * (Simplified version for code-window component)
   */
  private enhanceHTMLWithCSS(htmlContent: string): string {
    if (!htmlContent || typeof htmlContent !== 'string') {
      return '<html><body><p>No content available</p></body></html>';
    }

    // Check if HTML already has a complete document structure
    const hasDoctype = htmlContent.includes('<!DOCTYPE');
    const hasHtmlTag = htmlContent.includes('<html');
    const hasHead = htmlContent.includes('<head');

    if (hasDoctype && hasHtmlTag && hasHead) {
      // HTML is already complete, just ensure CSS frameworks are present
      return this.injectCSSFrameworksIfMissing(htmlContent);
    } else {
      // HTML is incomplete, wrap it in a complete document structure
      return this.wrapInCompleteHTML(htmlContent);
    }
  }

  /**
   * Inject CSS frameworks if missing from existing HTML
   */
  private injectCSSFrameworksIfMissing(htmlContent: string): string {
    let enhanced = htmlContent;

    // Check for Tailwind CSS
    if (!enhanced.includes('tailwindcss')) {
      enhanced = enhanced.replace(
        '</head>',
        '  <link href="https://cdn.tailwindcss.com/3.3.0" rel="stylesheet">\n</head>'
      );
    }

    if (!enhanced.includes('bootstrap')) {
      enhanced = enhanced.replace(
        '</head>',
        '  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">\n</head>'
      );
    }

    return enhanced;
  }

  private wrapInCompleteHTML(htmlContent: string): string {
    return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>UI Design Preview</title>

  <!-- CSS Frameworks -->
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

  <style>
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
      line-height: 1.6 !important;
      margin: 0 !important;
      padding: 20px !important;
      background: #f8fafc !important;
      min-height: 100vh;
    }
    * { box-sizing: border-box !important; }
    .container { max-width: 100% !important; margin: 0 auto !important; }
    img { max-width: 100%; height: auto; }
  </style>
</head>
<body class="iframe-content">
  ${htmlContent}
</body>
</html>`;
  }

  isNodeSelectedForEditing(nodeId: string): boolean {
    return this.uiDesignSelectionService.isNodeSelected(nodeId);
  }

  selectAllNodes(): void {
    const currentNodes = this.uiDesignNodes$.value;
    if (currentNodes.length === 0) {
      return;
    }

    const allNodesData: MultiSelectedNodeData[] = currentNodes.map(node => ({
      nodeId: node.id,
      fileName: node.data.displayTitle || node.data.title,
      htmlContent: node.data.htmlContent as string,
      rawContent: node.data.rawContent,
      selectedImages: [],
      metadata: {
        nodePosition: node.position,
        nodeDimensions: { width: node.data.width, height: node.data.height },
        selectionTimestamp: Date.now(),
      },
    }));

    this.uiDesignSelectionService.selectAllNodes(allNodesData);
  }

  clearAllSelection(): void {
    this.uiDesignSelectionService.clearSelection();
  }

  getSelectedNodesCount(): number {
    return this.uiDesignSelectionService.getSelectedNodesCount();
  }

  getPromptBarPlaceholder(): string {
    if (this.isUIDesignMode$.value) {
      return this.uiDesignSelectionService.getSelectionSummary();
    }
    return 'Ask me';
  }

  getPromptBarEnabledState(): boolean {
    if (this.isUIDesignMode$.value) {

      if (this.isUIDesignRegenerating$.value) {
        return false;
      }
      return this.uiDesignSelectionService.getIsPromptBarEnabled();
    }

    if (this.isRegenerationInProgress$.value) {
      return false;
    }

    return this.isPromptBarEnabled;
  }

  getPromptBarDisabledState(): boolean {
    if (this.isUIDesignMode$.value) {

      if (this.isUIDesignRegenerating$.value) {
        return true;
      }
      return !this.uiDesignSelectionService.getIsPromptBarEnabled();
    }

    if (this.isRegenerationInProgress$.value) {
      return true;
    }

    return !this.isPromptBarEnabled;
  }

  handleUIDesignPromptSubmission(): void {
    if (this.isUIDesignMode$.value) {

      if (this.lightPrompt && this.lightPrompt.trim()) {
        this.onUIDesignEditPrompt(this.lightPrompt.trim());
        this.lightPrompt = '';
      }
    } else {

      this.handleCodeRegenerationSubmission();
    }
  }

  handleRegenerationPayload(payload: { prompt: string; image: string[] }): void {

    this.validateAndResetRegenerationState('New regeneration request');

    if (this.isRegenerationCallInProgress) {
      return;
    }

    this.clearRegenerationTimeout();

    if (this.isUIDesignMode$.value) {

      const selectedNodes = this.uiDesignSelectionService.getSelectedNodes();
      if (selectedNodes.length === 0) {
        this.toastService.error('Please select one or more pages to edit');
        return;
      }

      this.cleanupLegacyEditMessages();

      this.createLoadingNodesForRegeneration(selectedNodes);

      this.uiDesignSelectionService.setEditingInProgress(true);
      this.isUIDesignRegenerating$.next(true);
      this.isUIDesignLoading$.next(true);

      const sessionId = this.generateRegenerationSessionId();

      const aiMessageId = `ai-regeneration-${sessionId}`;

      this.lightMessages.push({
        id: aiMessageId,
        text: '',
        from: 'ai',
        theme: 'light',
        showLoadingIndicator: true,
        loadingPhase: 'intro',
        mainAPIInProgress: true,
      });

      this.activeRegenerationSessions.set(sessionId, {
        aiMessageId: aiMessageId,
        timestamp: Date.now(),
        isActive: true,
        sessionId,
        prompt: payload.prompt,
        selectedNodes: [...selectedNodes],
      });

      this.activeAIMessageIds.add(aiMessageId);
      this.currentActiveMessageId = aiMessageId;

      const editRequest = this.uiDesignSelectionService.buildEditRequest(payload.prompt);
      if (!editRequest) {
        this.handleEditFailure('Failed to build edit request');
        return;
      }

      const mainEditAPICall = this.uiDesignEditService.editUIDesignPage(editRequest);

      this.uiDesignIntroService
        .executeParallelRegeneration(
          payload.prompt,
          selectedNodes,
          mainEditAPICall,
          this.currentActiveMessageId || undefined
        )
        .subscribe({
          next: result => {

            if (result.mainAPISuccess) {
              this.handleEditSuccess(result.mainAPIResult, selectedNodes);
            } else {
              this.handleEditFailure('Main edit API failed');
            }
          },
          error: _error => {
            this.handleEditFailure('Regeneration API calls failed');
          }
        });
    } else {

      this.handleCodeRegeneration(payload);
    }
  }

  private handleCodeRegeneration(payload: { prompt: string; image: string[] }): void {

    const currentCodeFiles = this.getCurrentCodeFiles();
    if (!currentCodeFiles || currentCodeFiles.length === 0) {
      this.handleEditError('No code files found to edit. Please ensure code generation is complete.');
      return;
    }

    this.sequentialRegenerationService.reset();

    this.regenerationIntegrationService.startRegeneration();

    this.executeSequentialCodeRegenerationWithPayload(currentCodeFiles, payload.prompt, payload.image);
  }

  handleUserMessageData(messageData: {
    prompt: string;
    images: Array<{ url: string; name: string; id: string }>;
    timestamp: string;
  }): void {

    const imageDataUri = messageData.images.length > 0 ? messageData.images[0].url : undefined;

    const userMessageId = `user-regeneration-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    this.lightMessages.push({
      id: userMessageId,
      from: 'user',
      text: messageData.prompt,
      theme: 'light',
      imageDataUri: imageDataUri
    });

    this.cdr.detectChanges();
  }

  private executeSequentialCodeRegenerationWithPayload(
    currentCodeFiles: FileModel[],
    userRequest: string,
    images: string[]
  ): void {

    this.isRegenerationCallInProgress = true;

    this.startRegenerationTimeout();

    this.prepareForRegeneration();

    this.cleanupStatusMonitoring();

    this.lastUserRequest = userRequest;

    this.isCodeGenerationLoading$.next(true);
    this.isRegenerationInProgress$.next(true);
    this.regenerationStartTime = Date.now();

    if (!this.projectId || !this.jobId) {
      const appState = this.appStateService.getState();
      if (appState?.project?.projectId && appState?.project?.jobId) {
        this.projectId = appState.project.projectId;
        this.jobId = appState.project.jobId;
      } else {
        this.handleEditError('Cannot start regeneration: Missing project information. Please try generating code first.');
        return;
      }
    }

    const aiMessageId = `ai-regeneration-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;



    this.activeAIMessageIds.add(aiMessageId);
    this.currentActiveMessageId = aiMessageId;

    this.executeSequentialRegenerationAPIs(currentCodeFiles, userRequest, images, aiMessageId);
  }

  private executeSequentialRegenerationAPIs(
    currentCodeFiles: FileModel[],
    userRequest: string,
    images: string[],
    aiMessageId: string
  ): void {

    const mainRegenerationAPICall = this.codeGenerationService
      .editCode(currentCodeFiles, userRequest, images, this.projectId || undefined, this.jobId || undefined);

    this.sequentialRegenerationService
      .executeSequentialCodeRegeneration(
        userRequest,
        currentCodeFiles,
        mainRegenerationAPICall,
        this.projectId!,
        this.jobId!,
        aiMessageId,
        {
          enableIntroAPI: true,
          enableSSE: true,
          sseEventType: 'code-regen',
          timeoutMs: 600000,
          retryAttempts: 2
        }
      )
      .subscribe({
        next: result => {

          if (result.overallSuccess) {
            this.handleSequentialRegenerationSuccess(result, aiMessageId);
          } else {

            if (result.timeout && result.reason === 'deployment_completed') {
              this.handleSequentialRegenerationSuccess(result, aiMessageId);
            } else {
              this.handleSequentialRegenerationError(new Error('Sequential regeneration failed'), aiMessageId);
            }
          }
        },
        error: error => {

          if (error.message && error.message.includes('timeout')) {
            this.handleSequentialRegenerationError(error, aiMessageId);
          } else {
            this.handleSequentialRegenerationError(error, aiMessageId);
          }
        },
        complete: () => {

          this.resetRegenerationCallFlag('Sequential regeneration completed');
        }
      });

    this.subscribeToSequentialRegenerationProgress(aiMessageId);
  }

  private handleSequentialRegenerationSuccess(result: any, aiMessageId: string): void {

    const aiMessage = this.lightMessages.find(msg => msg.id === aiMessageId);
    if (aiMessage) {

      aiMessage.showLoadingIndicator = true;
      aiMessage.loadingPhase = 'main';
      aiMessage.mainAPIInProgress = false;
    }

    if (result.regenerationResult?.data) {

    }

    this.cdr.detectChanges();
  }

  private startRegenerationTimeout(): void {
    this.clearRegenerationTimeout();

    this.regenerationTimeoutTimer = window.setTimeout(() => {

      this.validateAndResetRegenerationState('Regeneration timeout reached');

      this.sequentialRegenerationService.reset();

      this.regenerationIntegrationService.completeRegeneration();

      this.handleEditError('Regeneration timed out. Please try again.');

    }, this.REGENERATION_TIMEOUT_MS);

  }

  private clearRegenerationTimeout(): void {
    if (this.regenerationTimeoutTimer) {
      window.clearTimeout(this.regenerationTimeoutTimer);
      this.regenerationTimeoutTimer = null;
    }
  }

  private validateAndResetRegenerationState(_reason: string): void {

    if (this.isRegenerationCallInProgress) {
      this.isRegenerationCallInProgress = false;
    }

    if (this.isCodeGenerationLoading$.value) {
      this.isCodeGenerationLoading$.next(false);
    }

    if (this.isRegenerationInProgress$.value) {
      this.isRegenerationInProgress$.next(false);
    }

    this.clearRegenerationTimeout();

    this.codeRegenerationProgressDescription = '';

    this.regenerationStartTime = 0;

  }

  private resetRegenerationCallFlag(_reason: string): void {
    this.isRegenerationCallInProgress = false;
    this.clearRegenerationTimeout();
  }

  private handleSequentialRegenerationError(_error: any, aiMessageId: string): void {

    this.performRegenerationErrorCleanup();

    const aiMessage = this.lightMessages.find(msg => msg.id === aiMessageId);
    if (aiMessage) {
      aiMessage.showLoadingIndicator = false;
      aiMessage.loadingPhase = undefined;
      aiMessage.mainAPIInProgress = false;
      aiMessage.text = 'Sorry, there was an error during regeneration. The system has been reset and you can try again.';
    }

    this.isCodeGenerationLoading$.next(false);
    this.isRegenerationInProgress$.next(false);

    this.resetRegenerationCallFlag('Sequential regeneration error');

    this.handleEditError('Regeneration failed. Please try again.');

    this.cdr.detectChanges();
  }

  private performRegenerationErrorCleanup(): void {

    this.codeRegenerationProgressDescription = '';
    this.stopRegenerationPreviewLoading();

    this.previewError$.next(false);
    this.isIframeReady$.next(false);
    this.isUrlValidated$.next(false);
    this.isUrlAvailable$.next(false);

    this.clearRegenerationTimeout();

    const sessionKey = this.currentCheckpointSession();
    if (sessionKey) {
      this.regenerationCheckpointService.completeCheckpointSession(sessionKey, 'failed');
      this.currentCheckpointSession.set(null);
    }

    this.stepperStateService.setRegenerationActive(false);

    this.regenerationIntegrationService.completeRegeneration();

    this.performSequentialRegenerationCleanup();

  }

  private performSequentialRegenerationCleanup(): void {

    try {

      this.sequentialRegenerationService.reset();
    } catch (error) {
    }
  }

  private handleCodeRegenerationSubmission(): void {
    const userRequest = this.lightPrompt.trim();
    const currentImageDataUri = this.selectedImageDataUri;

    if (!userRequest) {
      return;
    }

    this.lightPrompt = '';

    const currentCodeFiles = this.getCurrentCodeFiles();
    if (!currentCodeFiles || currentCodeFiles.length === 0) {
      this.handleEditError('No code files found to edit. Please ensure code generation is complete.');
      return;
    }

    const images: string[] = currentImageDataUri ? [currentImageDataUri] : [];

    this.executeParallelCodeRegeneration(currentCodeFiles, userRequest, images);

  }

  private executeParallelCodeRegeneration(
    _currentCodeFiles: FileModel[],
    userRequest: string,
    _images: string[],
    skipUserMessage: boolean = false
  ): void {

    if (this.isRegenerationCallInProgress && !skipUserMessage) {
      return;
    }

    if (!skipUserMessage) {
      this.clearRegenerationTimeout();
    }

    if (!skipUserMessage) {
      this.isRegenerationCallInProgress = true;

      this.startRegenerationTimeout();
    }

    this.lastUserRequest = userRequest;

    this.isCodeGenerationLoading$.next(true);
    this.isRegenerationInProgress$.next(true);
    this.regenerationStartTime = Date.now();

    this.subscribeToRegenerationProgress();

    this.startRegenerationPreviewLoading();

    if (!this.projectId || !this.jobId) {
      const appState = this.appStateService.getState();
      if (appState?.project?.projectId && appState?.project?.jobId) {
        this.projectId = appState.project.projectId;
        this.jobId = appState.project.jobId;
      } else {
        this.handleEditError('Cannot start regeneration: Missing project information. Please try generating code first.');
        return;
      }
    }

    const aiMessageId = `ai-regeneration-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    if (!skipUserMessage) {
      this.lightMessages.push({
        text: userRequest,
        from: 'user',
        theme: 'light',
      });
    } else {
    }

    this.lightMessages.push({
      id: aiMessageId,
      text: '',
      from: 'ai',
      theme: 'light',
      showIntroMessage: true,
      showLoadingIndicator: true,
      loadingPhase: 'intro',
      mainAPIInProgress: true,
    });

    this.activeAIMessageIds.add(aiMessageId);
    this.currentActiveMessageId = aiMessageId;

    if (this.projectId && this.jobId) {
      this.startStatusMonitoring(this.projectId, this.jobId, 'code-regeneration-parallel');
    }
    this.updatePollingStatus(true);

    this.subscribeToRegenerationSSEUpdates(aiMessageId);
  }







  private handleRegenerationSSEFailure(aiMessageId: string, progressUpdate: any): void {

    this.isCodeGenerationLoading$.next(false);
    this.isRegenerationInProgress$.next(false);
    this.resetRegenerationCallFlag('Regeneration error handling');
    this.codeRegenerationProgressDescription = '';
    this.stopRegenerationPreviewLoading();

    const aiMessage = this.lightMessages.find(msg => msg.id === aiMessageId);
    if (aiMessage) {
      aiMessage.showLoadingIndicator = false;
      aiMessage.loadingPhase = 'completed';
      aiMessage.mainAPIInProgress = false;

      aiMessage.text = progressUpdate.dynamicMessage || progressUpdate.log || 'Regeneration failed. Please try again.';
    }

    const errorMessage = progressUpdate.dynamicMessage || progressUpdate.log || 'Regeneration failed. Please try again.';
    this.createRegenerationErrorResult(errorMessage);

    this.codeGenerationIntroService.completeRegenerationAfterSSE();

    this.activeAIMessageIds.delete(aiMessageId);
    this.currentActiveMessageId = null;

    this.toastService.error(errorMessage);

    this.currentProgress = 'DEPLOY';
    this.currentStatus = 'FAILED';
    this.updatePromptBarEnabledState();

    if (this.projectId && this.jobId) {
      this.startStatusMonitoring(this.projectId, this.jobId, 'post-regeneration-failure');
    }

    this.cdr.detectChanges();

  }



  private subscribeToRegenerationSSEUpdates(aiMessageId: string): void {

    if (!this.subscription) {
      this.subscription = new Subscription();
    }

    this.subscription.add(
      this.sseDataProcessor.codeFiles$.pipe(
        takeUntil(this.destroy$),
        filter(() => this.isRegenerationInProgress$.value),
        filter(() => this.isCodeGenerationComplete)
      ).subscribe(codeFiles => {

        if (codeFiles && codeFiles.length > 0) {

          const fileModels = codeFiles.map(file => ({
            name: file.path || 'Unknown file',
            type: 'file' as const,
            content: file.code || '',
            fileName: file.path || 'Unknown file'
          }));

          this.files$.next(fileModels);
          this.handleRegenerationCodeFiles(fileModels, aiMessageId);

          // ENHANCEMENT: Automatic tab switching for regeneration code files
          this.handleAutomaticCodeTabSwitch();
        }
      })
    );

  }

  private subscribeToSequentialRegenerationProgress(aiMessageId: string): void {

    if (!this.subscription) {
      this.subscription = new Subscription();
    }

    this.subscription.add(
      this.sequentialRegenerationService.progressUpdates$.pipe(
        takeUntil(this.destroy$),
        filter(() => this.isRegenerationInProgress$.value)
      ).subscribe(progressUpdate => {

        if (progressUpdate.introMessage && progressUpdate.event === 'intro-message-typewriter') {

          const aiMessage = this.lightMessages.find(msg => msg.id === aiMessageId);
          if (aiMessage) {

            aiMessage.isTyping = true;
            aiMessage.showLoadingIndicator = false;
            this.startTypewriterEffectForMessage(progressUpdate.introMessage, aiMessageId);
          } else {
          }
        }

        if (progressUpdate.event === 'code-regen' &&
            progressUpdate.progress === 'DEPLOY' &&
            progressUpdate.status === 'COMPLETED' &&
            progressUpdate.deploymentCompleted) {

          this.completeRegenerationProcess(aiMessageId);

          this.handleRegenerationUIStateUpdate({
            shouldRefresh: true,
            shouldSwitchTab: 'preview',
            phase: 'DEPLOY',
            status: 'COMPLETED'
          });

          this.codeGenerationIntroService.completeRegenerationAfterSSE();

          return;
        }

        if (progressUpdate.event === 'regeneration-failed' && progressUpdate.status === 'FAILED') {

          this.handleRegenerationSSEFailure(aiMessageId, progressUpdate);

          return;
        }

      })
    );

  }









  private cleanUrl(url: string): string {
    if (!url || typeof url !== 'string') {
      return '';
    }

    let cleanedUrl = url;

    cleanedUrl = cleanedUrl.replace(/\x1b\[[0-9;]*m/g, '');

    cleanedUrl = cleanedUrl.replace(/\[\d+m/g, '');

    cleanedUrl = cleanedUrl.replace(/[\x00-\x1F\x7F]/g, '');

    const netIndex = cleanedUrl.indexOf('.net');
    if (netIndex !== -1) {
      cleanedUrl = cleanedUrl.substring(0, netIndex + 4);
    }

    cleanedUrl = cleanedUrl.replace(/\?+$/, '');

    cleanedUrl = cleanedUrl.trim();

    if (cleanedUrl !== url) {
    }

    return cleanedUrl;
  }



  private completeRegenerationProcess(aiMessageId: string): void {

    const sessionKey = this.currentCheckpointSession();
    if (sessionKey) {
      this.regenerationCheckpointService.completeCheckpointSession(sessionKey, 'completed');
      this.currentCheckpointSession.set(null);

    }

    this.isCodeGenerationLoading$.next(false);
    this.isRegenerationInProgress$.next(false);
    this.codeRegenerationProgressDescription = '';

    this.stopRegenerationPreviewLoading();

    const targetMessage = this.lightMessages.find(msg => msg.id === aiMessageId);
    if (targetMessage) {
      targetMessage.showLoadingIndicator = false;
      targetMessage.loadingPhase = 'completed';
      targetMessage.mainAPIInProgress = false;
    }

    const currentDeployedUrl = this.deployedUrl$.value;
    if (currentDeployedUrl) {

      this.isPreviewTabEnabled = true;
      this.previewError$.next(false);

    } else {

    }

    if (this.currentView$.value !== 'preview') {
      this.currentView$.next('preview');
    }

    this.resetRegenerationCallFlag('Regeneration completion');

    if (this.projectId && this.jobId) {
      this.startStatusMonitoring(this.projectId, this.jobId, 'post-regeneration');
      this.updatePollingStatus(true);
    }

    this.cdr.detectChanges();

  }



  private handleRegenerationCodeFiles(codeFiles: any[], _aiMessageId: string): void {
    if (!codeFiles || codeFiles.length === 0) {
      return;
    }

    const currentProgress = this.newPollingResponseProcessor.getCurrentProgress();
    const currentStatus = this.newPollingResponseProcessor.getCurrentStatus();

    if (currentProgress === 'BUILD') {

      const fileModels = codeFiles.map(file => {
        const content = file.code || file.content || file.data || '';
        const fileName = file.path || file.name || file.fileName || 'unknown.txt';

        if (!content) {
        }

        return {
          name: fileName,
          fileName: fileName,
          language: this.getLanguageFromPath(fileName),
          content: content,
          path: fileName,
          type: 'file' as const,
        };
      });

      this.generatedCode = fileModels;
      this.isCodeGenerationComplete = true;

      if (!this.isCodeTabEnabled) {
        this.isCodeTabEnabled = true;
      }

      if (currentProgress && currentProgress.toString().toUpperCase() === "DEPLOY" && currentStatus === 'COMPLETED') {

        this.ngZone.run(() => {
          this.toggleCodeView();
          this.cdr.markForCheck();
        });
      }



      this.cdr.detectChanges();

    } else {
    }
  }













  private createRegenerationAccordion(accordionData: any): void {

    const projectName = this.projectName || accordionData.projectName || 'Untitled Project';

    const generationResult: GenerationResult = {
      type: 'success',
      version: accordionData.version,
      projectName: projectName,
      files: accordionData.files,
      timestamp: accordionData.timestamp,
      isLatest: true
    };

    if (this.chatWindow) {
      this.chatWindow.addGenerationResult(generationResult);
    } else {
    }

    this.cdr.detectChanges();
  }

  private handleRegenerationUIStateUpdate(stateUpdate: any): void {

    if (stateUpdate.description && stateUpdate.isProgressDescription) {
      this.codeRegenerationProgressDescription = stateUpdate.description;
    }

    this.updatePreviewTabStateForRegeneration(stateUpdate);

    if (stateUpdate.shouldSwitchTab) {
      if (stateUpdate.shouldSwitchTab === 'code') {
        this.switchToCodeTab();
      } else if (stateUpdate.shouldSwitchTab === 'preview') {
        this.switchToPreviewTab();
      }
    }

    if (stateUpdate.shouldRefresh) {
      this.refreshIframe();
    }

    if (stateUpdate.phase === 'CODE_GENERATION' && stateUpdate.status === 'IN_PROGRESS') {
      this.isCodeGenerationLoading$.next(true);
    } else if (stateUpdate.phase === 'CODE_GENERATION' && stateUpdate.status === 'COMPLETED') {
      this.isCodeGenerationLoading$.next(false);
    }

    this.cdr.detectChanges();
  }

  private switchToCodeTab(): void {
    this.currentView$.next('editor');
    this.isCodeActive$.next(true);
    this.isPreviewActive$.next(false);
    this.isArtifactsActive$.next(false);
    this.cdr.detectChanges();
  }

  private switchToPreviewTab(): void {
    this.currentView$.next('preview');
    this.isCodeActive$.next(false);
    this.isPreviewActive$.next(true);
    this.isArtifactsActive$.next(false);
    this.cdr.detectChanges();
  }

  private refreshIframe(): void {

    const currentUrl = this.deployedUrl$.value;
    if (currentUrl) {
      const separator = currentUrl.includes('?') ? '&' : '?';
      const refreshUrl = `${currentUrl}${separator}_refresh=${Date.now()}`;

      this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl(refreshUrl);
      this.cdr.detectChanges();

    } else {
    }
  }

  onCanvasWheel(event: WheelEvent): void {

    const rect = this.uiDesignCanvas?.nativeElement.getBoundingClientRect();
    if (!rect) return;

    const isOverCanvas = event.clientX >= rect.left &&
                        event.clientX <= rect.right &&
                        event.clientY >= rect.top &&
                        event.clientY <= rect.bottom;

    if (!isOverCanvas) return;

    event.preventDefault();

    const centerPoint = {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top,
    };

    this.ngZone.run(() => {
      if (event.deltaY < 0) {
        this.uiDesignCanvasService.zoomIn(centerPoint);
      } else {
        this.uiDesignCanvasService.zoomOut(centerPoint);
      }
    });
  }

  zoomInCanvas(): void {
    this.uiDesignCanvasService.zoomIn();
  }

  zoomOutCanvas(): void {
    this.uiDesignCanvasService.zoomOut();
  }

  resetCanvasView(): void {

    this.updateCanvasContainerSize();

    this.uiDesignCanvasService.resetViewport();

    setTimeout(() => {
      this.centerCanvasOnNodes();
    }, 50);

  }

  fitCanvasToView(): void {
    this.uiDesignViewportService.fitContentToView();
  }

  centerCanvasOnNodes(): void {
    this.uiDesignViewportService.centerViewOnNodes();
  }

  centerCanvasOnNodesWithViewport(viewport: { x: number; y: number; zoom: number }): void {

    this.uiDesignCanvasService.updateViewport({
      x: viewport.x,
      y: viewport.y,
      zoom: viewport.zoom,
    });

  }

  updateCanvasContainerSize(): void {
    if (this.uiDesignCanvas?.nativeElement) {
      const canvasContainer = this.uiDesignCanvas.nativeElement;
      const canvasRect = canvasContainer.getBoundingClientRect();

      const rightPanelContent = canvasContainer.closest('[awe-rightpanel-content]');
      const rightPanelRect = rightPanelContent?.getBoundingClientRect();

      const containerRect = rightPanelRect || canvasRect;


      const rightPanelPadding = 16;

      const availableWidth = containerRect.width - rightPanelPadding * 2;


      const finalWidth = Math.max(availableWidth, 300);

      const finalHeight = 500;

      this.uiDesignViewportService.updateContainerSize(finalWidth, finalHeight);

    }
  }

  private setupAutoCanvasCentering(): void {

    this.updateCanvasContainerSize();

    setTimeout(() => {
      this.centerCanvasOnNodes();
    }, 100);

    if (this.uiDesignCanvas?.nativeElement && typeof ResizeObserver !== 'undefined') {
      const resizeObserver = new ResizeObserver(entries => {
        for (const _entry of entries) {

          clearTimeout(this.resizeTimeout);
          this.resizeTimeout = setTimeout(() => {
            this.updateCanvasContainerSize();
            this.centerCanvasOnNodes();
          }, 150);
        }
      });

      resizeObserver.observe(this.uiDesignCanvas.nativeElement);

      this.canvasResizeObserver = resizeObserver;
    }

  }

  getCanvasTransformStyle(): string {
    return this.uiDesignCanvasService.getTransformStyle();
  }

  getCanvasZoomPercentage(): number {
    return this.uiDesignCanvasService.getZoomPercentage();
  }

  isCanvasAtMinZoom(): boolean {
    return this.uiDesignCanvasService.isAtMinZoom();
  }

  isCanvasAtMaxZoom(): boolean {
    return this.uiDesignCanvasService.isAtMaxZoom();
  }

  trackByUIDesignNode(_index: number, node: UIDesignNode): string {
    return node.id;
  }

  getLoadingText(title: string): string {

    if (title.includes('Editing') && title.includes('...')) {
      return title;
    }

    return 'Generating wireframe...';
  }

  private subscribeToNewPollingProcessor(): void {
    if (!this.subscription) {
      this.subscription = new Subscription();
    }

    this.subscription.add(
      this.newPollingResponseProcessor.currentProgress$.subscribe(progress => {

        if (this.shouldBlockUIDesignData('progress')) {
          return;
        }

        this.currentProgress = progress;
        this.handleNewProgressChange(progress);
        this.updatePromptBarEnabledState();

        this.updatePreviewTabStateForProgress(progress);

        this.updateCodeTabStateForProgress(progress);

      })
    );

    this.subscription.add(
      this.newPollingResponseProcessor.currentStatus$.subscribe(status => {

        if (this.shouldBlockUIDesignData('status')) {
          return;
        }

        this.currentStatus = status;
        this.handleNewStatusChange(status);
        this.updatePromptBarEnabledState();

        this.updatePreviewTabStateForProgress(this.currentProgress);

        this.updateCodeTabStateForProgress(this.currentProgress);

      })
    );

    this.subscription.add(
      this.newPollingResponseProcessor.progressDescription$.subscribe(description => {

        if (this.isUIDesignMode$.value) {
          return;
        }

        this.newProgressDescription = description;
        this.updateStepperDescription(description);
      })
    );

    this.subscription.add(
      this.newPollingResponseProcessor.logContent$.subscribe(content => {

        if (this.shouldBlockUIDesignData('logs')) {
          return;
        }

        this.newLogContent = content;
        this.updateLogWindow(content);

      })
    );

    this.subscription.add(
      this.newPollingResponseProcessor.artifactData$.subscribe(data => {

        if (this.shouldBlockUIDesignData('artifacts')) {
          return;
        }

        if (data === null) {
        }

        this.newArtifactData = data;
        this.updateArtifactsWindow(data);
      })
    );

    this.subscription.add(
      this.newPollingResponseProcessor.fileList$.subscribe(files => {

        if (this.isUIDesignMode$.value) {
          return;
        }

        this.newFileList = files;
      })
    );

    this.subscription.add(
      this.newPollingResponseProcessor.codeFiles$.subscribe(files => {

        if (this.isUIDesignMode$.value) {
          return;
        }

        this.newCodeFiles = files;
        this.updateCodeViewer(files);
      })
    );

    this.subscription.add(
      this.newPollingResponseProcessor.previewUrl$.subscribe(url => {

        if (this.isUIDesignMode$.value) {
          return;
        }

        this.updatePreviewWindow(url);

        if (url && url.trim() !== '' && url !== 'ERROR_DEPLOYMENT_FAILED') {

          this.hasNewPollingResponseUrl = true;

          this.isNewPreviewEnabled = true;

          this.newPreviewUrl = url;

          this.updatePreviewWindow(url);

          // ENHANCEMENT: Automatic tab switching for polling-based preview URL
          this.handleAutomaticPreviewTabSwitch();
        } else if (url === 'ERROR_DEPLOYMENT_FAILED') {
          this.showDeploymentErrorInPreview();
        } else {
        }
      })
    );

    this.subscription.add(
      this.newPollingResponseProcessor.projectInfo$.subscribe(projectInfo => {

        if (this.isUIDesignMode$.value) {
          return;
        }

        this.currentProjectInfo = projectInfo;
        this.updateProjectInfo(projectInfo);
      })
    );

    this.subscription.add(
      this.codeSharingService.generatedCode$.subscribe(generatedCode => {
        if (generatedCode && this.isCodeGenerationComplete) {
          this.handleCodeUpdate(generatedCode);
        }
      })
    );
  }

  private subscribeToSSEDataProcessor(): void {

    if (!this.sseDataProcessorSubscription) {
      this.sseDataProcessorSubscription = new Subscription();
    }

    this.sseDataProcessorSubscription.add(
      this.sseDataProcessor.codeFiles$.pipe(
        takeUntil(this.destroy$),
        filter(codeFiles => {

          const shouldProcess = codeFiles && codeFiles.length > 0 &&
                               !this.isUIDesignMode$.value &&
                               !this.isRegenerationInProgress$.value;
          if (!shouldProcess && codeFiles && codeFiles.length > 0) {
            if (this.isUIDesignMode$.value) {
            } else if (this.isRegenerationInProgress$.value) {
            }
          }
          return shouldProcess;
        })
      ).subscribe(codeFiles => {

        const fileModels = codeFiles.map(file => ({
          name: file.path,
          type: 'file' as const,
          content: file.code,
        }));

        this.generatedCode = fileModels;
        this.isCodeGenerationComplete = true;
        this.updateCodeViewer(codeFiles);

        // ENHANCEMENT: Automatic tab switching when code files are received
        this.handleAutomaticCodeTabSwitch();

        this.cdr.markForCheck();
      })
    );

    this.sseDataProcessorSubscription.add(
      this.sseDataProcessor.artifactData$.pipe(
        takeUntil(this.destroy$),
        filter(artifactData => {

          const shouldProcess = !this.isUIDesignMode$.value && !this.isRegenerationInProgress$.value;
          if (!shouldProcess && artifactData) {
            if (this.isUIDesignMode$.value) {
            } else if (this.isRegenerationInProgress$.value) {
            }
          }
          return shouldProcess;
        })
      ).subscribe(artifactData => {
        if (artifactData) {
          this.newArtifactData = artifactData;
          this.updateArtifactsWindow(artifactData);
          this.cdr.markForCheck();
        }
      })
    );

    this.sseDataProcessorSubscription.add(
      this.sseDataProcessor.previewUrl$.pipe(
        takeUntil(this.destroy$),
        filter(() => !this.isUIDesignMode$.value && !this.isRegenerationInProgress$.value)
      ).subscribe(url => {
        if (url && url.trim() !== '' && url !== 'ERROR_DEPLOYMENT_FAILED') {
          this.updatePreviewWindow(url);

          // ENHANCEMENT: Automatic tab switching when preview URL is received
          this.handleAutomaticPreviewTabSwitch();

          this.cdr.markForCheck();
        }
      })
    );

  }

  private handleNewProgressChange(progress: ProgressState | null): void {
    if (!progress) return;

    this.checkStoredLayoutForDisplay(progress);

    switch (progress) {
      case 'OVERVIEW':
        this.handleProjectOverviewState();
        break;
      case 'SEED_PROJECT_INITIALIZED':
        this.handleSeedProjectInitializedState();
        break;
      case 'FILE_QUEUE':
        this.handleFileQueueState();
        break;
      case 'Design_System Analyzed':
        this.handleDesignSystemState();
        break;
      case 'COMPONENTS_CREATED':
        this.handleComponentsCreatedState();
        break;
      case 'Layout Analyzed':
      case 'LAYOUT_ANALYZED':
        this.handleLayoutAnalyzedState();
        break;
      case 'PAGES_GENERATED':
        this.handlePagesGeneratedState();
        break;
      case 'BUILD':
        this.handleBuildState();
        break;
      case 'DEPLOY':
      case 'deployed':
        this.handleDeployedState();
        break;
    }

    this.previousProgressState = this.currentProgressState;
    this.currentProgressState = progress;
    this.cdr.detectChanges();
  }

  private checkStoredLayoutForDisplay(currentProgress: ProgressState): void {

    if (
      this.detectedLayoutFromPrevMetadata &&
      this.previousProgressState === 'LAYOUT_ANALYZED' &&
      currentProgress !== 'LAYOUT_ANALYZED' &&
      currentProgress !== 'Layout Analyzed' &&
      !this.shouldShowLayoutArtifact
    ) {

      const existingLayoutArtifact = this.artifactsData.find(item => item.name === 'Layout Analyzed');
      if (existingLayoutArtifact) {
        return;
      }

      const layoutKey = this.detectedLayoutFromPrevMetadata;
      const layoutImageUrl = `assets/images/layout-${layoutKey}.png`;
      const layoutName = this.layoutMapping[layoutKey];

      const artifactItem = {
        name: 'Layout Analyzed',
        type: 'image',
        content: layoutImageUrl,
      };

      this.artifactsData.push(artifactItem);

      this.ensureLogsFileAtBottom();

      this.layoutAnalyzedData = [
        {
          key: layoutKey,
          name: layoutName,
          imageUrl: layoutImageUrl,
        },
      ];

      this.loadedArtifacts.add('Layout Analyzed');
      this.hasLayoutAnalyzed = true;
      this.shouldShowLayoutArtifact = true;

      this.enableArtifactsTabIfNeeded();

    }
  }

  private handleNewStatusChange(status: StatusType | null): void {
    if (!status) return;

    switch (status) {
      case 'IN_PROGRESS':
        this.showProgressIndicator();
        break;
      case 'COMPLETED':
        this.showCompletionState();
        break;
      case 'FAILED':
        this.showErrorState();
        break;
    }

    this.pollingStatus = status;
    this.cdr.detectChanges();
  }

  private updateStepperDescription(description: string): void {
    this.lastProgressDescription = description;
    this.cdr.detectChanges();
  }

  private updatePromptBarEnabledState(): void {
    const currentProgress = this.currentProgress;
    const currentStatus = this.currentStatus;

    const shouldEnable =
      currentProgress === 'DEPLOY' && (currentStatus === 'COMPLETED' || currentStatus === 'FAILED');

    if (this.isPromptBarEnabled !== shouldEnable) {
      this.isPromptBarEnabled = shouldEnable;

      this.cdr.detectChanges();
    }
  }

  private updateLogWindow(content: string): void {
    if (content) {

      const timestamp = new Date().toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      });

      const logMessage = `${timestamp} - INFO - ${content}`;
      this.logMessages = [...this.logMessages, logMessage];
      this.hasLogs = true;
      this.isLogsTabEnabled = true;

      this.autoEnableLogsTabIfNeeded();

      this.enableArtifactsTabIfNeeded();

      this.cdr.detectChanges();
    }
  }

  private autoEnableLogsTabIfNeeded(): void {

    if (!this.logsTabAutoEnabled && !this.userSelectedTab && this.hasLogs) {

      this.logsTabAutoEnabled = true;

      this.autoSwitchToLogsView();
    }
  }

  private autoSwitchToLogsView(): void {

    if (!this.hasLogs) {
      return;
    }

    this.isCodeActive$.next(false);
    this.isPreviewActive$.next(false);

    this.isArtifactsActive$.next(false);

    this.currentView$.next('logs');

    this.isLoading$.next(false);

    if (this.formattedLogMessages.length > 0) {

      setTimeout(() => {
        this.scrollLogsToBottom();
      }, 0);
    }

  }

  private updateProjectOverviewArtifact(content: string): void {

    const projectOverviewIndex = this.artifactsData.findIndex(
      item => item.name === 'Project Overview'
    );

    if (projectOverviewIndex !== -1) {

      this.artifactsData[projectOverviewIndex].content = content;
    } else {

    }

    this.isArtifactsTabEnabled = true;
    this.cdr.detectChanges();
  }

  private updateDesignSystemArtifact(artifactData: any): void {

    const designSystemIndex = this.artifactsData.findIndex(item => item.name === 'Design System');

    if (designSystemIndex !== -1) {

      this.artifactsData[designSystemIndex].content = artifactData.tokens || artifactData.content;
    } else {

      this.artifactsData.push({
        name: 'Design System',
        type: 'component',
        content: artifactData.tokens || artifactData.content,
      });

      this.ensureLogsFileAtBottom();
    }

    this.isArtifactsTabEnabled = true;
    this.cdr.detectChanges();
  }

  private updateArtifactsWindow(artifactData: any): void {
    if (!artifactData) {

      if (this.artifactsData.length === 0 && this.persistentArtifacts.size > 0) {
        this.restorePersistentArtifacts();
      }
      return;
    }

    this.processArtifactData(artifactData);

    switch (artifactData.type) {
      case 'readme':
        this.displayReadmeContent(artifactData.content);
        break;
      case 'layout':
        this.displayLayoutContent(artifactData.layoutCode);
        break;
      case 'design-tokens':
        this.displayDesignTokens(artifactData.tokens);
        break;
    }

    this.isArtifactsTabEnabled = true;
    this.cdr.detectChanges();
  }

  private updateCodeViewer(files: FileData[]): void {
    if (files && files.length > 0) {

      const fileModels: FileModel[] = files.map(file => ({
        name: file.path,
        type: 'file',
        content: file.code,
        fileName: file.path,
      }));

      this.files$.next(fileModels);
      this.isCodeTabEnabled = true;
      this.isCodeGenerationComplete = true;
      this.cdr.detectChanges();
    }
  }

  private handleCodeUpdate(generatedCode: any): void {
    try {

      if (Array.isArray(generatedCode)) {

        const fileModels: FileModel[] = generatedCode.map(file => ({
          name: file.fileName || file.name || 'unknown.txt',
          type: 'file',
          content: file.content || '',
          fileName: file.fileName || file.name || 'unknown.txt',
        }));

        this.files$.next(fileModels);
      } else if (typeof generatedCode === 'object' && generatedCode !== null) {

        const fileModels: FileModel[] = Object.entries(generatedCode).map(([path, content]) => ({
          name: path,
          type: 'file',
          content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
          fileName: path,
        }));

        this.files$.next(fileModels);
      }

      this.cdr.detectChanges();

      if (this.codeViewer) {
        setTimeout(() => {
          this.codeViewer.refreshOpenFiles();
        }, 100);
      }
    } catch (error) {
    }
  }

  private updatePreviewWindow(url: string): void {

    if (url && url.trim() !== '' && this.isNewPreviewEnabled) {

      this.hasNewPollingResponseUrl = true;

      this.processUrlForIframe(url.trim());
    } else if (url === 'ERROR_DEPLOYMENT_FAILED') {
      this.showDeploymentErrorInPreview();
    } else {

      this.isIframeReady$.next(false);
      this.isUrlValidated$.next(false);
      this.isUrlAvailable$.next(false);

      this.isPreviewTabEnabled = false;
      this.previewIcon$.next('bi-code-slash');
      this.urlSafe = undefined;
    }

    this.cdr.detectChanges();
  }

  private isValidPreviewUrl(url: string): boolean {
    try {

      if (!url || typeof url !== 'string' || url.trim() === '') {
        this.urlValidationError$.next('URL is empty or invalid');
        return false;
      }

      const urlObject = new URL(url.trim());

      const isValidProtocol = urlObject.protocol === 'http:' || urlObject.protocol === 'https:';

      if (!isValidProtocol) {
        this.urlValidationError$.next(
          `Invalid protocol: ${urlObject.protocol}. Only HTTP and HTTPS are allowed.`
        );
        return false;
      }

      if (!urlObject.hostname || urlObject.hostname.trim() === '') {
        this.urlValidationError$.next('URL hostname is missing or empty');
        return false;
      }

      this.urlValidationError$.next('');
      return true;
    } catch (error) {
      this.urlValidationError$.next(error instanceof Error ? error.message : 'Invalid URL format');
      return false;
    }
  }

  private processUrlForIframe(url: string): void {

    this.isIframeReady$.next(false);
    this.isUrlValidated$.next(false);
    this.isUrlAvailable$.next(false);

    if (!this.isValidPreviewUrl(url)) {
      return;
    }
    this.isUrlValidated$.next(true);

    this.checkUrlAvailability(url.trim())
      .then(isAvailable => {

        if (isAvailable) {
          this.isUrlAvailable$.next(true);

          this.prepareUrlForIframe(url.trim());
        } else {
          this.urlValidationError$.next('URL is not accessible');
        }
      })
      .catch(_error => {
        this.urlValidationError$.next('Error checking URL availability');
      });
  }

  private async checkUrlAvailability(url: string): Promise<boolean> {
    try {

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000);

      try {
        await fetch(url, {
          method: 'HEAD',
          signal: controller.signal,
          mode: 'no-cors',
        });

        clearTimeout(timeoutId);

        return true;
      } catch (fetchError) {
        clearTimeout(timeoutId);

        if (fetchError instanceof Error && fetchError.name === 'TypeError') {
          return true;
        }

        throw fetchError;
      }
    } catch (error) {

      return true;
    }
  }

  private prepareUrlForIframe(url: string): void {

    try {

      const cleanedUrl = this.cleanUrl(url);

      this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl(cleanedUrl);

      this.deployedUrl$.next(url);

      this.isIframeReady$.next(true);

      this.isPreviewTabEnabled = true;
      this.isPreviewLoading$.next(false);
      this.previewIcon$.next('bi-eye');
      this.previewError$.next(false);

      this.cdr.detectChanges();
    } catch (error) {
      this.urlValidationError$.next('Error preparing URL for iframe');
    }
  }

  private updateProjectInfo(projectInfo: ProjectInfo | null): void {
    if (!projectInfo) {
      return;
    }

    if (projectInfo.name && projectInfo.name.trim() !== '') {
      this.projectName = projectInfo.name;
      this.isProjectNameLoading = false;
    }

    const projectOverviewIndex = this.artifactsData.findIndex(
      item => item.name === 'Project Overview'
    );
    if (projectOverviewIndex !== -1) {

      const currentContent = this.artifactsData[projectOverviewIndex].content;
      const updatedContent = currentContent.replace(/# .*/, `# ${projectInfo.name}`);
      this.artifactsData[projectOverviewIndex].content = updatedContent;
    }

    this.cdr.detectChanges();
  }

  private handleProjectOverviewState(): void {

    this.isArtifactsTabEnabled = this.newPollingResponseProcessor.isArtifactsTabEnabled();
  }

  private handleSeedProjectInitializedState(): void {

  }

  private handleFileQueueState(): void {

  }

  private handleComponentsCreatedState(): void {

  }

  private handlePagesGeneratedState(): void {

  }

  private handleLayoutAnalyzedState(): void {
    this.hasLayoutAnalyzed = true;
    this.isArtifactsTabEnabled = this.newPollingResponseProcessor.isArtifactsTabEnabled();
  }

  private handleDesignSystemState(): void {
    this.hasDesignSystem = true;
    this.isArtifactsTabEnabled = this.newPollingResponseProcessor.isArtifactsTabEnabled();
  }

  private handleBuildState(): void {

    if (!this.subscription) {
      this.subscription = new Subscription();
    }

    this.subscription.add(
      this.newPollingResponseProcessor.currentStatus$.subscribe(status => {
        const currentProgress = this.newPollingResponseProcessor.getCurrentProgress();
        if (currentProgress === 'BUILD' && (status === 'IN_PROGRESS' || status === 'COMPLETED')) {

          this.isCodeTabEnabled = this.newPollingResponseProcessor.isCodeTabEnabled();
        }
      })
    );

    this.subscription.add(
      combineLatest([
        this.newPollingResponseProcessor.codeFiles$,
        this.newPollingResponseProcessor.currentGenerationType$
      ]).subscribe(([files, _generationType]) => {
        if (files && files.length > 0) {

          const fileModels = files.map(file => {

            const fullPath = file.path;

            const language = this.getLanguageFromPath(file.path);

            return {
              name: fullPath,
              language: language,
              content: file.code,
              path: fullPath,
              type: 'file' as const,
            };
          });

          this.files$.next(fileModels);

          if (fileModels.length > 0) {
            this.setCodeTabEnabled('View generated code');
          }

          this.isCodeTabEnabled = true;

          // ENHANCEMENT: Use new automatic tab switching instead of direct toggle
          this.handleAutomaticCodeTabSwitch();
        }
      })
    );
  }

  private handleDeployedState(): void {

    if (!this.subscription) {
      this.subscription = new Subscription();
    }

    this.subscription.add(
      this.newPollingResponseProcessor.previewUrl$.subscribe(url => {

        if (url && url.trim() !== '') {

          if (url === 'ERROR_DEPLOYMENT_FAILED') {
            this.showDeploymentErrorInPreview();

            this.setPreviewTabError('Deployment failed', 'View error details');
          } else {

            this.newPreviewUrl = '';
            this.urlSafe = undefined;

            this.newPreviewUrl = url.trim();
            this.deployedUrl$.next(url.trim());

            this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl(url.trim());

            this.setPreviewTabEnabled('View deployed application preview');

            this.isPreviewTabEnabled = true;
            this.previewError$.next(false);
            this.isLoading$.next(false);
            this.isPreviewLoading$.next(false);

            this.togglePreviewView();

          }
        } else if (url === '') {

          this.newPreviewUrl = '';
          this.urlSafe = undefined;
          this.deployedUrl$.next('');
        }
      })
    );

    this.subscription.add(
      this.newPollingResponseProcessor.currentStatus$.subscribe(status => {
        const currentProgress = this.newPollingResponseProcessor.getCurrentProgress();
        if (currentProgress === 'DEPLOY') {
          if (status === 'COMPLETED') {

            if (this.isRegenerationInProgress$.value) {

            }

          } else if (status === 'IN_PROGRESS') {

            this.clearPreviousDeployment();
          }
        }
      })
    );
  }

  private clearPreviousDeployment(): void {
    this.newPreviewUrl = '';
    this.urlSafe = undefined;
    this.deployedUrl$.next('');
    this.previewError$.next(false);
  }

  private showDeploymentErrorInPreview(): void {
    this.previewError$.next(true);
    this.isPreviewTabEnabled = true;

    // ENHANCEMENT: Automatic tab switching for FAILED status/progress
    this.handleAutomaticFailedTabSwitch();
  }

  get isNewArtifactsTabEnabled(): boolean {
    return this.newPollingResponseProcessor.isArtifactsTabEnabled();
  }

  get isNewCodeTabEnabled(): boolean {

    return true;
  }

  get isNewLogsTabEnabled(): boolean {
    return true;
  }

  get isNewPreviewTabEnabled(): boolean {

    return true;
  }

  get isPreviewTabInteractive(): boolean {
    const currentState = this.previewTabState$.value;
    return currentState.isEnabled && !currentState.isLoading && !currentState.hasError;
  }

  get isPreviewContentReady(): boolean {

    const hasDeployedUrl = !!this.deployedUrl$.value || !!this.previewUrl;
    const isDeployCompleted = this.newPollingResponseProcessor.isPreviewEnabled();
    return hasDeployedUrl || isDeployCompleted || this.isFailedState;
  }

  /**
   * ENHANCEMENT: Check if code tab content can be displayed
   * Returns true when either code generation is complete OR template files are available
   */
  get canShowCodeTabContent(): boolean {
    return this.isCodeGenerationComplete || this.isTemplateFilesAvailable;
  }

  get previewTabName(): Observable<string> {
    return this.previewTabName$.asObservable();
  }

  get previewTabState(): Observable<PreviewTabState> {
    return this.previewTabState$.asObservable();
  }

  get previewTabStatus(): Observable<PreviewTabStatus> {
    return this.previewTabStatus$.asObservable();
  }

  get currentPreviewTabState(): PreviewTabState {
    return this.previewTabState$.value;
  }

  get currentPreviewTabStatus(): PreviewTabStatus {
    return this.previewTabStatus$.value;
  }

  get codeTabState(): Observable<CodeTabState> {
    return this.codeTabState$.asObservable();
  }

  get currentCodeTabState(): CodeTabState {
    return this.codeTabState$.value;
  }

  get isInFailedState(): boolean {
    return this.isFailedState;
  }

  get regenerationInProgress$(): Observable<boolean> {
    return this.isRegenerationInProgress$.asObservable();
  }

  get codeGenerationLoading$(): Observable<boolean> {
    return this.isCodeGenerationLoading$.asObservable();
  }

  // ENHANCEMENT: Download loading state getter for server-side download
  get isDownloadLoading(): Observable<boolean> {
    return this.isDownloadLoading$.asObservable();
  }

  // ENHANCEMENT: Clone in VS Code loading state getter
  get isCloneLoading(): Observable<boolean> {
    return this.isCloneLoading$.asObservable();
  }

  private updatePreviewTabState(updates: Partial<PreviewTabState>): void {
    const currentState = this.previewTabState$.value;
    const newState = { ...currentState, ...updates };
    this.previewTabState$.next(newState);

    if (newState.hasError) {
      this.previewTabStatus$.next(PreviewTabStatus.ERROR);
    } else if (newState.isLoading) {
      this.previewTabStatus$.next(PreviewTabStatus.LOADING);
    } else if (newState.isEnabled) {
      this.previewTabStatus$.next(PreviewTabStatus.ENABLED);
    } else {
      this.previewTabStatus$.next(PreviewTabStatus.DISABLED);
    }
  }

  private setPreviewTabDisabled(message: string = 'Preview will be available once the application is deployed'): void {
    this.updatePreviewTabState({
      isEnabled: false,
      isLoading: false,
      hasError: false,
      tooltipMessage: message
    });
  }

  private setPreviewTabLoading(message: string = 'Preparing preview...'): void {

    this.previewTabName$.next('Preview');

    const isRetryOperation = message.toLowerCase().includes('retry');

    this.updatePreviewTabState({
      isEnabled: isRetryOperation ? true : false,
      isLoading: true,
      hasError: false,
      loadingMessage: message,
      tooltipMessage: isRetryOperation ? 'Retrying - preview will update when ready' : 'Preview is being prepared'
    });

    if (isRetryOperation) {
    }
  }

  private setPreviewTabEnabled(message: string = 'View deployed application preview'): void {

    this.previewTabName$.next('Preview');

    this.updatePreviewTabState({
      isEnabled: true,
      isLoading: false,
      hasError: false,
      tooltipMessage: message
    });
  }

  private setPreviewTabError(errorMessage: string = 'An error occurred', tooltipMessage: string = 'View error details'): void {

    this.previewTabName$.next('Error');

    // ENHANCED: Extract detailed error message using same logic as /build/error API endpoint
    const detailedErrorMessage = this.extractDetailedErrorMessageForPreview(errorMessage);

    this.updatePreviewTabState({
      isEnabled: true,
      isLoading: false,
      hasError: true,
      errorMessage: detailedErrorMessage,
      tooltipMessage
    });

    // ENHANCED: Update error description with detailed message for error page display
    this.errorDescription$.next(detailedErrorMessage);
  }

  private updatePreviewTabStateForProgress(progress: ProgressState | null): void {
    if (!progress) return;

    const progressUpper = progress.toUpperCase();
    const currentStatus = this.newPollingResponseProcessor.getCurrentStatus();

    switch (progressUpper) {
      case 'OVERVIEW':
      case 'SEED_PROJECT_INITIALIZED':
      case 'FILE_QUEUE':
      case 'DESIGN_SYSTEM_MAPPED':
      case 'COMPONENTS_CREATED':
      case 'LAYOUT_ANALYZED':
      case 'PAGES_GENERATED':
        this.setPreviewTabLoading('Generating your application...');
        break;

      case 'BUILD_STARTED':
      case 'BUILD':
        this.setPreviewTabLoading('Building your application...');
        break;

      case 'BUILD_SUCCEEDED':
      case 'FILES_GENERATED':
        this.setPreviewTabLoading('Preparing deployment...');
        break;

      case 'DEPLOY':
        if (currentStatus === 'IN_PROGRESS') {
          this.setPreviewTabLoading('Deploying your application...');
        } else if (currentStatus === 'COMPLETED') {

          this.setPreviewTabLoading('Finalizing preview...');
        } else if (currentStatus === 'FAILED') {
          this.setPreviewTabError('Deployment failed', 'View error details');
        }
        break;

      case 'DEPLOYED':
        if (currentStatus === 'COMPLETED') {

          if (this.isPreviewContentReady) {
            this.setPreviewTabEnabled('View deployed application preview');
          } else {
            this.setPreviewTabLoading('Preparing preview...');
          }
        }
        break;

      default:

        if (!this.currentPreviewTabState.isEnabled && !this.currentPreviewTabState.hasError) {
          this.setPreviewTabLoading('Processing...');
        }
        break;
    }
  }

  private updatePreviewTabStateForRegeneration(stateUpdate: any): void {
    if (!stateUpdate.phase) return;

    switch (stateUpdate.phase) {
      case 'CODE_GENERATION':
        if (stateUpdate.status === 'IN_PROGRESS') {
          this.setPreviewTabDisabled('Code generation in progress...');
        } else if (stateUpdate.status === 'COMPLETED') {
          this.setPreviewTabDisabled('Building application...');
        }
        break;

      case 'BUILD':
        if (stateUpdate.status === 'IN_PROGRESS') {
          this.setPreviewTabDisabled('Building application...');
        } else if (stateUpdate.status === 'COMPLETED') {
          this.setPreviewTabDisabled('Preparing deployment...');
        }
        break;

      case 'DEPLOY':
        if (stateUpdate.status === 'IN_PROGRESS') {
          this.setPreviewTabDisabled('Deploying application...');
        } else if (stateUpdate.status === 'COMPLETED') {

          this.setPreviewTabEnabled('View regenerated application');
        } else if (stateUpdate.status === 'FAILED') {
          this.setPreviewTabError('Regeneration failed', 'View error details');
        }
        break;

      default:

        this.setPreviewTabDisabled('Processing regeneration...');
        break;
    }

    this.updateCodeTabStateForRegeneration(stateUpdate);
  }

  private handlePreviewError(errorType: 'deployment' | 'loading' | 'network' | 'timeout', errorMessage?: string): void {
    let message = 'An error occurred';
    let tooltip = 'View error details';

    switch (errorType) {
      case 'deployment':
        message = 'Deployment failed';
        tooltip = 'Click to view deployment error details';
        break;
      case 'loading':
        message = 'Preview loading failed';
        tooltip = 'Click to retry loading preview';
        break;
      case 'network':
        message = 'Network error';
        tooltip = 'Check your connection and try again';
        break;
      case 'timeout':
        message = 'Preview timeout';
        tooltip = 'Preview took too long to load';
        break;
    }

    if (errorMessage) {
      message = errorMessage;
    }

    this.setPreviewTabError(message, tooltip);
  }

  /**
   * ENHANCED: Extract detailed error message for preview display using same logic as /build/error API endpoint
   * This ensures consistency between error page display and API error reporting
   * CRITICAL: Uses same extraction logic as ErrorReportingService.reportBuildError user_request parameter
   */
  private extractDetailedErrorMessageForPreview(errorMessage: string): string {
    // Fallback to original error message if it contains useful information
    if (errorMessage && errorMessage.trim() !== '' && errorMessage !== 'An error occurred') {
      // Check if the error message already contains detailed information
      const trimmedMessage = errorMessage.trim();
      if (trimmedMessage.length > 20) { // Assume longer messages are more detailed
        return trimmedMessage;
      }
    }

    // Try to get detailed error message from regeneration integration service
    try {
      // Check if regeneration integration service has error state with detailed message
      // Use the observable to get current error state
      let currentErrorState: any = null;
      this.regenerationIntegrationService?.errorState$?.pipe(take(1)).subscribe(state => {
        currentErrorState = state;
      });

      if (currentErrorState?.hasError && currentErrorState.errorMessage) {
        return currentErrorState.errorMessage.trim();
      }
    } catch (error) {
    }

    // Default error message
    return 'An error occurred during processing. Please try again.';
  }

  public retryPreviewLoading(): void {

    this.previewError$.next(false);
    this.isFailedState = false;

    this.setPreviewTabLoading('Retrying preview...');

    if (this.previewUrl || this.deployedUrl$.value) {
      const url = this.previewUrl || this.deployedUrl$.value;
      if (url) {
        this.validateAndPrepareUrl(url);
      } else {
        this.setPreviewTabDisabled('No preview URL available. Please regenerate your application.');
      }
    } else {

      this.setPreviewTabDisabled('No preview URL available. Please regenerate your application.');
    }
  }

  private async validateAndPrepareUrl(url: string): Promise<void> {
    try {
      this.setPreviewTabLoading('Validating preview URL...');

      if (!url || !this.isValidUrl(url)) {
        throw new Error('Invalid URL format');
      }

      const isAvailable = await this.checkUrlAvailabilityWithTimeout(url, 60000);

      if (!isAvailable) {
        throw new Error('Preview URL is not accessible');
      }

      this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl(url);

      this.setPreviewTabEnabled('View deployed application preview');

    } catch (error) {

      if (error instanceof Error) {
        if (error.message.includes('timeout')) {
          this.handlePreviewError('timeout', 'Preview URL took too long to respond');
        } else if (error.message.includes('network') || error.message.includes('accessible')) {
          this.handlePreviewError('network', 'Preview URL is not accessible');
        } else {
          this.handlePreviewError('loading', error.message);
        }
      } else {
        this.handlePreviewError('loading', 'Unknown error occurred while preparing preview');
      }
    }
  }

  private async checkUrlAvailabilityWithTimeout(url: string, timeoutMs: number): Promise<boolean> {
    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        resolve(false);
      }, timeoutMs);

      this.checkUrlAvailability(url).then((isAvailable) => {
        clearTimeout(timeout);
        resolve(isAvailable);
      }).catch(() => {
        clearTimeout(timeout);
        resolve(false);
      });
    });
  }

  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  private updateCodeTabState(updates: Partial<CodeTabState>): void {
    const currentState = this.codeTabState$.value;
    const newState = { ...currentState, ...updates };
    this.codeTabState$.next(newState);

  }

  private setCodeTabDisabled(message: string = 'Code will be available once generated'): void {
    // ENHANCEMENT: Respect permanent enablement flag - once enabled after SEED_PROJECT_INITIALIZED, never disable
    if (this.isCodeTabPermanentlyEnabled) {
      console.log('🔒 Code tab is permanently enabled - ignoring disable request:', message);
      return;
    }

    this.updateCodeTabState({
      isEnabled: false,
      isLoading: false,
      hasError: false,
      tooltipMessage: message
    });
  }

  private setCodeTabLoading(message: string = 'Generating code...'): void {
    this.updateCodeTabState({
      isEnabled: false,
      isLoading: true,
      hasError: false,
      loadingMessage: message,
      tooltipMessage: 'Code is being generated'
    });
  }

  private setCodeTabEnabled(message: string = 'View generated code'): void {
    this.updateCodeTabState({
      isEnabled: true,
      isLoading: false,
      hasError: false,
      tooltipMessage: message
    });
  }

  private setCodeTabError(errorMessage: string = 'Code generation failed', tooltipMessage: string = 'View error details'): void {
    this.updateCodeTabState({
      isEnabled: true,
      isLoading: false,
      hasError: true,
      errorMessage,
      tooltipMessage
    });
  }

  private updateCodeTabStateForRegeneration(stateUpdate: any): void {
    if (!stateUpdate.phase) return;

    const shouldEnableCodeTab = stateUpdate.phase === 'BUILD' && stateUpdate.status === 'IN_PROGRESS';

    switch (stateUpdate.phase) {
      case 'CODE_GENERATION':
        if (stateUpdate.status === 'IN_PROGRESS') {
          this.setCodeTabLoading('Generating code...');
        } else if (stateUpdate.status === 'COMPLETED') {

          this.setCodeTabDisabled('Code will be available during build phase');
        } else if (stateUpdate.status === 'FAILED') {
          this.setCodeTabError('Code generation failed', 'View error details');
        }
        break;

      case 'SEED_PROJECT_INITIALIZED':
        if (stateUpdate.status === 'COMPLETED') {
          // ENHANCEMENT: Set permanent enablement flag - code tab should never be disabled after this point
          this.isCodeTabPermanentlyEnabled = true;
          this.setCodeTabEnabled('View seed project template');
          console.log('🔒 Code tab permanently enabled after SEED_PROJECT_INITIALIZED + COMPLETED');
        } else if (stateUpdate.status === 'FAILED') {
          this.setCodeTabError('Template loading failed', 'View error details');
        }
        break;

      case 'BUILD':
        if (stateUpdate.status === 'IN_PROGRESS') {

          this.setCodeTabEnabled('View generated code');
        } else if (stateUpdate.status === 'COMPLETED') {
          this.setCodeTabEnabled('View generated code');
        } else if (stateUpdate.status === 'FAILED') {
          this.setCodeTabError('Build failed', 'View error details');
        }
        break;

      case 'DEPLOY':
        if (stateUpdate.status === 'IN_PROGRESS') {

          this.setCodeTabEnabled('View generated code');
        } else if (stateUpdate.status === 'COMPLETED') {
          this.setCodeTabEnabled('View generated code');
        } else if (stateUpdate.status === 'FAILED') {
          this.setCodeTabError('Deployment failed', 'View error details');
        }
        break;

      default:

        if (shouldEnableCodeTab) {
          this.setCodeTabEnabled('View generated code');
        } else if (!this.currentCodeTabState.hasError) {
          this.setCodeTabDisabled('Code will be available during build phase');
        }
        break;
    }
  }

  private updateCodeTabStateForProgress(progress: ProgressState | null): void {
    if (!progress) return;

    const progressUpper = progress.toUpperCase();
    const currentStatus = this.newPollingResponseProcessor.getCurrentStatus();

    const shouldEnableCodeTab = progressUpper === 'BUILD' && currentStatus === 'IN_PROGRESS';

    switch (progressUpper) {
      case 'CODE_GENERATION':
        if (currentStatus === 'IN_PROGRESS') {
          this.setCodeTabLoading('Generating code...');
        } else if (currentStatus === 'COMPLETED') {

          this.setCodeTabDisabled('Code will be available during build phase');
        } else if (currentStatus === 'FAILED') {
          this.setCodeTabError('Code generation failed', 'View error details');
        }
        break;

      case 'SEED_PROJECT_INITIALIZED':
        if (currentStatus === 'COMPLETED') {
          // ENHANCEMENT: Set permanent enablement flag - code tab should never be disabled after this point
          this.isCodeTabPermanentlyEnabled = true;
          this.setCodeTabEnabled('View seed project template');
          console.log('🔒 Code tab permanently enabled after SEED_PROJECT_INITIALIZED + COMPLETED');
        } else if (currentStatus === 'FAILED') {
          this.setCodeTabError('Template loading failed', 'View error details');
        }
        break;

      case 'BUILD':
        if (currentStatus === 'IN_PROGRESS') {

          this.setCodeTabEnabled('View generated code');
        } else if (currentStatus === 'COMPLETED') {
          this.setCodeTabEnabled('View generated code');
        } else if (currentStatus === 'FAILED') {
          this.setCodeTabError('Build failed', 'View error details');
        }
        break;

      case 'DEPLOY':
      case 'DEPLOYED':
        if (currentStatus === 'IN_PROGRESS') {

          this.setCodeTabEnabled('View generated code');
        } else if (currentStatus === 'COMPLETED') {
          this.setCodeTabEnabled('View generated code');
        } else if (currentStatus === 'FAILED') {
          this.setCodeTabError('Deployment failed', 'View error details');
        }
        break;

      default:

        if (shouldEnableCodeTab) {
          this.setCodeTabEnabled('View generated code');
        } else if (!this.currentCodeTabState.hasError) {
          this.setCodeTabDisabled('Code will be available during build phase');
        }
        break;
    }
  }

  private showProgressIndicator(): void {
    this.isLoading$.next(true);
    this.isPreviewLoading$.next(true);
    this.previewError$.next(false);

    this.setPreviewTabLoading('Generating your application...');
  }

  private showCompletionState(): void {
    this.isLoading$.next(false);
    this.isPreviewLoading$.next(false);
    this.previewError$.next(false);
    this.isCodeGenerationComplete = true;

    if (this.isPreviewContentReady) {
      this.setPreviewTabEnabled('View deployed application preview');
    } else {
      this.setPreviewTabDisabled('Deployment in progress...');
    }

  }

  private addGenerationAccordionToChat(): void {

    this.generationVersionCounter++;

    const projectName = this.projectName || 'Untitled Project';

    let generatedFiles: string[] = [];

    if (this.generatedCode && this.generatedCode.length > 0) {
      generatedFiles = this.generatedCode.map((file: any) => file.name || file.path || 'Unknown file');
    }

    else if (this.artifactsData && this.artifactsData.length > 0) {
      generatedFiles = this.artifactsData
        .filter(artifact => artifact.type === 'file' || artifact.type === 'code')
        .map(artifact => artifact.name);
    }

    else {
      this.files$.pipe(take(1)).subscribe(files => {
        if (files && files.length > 0) {
          generatedFiles = files.map(file => file.name || 'Unknown file');
        }
      });
    }

    if (generatedFiles.length === 0) {
      generatedFiles = ['Application files generated'];
    }

    const generationResult: GenerationResult = {
      type: 'success',
      version: this.generationVersionCounter,
      projectName: projectName,
      files: generatedFiles,
      timestamp: new Date()
    };

    if (this.chatWindow) {
      this.chatWindow.addGenerationResult(generationResult);
    } else {
    }
  }

  private addRegenerationGenerationAccordionToChat(codeFiles: FileData[]): void {

    this.generationVersionCounter++;

    const projectName = this.projectName || 'Untitled Project';

    let regeneratedFiles: string[] = [];

    if (codeFiles && codeFiles.length > 0) {
      regeneratedFiles = codeFiles.map(file => file.path || 'Unknown file');
    } else {

      if (this.generatedCode && this.generatedCode.length > 0) {
        regeneratedFiles = this.generatedCode.map((file: any) => file.name || file.path || 'Unknown file');
      } else {
        regeneratedFiles = ['Regenerated application files'];
      }
    }

    const generationResult: GenerationResult = {
      type: 'success',
      version: this.generationVersionCounter,
      projectName: projectName,
      files: regeneratedFiles,
      timestamp: new Date()
    };

    if (this.chatWindow) {
      this.chatWindow.addGenerationResult(generationResult);
    } else {
    }
  }

  private showErrorState(): void {

    this.isLoading$.next(false);
    this.isPreviewLoading$.next(false);
    this.previewError$.next(true);
    this.isFailedState = true;

    // ENHANCED: Always restore persistent artifacts during FAILED events
    // This ensures layout analyzed data remains frozen/persistent even when operations fail
    if (this.persistentArtifacts.size > 0) {
      console.log('🛡️ Restoring persistent artifacts during FAILED state to preserve layout data');
      this.restorePersistentArtifacts();
    } else if (this.artifactsData.length === 0) {
      console.warn('⚠️ No persistent artifacts available to restore during FAILED state');
    }

    this.handleLayoutAnalysisFailed();

    this.previewTabName$.next('Error');

    this.errorDescription$.next('An error occurred during processing. Please try again.');

    this.extractErrorMessageFromProgressDescription();

    this.setPreviewTabError('An error occurred during processing', 'View error details');

    this.isPreviewTabEnabled = true;

    if (!this.userSelectedTab) {
      this.togglePreviewView();

      this.userSelectedTab = false;
    } else {

      this.currentView$.next('preview');
    }

    this.cdr.detectChanges();

  }

  private displayReadmeContent(content: string): void {
    const readmeIndex = this.artifactsData.findIndex(item => item.name === 'README');
    if (readmeIndex !== -1) {
      this.artifactsData[readmeIndex].content = content;
    } else {

    }
  }

  private displayLayoutContent(layoutCode: string): void {

    this.preventDuplicateLayoutAnalyzed();

    if (!layoutCode || !this.layoutMapping[layoutCode]) {
      return;
    }

    const imageUrl = `assets/images/layout-${layoutCode}.png`;
    const layoutName = this.layoutMapping[layoutCode];

    const layoutIndex = this.artifactsData.findIndex(item => item.name === 'Layout Analyzed');
    if (layoutIndex !== -1) {

      this.artifactsData[layoutIndex].content = imageUrl;
    } else {

    }

    this.layoutData = [layoutCode];

    this.layoutAnalyzedData = [
      {
        key: layoutCode,
        name: layoutName,
        imageUrl: imageUrl,
      },
    ];

    this.hasLayoutAnalyzed = true;

    this.stopLayoutAnalyzing();

  }

  private displayDesignTokens(tokens: DesignTokensData): void {
    const designSystemIndex = this.artifactsData.findIndex(item => item.name === 'Design System');
    if (designSystemIndex !== -1) {
      this.artifactsData[designSystemIndex].content = tokens;
    } else {
      this.artifactsData.push({
        name: 'Design System',
        type: 'component',
        content: tokens,
      });

      this.ensureLogsFileAtBottom();
    }
    this.hasDesignSystem = true;
    this.designSystemData = tokens;
  }

  ngAfterViewInit(): void {

    setTimeout(() => {

      if (this.chatWindow) {

        this.chatWindow.showImagePreview = (imageUrl: string, imageName: string = 'Image') => {

          this.showImagePreview(imageUrl, imageName);
        };
      }
    }, 0);

    this.setupPanelWidthObserver();
    this.setupResizerEventListener();
    this.setupCanvasWheelListener();
  }

  private setupCanvasWheelListener(): void {
    if (this.canvasContent?.nativeElement) {

      this.wheelEventListener = (event: WheelEvent) => {
        this.onCanvasWheel(event);
      };

      this.ngZone.runOutsideAngular(() => {
        this.canvasContent.nativeElement.addEventListener('wheel', this.wheelEventListener, {
          passive: false,
          capture: false
        });
      });
    }
  }

  private setupResizerEventListener(): void {

    setTimeout(() => {
      const resizer = document.querySelector('.resizer') as HTMLElement;
      if (resizer) {
        resizer.addEventListener('mousedown', (event: MouseEvent) => {
          this.startResize(event);
        });
      } else {

        setTimeout(() => this.setupResizerEventListener(), 100);
      }
    }, 100);
  }

  private setupPanelWidthObserver(): void {

    if (typeof ResizeObserver !== 'undefined') {
      const resizeObserver = new ResizeObserver(entries => {
        for (const entry of entries) {
          const width = entry.contentRect.width;

          this.shouldHideProjectName$.next(width < 400);
          this.cdr.detectChanges();
        }
      });

      setTimeout(() => {
        const leftPanel = document.querySelector('.awe-leftpanel') as HTMLElement;
        if (leftPanel) {
          resizeObserver.observe(leftPanel);
        }
      }, 100);
    } else {

      const checkPanelWidth = () => {
        const leftPanel = document.querySelector('.awe-leftpanel') as HTMLElement;
        if (leftPanel) {
          const width = leftPanel.offsetWidth;
          this.shouldHideProjectName$.next(width < 400);
          this.cdr.detectChanges();
        }
      };

      window.addEventListener('resize', checkPanelWidth);

      setTimeout(checkPanelWidth, 100);
    }
  }

  private initializeAppState(): void {

    this.subscription = this.appStateService.project$.subscribe(projectState => {

      if (projectState.projectId && projectState.projectId !== this.projectId) {

        if (this.projectId) {

          this.stepperStateService.triggerStepperReset();
        }

        this.projectId = projectState.projectId;

        this.hasAddedInitialGenerationAccordion = false;
        this.generationVersionCounter = 0;

        this.stepperStateService.setCurrentProjectId(this.projectId);
      }

      if (projectState.jobId && projectState.jobId !== this.jobId) {
        this.jobId = projectState.jobId;

      }

      if (projectState.prompt && this.lightMessages.length === 0 && !this.isUIDesignMode$.value) {

        this.lightMessages = [
          { text: projectState.prompt, from: 'user', theme: 'light' },
          {
            text: "I'm generating code based on your request. Please wait while I process your input...",
            from: 'ai',
            theme: 'light',
          },
        ];
      } else if (this.isUIDesignMode$.value) {
      }

      if (this.projectId && this.jobId && !this.isPolling && !this.isUIDesignMode$.value) {

        this.startLayoutAnalyzing();

        this.startStatusMonitoring(this.projectId, this.jobId, 'code generation');
        this.updatePollingStatus(true);

      } else if (this.isUIDesignMode$.value) {
      }
    });

    this.currentView$.next('preview');
    this.isLoading$.next(true);
    this.isPreviewLoading$.next(true);
    this.isPreviewActive$.next(true);
    this.isCodeActive$.next(false);

    this.isLogsTabEnabled = true;
    this.isPreviewTabEnabled = true;
    this.isCodeTabEnabled = false;

    this.subscription.add(
      this.newPollingResponseProcessor.logContent$.subscribe(logContent => {
        if (logContent && logContent.trim() !== '') {

          const logLines = logContent.split('\n').filter(line => line.trim() !== '');

          this.startLogStreaming(logLines);

          this.hasLogs = true;

          this.autoEnableLogsTabIfNeeded();

          this.enableArtifactsTabIfNeeded();

          for (const log of logLines) {
            if (typeof log === 'string' && log.includes('"message"') && log.includes('"data"')) {
              try {

                const jsonStartIndex = log.indexOf('{');
                if (jsonStartIndex !== -1) {
                  const jsonPart = log.substring(jsonStartIndex);
                  const parsedData = JSON.parse(jsonPart);

                  if (parsedData.message && parsedData.data) {

                    const layoutKeys = parsedData.data;

                    this.layoutData = [];

                    if (typeof layoutKeys === 'string') {

                      if (this.layoutMapping[layoutKeys]) {
                        this.layoutData = [layoutKeys];
                      }
                    } else if (Array.isArray(layoutKeys)) {

                      const validKeys = layoutKeys.filter(
                        key => typeof key === 'string' && this.layoutMapping[key]
                      );

                      if (validKeys.length > 0) {
                        this.layoutData = [validKeys[0]];
                      }
                    } else if (typeof layoutKeys === 'object' && layoutKeys !== null) {

                      const validKeys = Object.keys(layoutKeys).filter(
                        key => this.layoutMapping[key]
                      );

                      if (validKeys.length > 0) {
                        this.layoutData = [validKeys[0]];
                      }
                    }

                    if (this.layoutData.length === 0) {
                    } else {

                      if (log.includes('LAYOUT_ANALYZED')) {

                        this.captureLayoutAnalyzedState();
                      } else if (log.includes('DESIGN_SYSTEM_MAPPED')) {

                        this.designSystemData = parsedData.data;

                        this.captureDesignSystemMappedState();
                      }
                    }

                    this.isLayoutLoading = false;
                  }
                }
              } catch (e) {
              }
            }
          }

          this.addLogUpdateToChatWindow(logLines);
        }
      })
    );

    this.subscription.add(
      this.newPollingResponseProcessor.artifactData$.subscribe(artifactData => {
        if (artifactData) {

          if (artifactData.type === 'text') {

            this.updateProjectOverviewArtifact(artifactData.content);
          } else if (artifactData.type === 'json') {

            this.updateDesignSystemArtifact(artifactData);
          }
        }
      })
    );

    this.subscription.add(
      this.newPollingResponseProcessor.codeFiles$.subscribe(codeFiles => {
        if (codeFiles && codeFiles.length > 0) {

          const fileModels = codeFiles.map(file => ({
            name: file.path,
            type: 'file' as const,
            content: file.code,
          }));

          this.generatedCode = fileModels;
          this.isCodeGenerationComplete = true;

          if (this.isCodeGenerationLoading$.value) {
            this.isCodeGenerationLoading$.next(false);
            this.codeRegenerationProgressDescription = '';

            this.createRegenerationSuccessResult(codeFiles);

            const lastMessage = this.getLastMessage();

            if (lastMessage && lastMessage.from === 'ai') {
              const isLastMessageIntro = this.isIntroMessage(lastMessage);

              if (isLastMessageIntro) {

                lastMessage.showLoadingIndicator = false;
                lastMessage.loadingPhase = 'completed';
                lastMessage.mainAPIInProgress = false;

                this.codeGenerationIntroService.generateCompletionMessage(
                  this.lastUserRequest || 'code regeneration',
                  this.getCurrentCodeFiles(),
                  { preservedEdits: 0, updatedFiles: 0, conflicts: 0 }
                ).subscribe(completionMessage => {
                  this.addCompletionMessageWithTypewriter(completionMessage);
                });
              } else {

                this.codeGenerationIntroService.generateCompletionMessage(
                  this.lastUserRequest || 'code regeneration',
                  this.getCurrentCodeFiles(),
                  { preservedEdits: 0, updatedFiles: 0, conflicts: 0 }
                ).subscribe(completionMessage => {
                  lastMessage.text = completionMessage;
                  this.cdr.markForCheck();
                });
              }
            } else {

              this.codeGenerationIntroService.generateCompletionMessage(
                this.lastUserRequest || 'code regeneration',
                this.getCurrentCodeFiles(),
                { preservedEdits: 0, updatedFiles: 0, conflicts: 0 }
              ).subscribe(completionMessage => {
                this.addCompletionMessageWithTypewriter(completionMessage);
              });
            }

            this.cdr.detectChanges();
          }

          this.processCodeFromService(fileModels);
        }
      })
    );

    this.subscription.add(
      this.newPollingResponseProcessor.previewUrl$.subscribe(previewUrl => {
        if (previewUrl && previewUrl.trim() !== '') {
          this.previewUrl = previewUrl;
          this.isNewPreviewEnabled = true;
        }
      })
    );

    this.subscription.add(
      this.newPollingResponseProcessor.currentProgress$.subscribe(progress => {
        if (progress) {
          this.currentProgressState = progress;

        }
      })
    );

    this.subscription.add(
      this.newPollingResponseProcessor.currentStatus$.subscribe(status => {
        if (status) {

          if (status === 'COMPLETED') {

            if (this.isCodeGenerationLoading$.value) {

            }
          } else if (status === 'FAILED') {

            // ENHANCED: Always restore persistent artifacts during FAILED events to preserve layout data
            if (this.persistentArtifacts.size > 0) {
              console.log('🛡️ Restoring persistent artifacts during SSE FAILED status to preserve layout data');
              this.restorePersistentArtifacts();
            }

            if (this.isCodeGenerationLoading$.value) {
              this.isCodeGenerationLoading$.next(false);
              this.isRegenerationInProgress$.next(false);
              this.resetRegenerationCallFlag('SSE FAILED status');
              this.codeRegenerationProgressDescription = '';

              this.createRegenerationErrorResult('Code regeneration failed. Please try again or check the logs for more details.');

              const lastMessage = this.getLastMessage();
              if (lastMessage && lastMessage.from === 'ai') {
                lastMessage.text = 'Code regeneration failed. Please try again or check the logs for more details.';
                lastMessage.showLoadingIndicator = false;
                lastMessage.loadingPhase = 'completed';
                lastMessage.mainAPIInProgress = false;
              }

              this.toastService.error('Code regeneration failed. Please try again.');

              this.cdr.detectChanges();
            }
          }
        }
      })
    );

    this.themeService.themeObservable
      .pipe(takeUntil(this.destroy$))
      .subscribe((theme: 'light' | 'dark') => {
        this.currentTheme$.next(theme);

      });

    this.subscription.add(
      this.newPollingResponseProcessor.artifactData$.subscribe(artifactData => {
        if (artifactData && this.isUIDesignArtifact(artifactData)) {
          this.handleUIDesignResponse(artifactData.content);
        }
      })
    );

    this.generatedCode = this.codeSharingService.getGeneratedCode();

    if (this.generatedCode) {
      this.isCodeGenerationComplete = true;
      this.isPreviewLoading$.next(false);

      this.layoutData = [];

      if (!this.userSelectedTab) {
        this.currentView$.next('preview');
      } else {
      }

      setTimeout(() => {
        const iframe = document.querySelector('.preview-frame') as HTMLIFrameElement;
        if (!iframe) {
        }
      }, 200);

      this.processCodeFromService(this.generatedCode);
    } else {

      if (this.projectId && this.jobId && !this.isPolling && !this.isUIDesignMode$.value) {

        this.startStatusMonitoring(this.projectId, this.jobId, 'code generation');
        this.updatePollingStatus(true);
      } else if (this.isPolling) {
      } else if (this.isUIDesignMode$.value) {
      }
    }

    this.subscription = this.codeSharingService.generatedCode$.subscribe(code => {
      if (code) {
        this.isCodeGenerationComplete = true;
        this.processCodeFromService(code);
      }
    });
  }

  processApiResponse(response: any): void {
    if (!response || !response.details) return;

    const { progress, progress_description, log } = response.details;

    let formattedDescription = progress_description;
    if (progress_description) {

      if (progress_description.includes('LAYOUT_ANALYZED')) {
        formattedDescription = this.formatLayoutAnalyzedDescription(progress_description);
      }

      if (formattedDescription !== progress_description) {

      } else {

      }
    }

    if (progress) {
      this.currentProgressState = progress;

      if (progress.includes('LAYOUT_ANALYZED') || progress.includes('DESIGN_SYSTEM_MAPPED')) {
        this.isLayoutLoading = true;

        if (log && typeof log === 'string') {
          try {

            if (log.includes('{') && log.includes('}')) {

              const parsedLog = JSON.parse(log);

              if (parsedLog.message && parsedLog.data) {

                const layoutKeys = parsedLog.data;

                this.layoutData = [];

                if (progress.includes('LAYOUT_ANALYZED')) {

                } else if (progress.includes('DESIGN_SYSTEM_MAPPED')) {

                  this.designSystemData = parsedLog.data;
                }

                if (typeof layoutKeys === 'string') {

                  if (this.layoutMapping[layoutKeys]) {
                    this.layoutData = [layoutKeys];
                  }
                } else if (Array.isArray(layoutKeys)) {

                  const validKeys = layoutKeys.filter(
                    key => typeof key === 'string' && this.layoutMapping[key]
                  );

                  if (validKeys.length > 0) {
                    this.layoutData = [validKeys[0]];
                  }
                } else if (typeof layoutKeys === 'object' && layoutKeys !== null) {

                  const validKeys = Object.keys(layoutKeys).filter(key => this.layoutMapping[key]);

                  if (validKeys.length > 0) {
                    this.layoutData = [validKeys[0]];
                  }
                }
              }
            }
          } catch (e) {
          }
        }

        if (
          !this.layoutData ||
          this.layoutData.length === 0 ||
          !this.layoutMapping[this.layoutData[0]]
        ) {

          this.isLayoutLoading = true;
          this.isPreviewLoading$.next(true);
          this.isLoading$.next(true);
        } else {

          this.isLayoutLoading = false;
          this.isPreviewLoading$.next(false);
          this.isLoading$.next(false);

          if (progress.includes('LAYOUT_ANALYZED')) {

            this.captureLayoutAnalyzedState();
          } else if (progress.includes('DESIGN_SYSTEM_MAPPED')) {

            this.captureDesignSystemMappedState();
          }
        }
      }
    }

    if (progress_description && (!this.layoutData || this.layoutData.length === 0)) {

      if (progress_description.includes('{') && progress_description.includes('}')) {
        try {
          const jsonStartIndex = progress_description.indexOf('{');
          const jsonEndIndex = progress_description.lastIndexOf('}') + 1;

          if (jsonStartIndex !== -1 && jsonEndIndex !== -1) {
            const jsonPart = progress_description.substring(jsonStartIndex, jsonEndIndex);
            const parsedData = JSON.parse(jsonPart);

            if (parsedData.message && parsedData.data) {

              const layoutKeys = parsedData.data;

              this.layoutData = [];

              if (typeof layoutKeys === 'string') {

                if (this.layoutMapping[layoutKeys]) {
                  this.layoutData = [layoutKeys];
                }
              } else if (Array.isArray(layoutKeys)) {

                this.layoutData = layoutKeys.filter(
                  key => typeof key === 'string' && this.layoutMapping[key]
                );

                if (this.layoutData.length > 0) {
                  this.layoutData.forEach(_key => {

                  });
                }
              } else if (typeof layoutKeys === 'object' && layoutKeys !== null) {

                this.layoutData = Object.keys(layoutKeys).filter(key => this.layoutMapping[key]);

                if (this.layoutData.length > 0) {
                  this.layoutData.forEach(_key => {

                  });

                  if (progress_description.includes('LAYOUT_ANALYZED')) {

                    this.captureLayoutAnalyzedState();
                  } else if (progress_description.includes('DESIGN_SYSTEM_MAPPED')) {

                    this.designSystemData = parsedData.data;

                    this.captureDesignSystemMappedState();
                  }
                }
              }
            }
          }
        } catch (e) {
        }
      }

      if (!this.layoutData || this.layoutData.length === 0) {

      }
    }
  }

  getPageTitle(index: number, layoutKey: string): string {

    const defaultTitles = [
      'Home Page',
      'About Us Page',
      'Products Page',
      'Services Page',
      'Blog Page',
      'Contact Page',
      'Gallery Page',
      'FAQ Page',
    ];

    if (layoutKey && this.layoutMapping[layoutKey]) {

      return defaultTitles[index % defaultTitles.length] + ` (${layoutKey})`;
    }

    return defaultTitles[index % defaultTitles.length] || `Page ${index + 1}`;
  }

  private processCodeFromService(code: any): void {
    this.generatedCode = code;

    const fileModels: FileModel[] = [];

    if (Array.isArray(this.generatedCode)) {

      if (
        this.generatedCode.length > 0 &&
        'fileName' in this.generatedCode[0] &&
        'content' in this.generatedCode[0]
      ) {

        for (const file of this.generatedCode) {
          fileModels.push({
            name: file.fileName,
            type: 'file',
            content:
              typeof file.content === 'string'
                ? file.content
                : JSON.stringify(file.content, null, 2),
          });
        }
      } else {

        for (const item of this.generatedCode) {
          if (typeof item === 'object' && item !== null) {

            const name = item.name || item.fileName || item.path || 'unknown.txt';
            const content = item.content || '';
            fileModels.push({
              name,
              type: 'file',
              content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
            });
          }
        }
      }
    } else if (typeof this.generatedCode === 'string') {

      try {
        const parsedCode = JSON.parse(this.generatedCode);

        if (Array.isArray(parsedCode)) {

          for (const item of parsedCode) {
            if (typeof item === 'object' && item !== null) {
              const name = item.fileName || item.name || item.path || 'unknown.txt';
              const content = item.content || '';
              fileModels.push({
                name,
                type: 'file',
                content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
              });
            }
          }
        } else if (typeof parsedCode === 'object') {

          for (const [filePath, content] of Object.entries(parsedCode)) {
            fileModels.push({
              name: filePath,
              type: 'file',
              content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
            });
          }
        }
      } catch (e) {

        fileModels.push({
          name: 'output.txt',
          type: 'file',
          content: this.generatedCode,
        });
      }
    } else if (typeof this.generatedCode === 'object' && this.generatedCode !== null) {

      for (const [filePath, content] of Object.entries(this.generatedCode)) {
        fileModels.push({
          name: filePath,
          type: 'file',
          content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
        });
      }
    } else {
    }

    const projectState = this.appStateService.getProjectState();
    const technology = projectState.technology || 'angular';

    const sortedFileModels = this.sortFilesByTechnology(fileModels, technology);

    if (sortedFileModels.length > 0) {
      this.files$.next(sortedFileModels);

      this.fileTreePersistenceService.initializeBaseline(sortedFileModels);

      this.setCodeTabEnabled('View generated code');

    } else {
    }

    this.isLoading$.next(false);
    this.isPreviewLoading$.next(false);
    this.isCodeGenerationComplete = true;

    this.layoutData = [];

    if (!this.userSelectedTab) {
      this.togglePreviewView();
    } else {

      this.isCodeActive$.next(false);
      this.isPreviewActive$.next(true);

    }

    setTimeout(() => {
      const iframe = document.querySelector('.preview-frame') as HTMLIFrameElement;
      if (!iframe) {

        if (this.currentView$.value === 'preview') {
          this.currentView$.next('editor');
          setTimeout(() => {
            this.currentView$.next('preview');
          }, 50);
        } else {
        }
      }
    }, 200);

    if (this.codeSharingService.getDeployedUrl()) {
      this.deployedUrl$.next(this.codeSharingService.getDeployedUrl() ?? '');
    }

     this.isPreviewTabEnabled = !!this.deployedUrl$.value;

    if (this.deployedUrl$.value) {
      this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl(this.deployedUrl$.value);
    } else {
      this.urlSafe = undefined;
    }

    this.subscription?.add(
      this.codeSharingService.deployedUrl$.subscribe(url => {
        this.deployedUrl$.next(url ?? '');
        if (url) {
          this.isPreviewLoading$.next(false);
          this.previewIcon$.next('bi-eye');
          this.isPreviewTabEnabled = true;
        }
      })
    );
  }

  toggleView() {
    if (!this.deployedUrl$.value && this.currentView$.value === 'editor') {

      this.isPreviewLoading$.next(true);
      this.previewIcon$.next('bi-arrow-clockwise');
      this.previewError$.next(false);

      const generatedCode = this.codeSharingService.getGeneratedCode();
      if (generatedCode) {
        this.codeGenerationService.getPreviewDeployment(generatedCode).subscribe({
          next: response => {
            if (response.status === 'success' && response.deployedUrl) {
              this.codeSharingService.setDeployedUrl(response.deployedUrl);
              this.currentView$.next('preview');
              this.previewError$.next(false);
            } else {
              this.previewError$.next(true);
              this.currentView$.next('preview');
            }
          },
          error: _error => {
            this.isPreviewLoading$.next(false);
            this.previewIcon$.next('bi-code-slash');
            this.previewError$.next(true);
            this.currentView$.next('preview');
          },
          complete: () => {
            this.isPreviewLoading$.next(false);
          },
        });
      }
    } else {

      const newView = this.currentView$.value === 'editor' ? 'preview' : 'editor';
      this.currentView$.next(newView);
      this.previewIcon$.next(newView === 'editor' ? 'bi-code-slash' : 'bi-eye');
    }
  }

  @HostListener('window:resize')
  onWindowResize() {

    if (!this.isResizing$.value) {

      const leftPanel = document.querySelector('.awe-leftpanel') as HTMLElement;
      const rightPanel = document.querySelector('.awe-rightpanel') as HTMLElement;
      const container = document.querySelector('.awe-splitscreen') as HTMLElement;

      if (leftPanel && rightPanel && container && !this.isLeftPanelCollapsed$.value) {

        const containerWidth = container.offsetWidth;

        const leftWidthStr = this.defaultLeftPanelWidth;
        const rightWidthStr = this.defaultRightPanelWidth;

        const leftWidthPercent = parseFloat(leftWidthStr);
        const rightWidthPercent = parseFloat(rightWidthStr);

        if (!isNaN(leftWidthPercent) && !isNaN(rightWidthPercent)) {

          const minWidthPx = parseInt(this.minWidth$.value || '300', 10);

          if ((containerWidth * leftWidthPercent) / 100 < minWidthPx) {

            const newLeftPercentage = ((minWidthPx / containerWidth) * 100).toFixed(2) + '%';
            const newRightPercentage =
              (((containerWidth - minWidthPx) / containerWidth) * 100).toFixed(2) + '%';

            leftPanel.style.width = newLeftPercentage;
            rightPanel.style.width = newRightPercentage;

            this.defaultLeftPanelWidth = newLeftPercentage;
            this.defaultRightPanelWidth = newRightPercentage;
          }

          else if ((containerWidth * rightWidthPercent) / 100 < minWidthPx) {

            const newRightPercentage = ((minWidthPx / containerWidth) * 100).toFixed(2) + '%';
            const newLeftPercentage =
              (((containerWidth - minWidthPx) / containerWidth) * 100).toFixed(2) + '%';

            leftPanel.style.width = newLeftPercentage;
            rightPanel.style.width = newRightPercentage;

            this.defaultLeftPanelWidth = newLeftPercentage;
            this.defaultRightPanelWidth = newRightPercentage;
          }
        }
      }
    }
  }

  ngOnDestroy() {

    this.clearRegenerationTimeout();

    if (this.subscription) {
      this.subscription.unsubscribe();
      this.subscription = null;
    }

    if (this.sseDataProcessorSubscription) {
      this.sseDataProcessorSubscription.unsubscribe();
      this.sseDataProcessorSubscription = null;
    }

    this.destroy$.next();
    this.destroy$.complete();

    if (this.checkSessionStorageInterval) {
      clearInterval(this.checkSessionStorageInterval);
      this.checkSessionStorageInterval = null;
    }

    if (this.logStreamTimer) {
      clearInterval(this.logStreamTimer);
      this.logStreamTimer = null;
    }

    if (this.autoSwitchToLogsTimer) {
      clearTimeout(this.autoSwitchToLogsTimer);
      this.autoSwitchToLogsTimer = null;
    }

    if (this.resizeTimeout) {
      clearTimeout(this.resizeTimeout);
      this.resizeTimeout = null;
    }

    if (this.canvasResizeObserver) {
      this.canvasResizeObserver.disconnect();
      this.canvasResizeObserver = undefined;
    }

    if (this.wheelEventListener && this.canvasContent?.nativeElement) {
      this.canvasContent.nativeElement.removeEventListener('wheel', this.wheelEventListener);
      this.wheelEventListener = undefined;
    }

    this.cleanupStatusMonitoring();

    this.pollingService.resetLogs();
    this.currentProgressState = '';
    this.lastProgressDescription = '';
    this.pollingStatus = 'PENDING';
    this.hasLogs = false;
    this.logMessages = [];
    this.formattedLogMessages = [];
    this.processedLogHashes.clear();
    this.expandedCodeLogs.clear();
    this.promptService.setImage(null);
    this.promptService.setPrompt('');
    this.promptSubmissionService.resetSubmissionState();

    this.codeSharingService.resetState();

    this.appStateService.updateProjectState({
      codeGenerated: false,
      generatedCode: null,
    });

    this.resetComponentState();

    if (this.isElementSelectionMode) {
      this.cleanupSelectionMode();
      this.isElementSelectionMode = false;
      this.clearSelectedElement();
    }

    try {

      const iframe = document.querySelector('.preview-frame') as HTMLIFrameElement;
      if (iframe && iframe.contentWindow) {

        iframe.src = 'about:blank';
      }
    } catch (error) {
    }

    this.urlSafe = undefined;

    this.userSelectedTab = false;

    this.stepperStateService.clearCurrentProjectId();

    document.body.classList.remove('user-select-none');

    if (this.isResizing$.value) {
      const leftPanel = document.querySelector('.awe-leftpanel') as HTMLElement;
      const rightPanel = document.querySelector('.awe-rightpanel') as HTMLElement;
      const resizer = document.querySelector('.resizer') as HTMLElement;
      const overlay = document.getElementById('resize-overlay');

      if (leftPanel) leftPanel.classList.remove('dragging');
      if (rightPanel) rightPanel.classList.remove('dragging');
      if (resizer) resizer.classList.remove('active');

      if (overlay) {
        document.body.removeChild(overlay);
      }

      document.body.classList.remove('user-select-none');
      document.documentElement.classList.remove('resizing-active');

      this.isResizing$.next(false);
    }

    this.clearAllTypewriterTimeouts();

    this.clearAllLoadingNodes();

    this.destroy$.next();
    this.destroy$.complete();
  }

  private cleanupStatusMonitoring(): void {
    if (this.useSSE && this.enhancedSSEService.isMonitoring()) {
      this.enhancedSSEService.stopMonitoring();
    }

    if (this.sseSubscription) {
      this.sseSubscription.unsubscribe();
      this.sseSubscription = null;
    }

    if (this.sseDataProcessorSubscription) {
      this.sseDataProcessorSubscription.unsubscribe();
      this.sseDataProcessorSubscription = null;
    }

    if (this.isPolling) {
      this.pollingService.stopPolling();
      this.isPolling = false;
    }
  }

  private startStatusMonitoring(projectId: string, jobId: string, taskType: string): void {
    if (this.useSSE) {
      this.startSSEMonitoring(projectId, jobId, taskType);
    }
  }

  private startSSEMonitoring(projectId: string, jobId: string, taskType: string): void {

    const sseOptions = {
      reconnectInterval: 5000,
      maxReconnectAttempts: 10,
      enableExponentialBackoff: true,
      backoffFactor: 1.5,
      maxBackoffInterval: 60000,
      enableHeartbeat: true,
      heartbeatInterval: 60000
    };

    this.sseSubscription = this.enhancedSSEService.startMonitoring(
      projectId,
      jobId,
      sseOptions,
      () => this.fallbackToPolling(projectId, jobId, taskType)
    ).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (_data) => {

      },
      error: (_error) => {
        this.fallbackToPolling(projectId, jobId, taskType);
      }
    });

    this.updatePollingStatus(true);
  }

  private fallbackToPolling(_projectId: string, _jobId: string, _taskType: string): void {
    this.useSSE = false;

  }

  private clearAllTypewriterTimeouts(): void {
    Object.values(this.typewriterTimeouts).forEach(timeout => clearTimeout(timeout));
    this.typewriterTimeouts = {};
  }

  private startArtifactTypewriter(artifactName: string, content: string): void {

    if (this.typewriterTimeouts[artifactName]) {
      clearTimeout(this.typewriterTimeouts[artifactName]);
    }

    this.artifactTypewriterStates[artifactName] = {
      visibleContent: '',
      isTyping: true,
      fullContent: content,
    };

    this.typeArtifactCharacter(artifactName, 0);
  }

  private typeArtifactCharacter(artifactName: string, charIndex: number): void {
    const state = this.artifactTypewriterStates[artifactName];
    if (!state || !state.isTyping) {
      return;
    }

    const fullContent = state.fullContent;
    if (charIndex < fullContent.length) {
      const nextChar = fullContent.charAt(charIndex);
      state.visibleContent = fullContent.substring(0, charIndex + 1);

      let delay = this.artifactTypingSpeed;
      if (['.', '!', '?'].includes(nextChar)) {
        delay = this.artifactTypingSpeed * 1.3;
      } else if ([',', ';', ':'].includes(nextChar)) {
        delay = this.artifactTypingSpeed * 1.1;
      } else if (nextChar === ' ') {
        delay = this.artifactTypingSpeed * 0.7;
      } else if (nextChar === '\n') {
        delay = this.artifactTypingSpeed * 1.5;
      }

      this.cdr.detectChanges();

      this.typewriterTimeouts[artifactName] = setTimeout(() => {
        this.typeArtifactCharacter(artifactName, charIndex + 1);
      }, delay);
    } else {

      state.isTyping = false;
      this.cdr.detectChanges();
    }
  }

  getArtifactVisibleContent(artifactName: string): string {
    const state = this.artifactTypewriterStates[artifactName];
    if (state && state.isTyping) {
      return state.visibleContent;
    }

    return this.selectedArtifactFile?.content || '';
  }



  private updatePollingStatus(isPolling: boolean): void {
    this.isPolling = isPolling;
    if (isPolling) {

      if (this.currentView$.value === 'loading') {
        this.isLoading$.next(true);
      }

      if (this.isCodeGenerationLoading$.value) {

      }

      if (this.selectedImageDataUri) {
        this.selectedImageDataUri = '';
      }
    } else {

      if (this.isCodeGenerationLoading$.value) {
        this.codeRegenerationProgressDescription = '';
      }
    }
  }

  startResize(event: MouseEvent) {

    event.preventDefault();

    this.isResizing$.next(true);

    const leftPanel = document.querySelector('.awe-leftpanel') as HTMLElement;
    const rightPanel = document.querySelector('.awe-rightpanel') as HTMLElement;
    const resizer = document.querySelector('.resizer') as HTMLElement;
    const container = document.querySelector('.awe-splitscreen') as HTMLElement;
    const splitScreen = document.querySelector('.smooth-split-screen') as HTMLElement;

    if (!leftPanel || !rightPanel || !container || !resizer) {
      return;
    }

    const overlay = document.createElement('div');
    overlay.id = 'resize-overlay';
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      z-index: 9999;
      cursor: col-resize;
      background: transparent;
      pointer-events: auto;
    `;
    document.body.appendChild(overlay);

    resizer.classList.add('active');

    leftPanel.classList.add('dragging');
    rightPanel.classList.add('dragging');

    if (splitScreen) {
      splitScreen.classList.add('resizing');
    }

    document.body.classList.add('user-select-none');

    document.documentElement.classList.add('resizing-active');

    const initialX = event.clientX;
    const containerWidth = container.offsetWidth;
    const initialLeftWidth = leftPanel.offsetWidth;

    const minWidth = parseInt(this.minWidth$.value || '300', 10);
    const maxLeftWidth = containerWidth - minWidth;
    const minLeftWidth = minWidth;

    let lastX = initialX;
    let velocity = 0;
    let lastUpdateTime = Date.now();

    let animationFrameId: number | null = null;

    const handleMouseMove = (e: MouseEvent) => {

      e.preventDefault();
      e.stopPropagation();

      if (animationFrameId !== null) {
        cancelAnimationFrame(animationFrameId);
      }

      animationFrameId = requestAnimationFrame(() => {

        const dx = e.clientX - initialX;

        let newLeftWidth = initialLeftWidth + dx;

        newLeftWidth = Math.max(minLeftWidth, Math.min(maxLeftWidth, newLeftWidth));

        const leftPercentage = ((newLeftWidth / containerWidth) * 100).toFixed(2) + '%';
        const rightPercentage =
          (((containerWidth - newLeftWidth) / containerWidth) * 100).toFixed(2) + '%';

        leftPanel.style.width = leftPercentage;
        rightPanel.style.width = rightPercentage;

        this.defaultLeftPanelWidth = leftPercentage;
        this.defaultRightPanelWidth = rightPercentage;

        const now = Date.now();
        const elapsed = now - lastUpdateTime;
        if (elapsed > 0) {
          velocity = (e.clientX - lastX) / elapsed;
          lastX = e.clientX;
          lastUpdateTime = now;
        }

        this.ngZone.run(() => {
          this.cdr.detectChanges();
        });

        animationFrameId = null;
      });
    };

    const handleMouseUp = () => {

      if (animationFrameId !== null) {
        cancelAnimationFrame(animationFrameId);
        animationFrameId = null;
      }

      const overlay = document.getElementById('resize-overlay');
      if (overlay) {
        overlay.removeEventListener('mousemove', handleMouseMove);
        overlay.removeEventListener('mouseup', handleMouseUp);
        overlay.removeEventListener('touchmove', handleTouchMove);
        overlay.removeEventListener('touchend', handleTouchEnd);
        document.body.removeChild(overlay);
      }

      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('touchmove', handleTouchMove, {
        passive: false,
      } as EventListenerOptions);
      document.removeEventListener('touchend', handleTouchEnd);

      if (Math.abs(velocity) > 0.5) {
        const momentum = velocity * 10;
        let newLeftWidth = leftPanel.offsetWidth + momentum;

        newLeftWidth = Math.max(minLeftWidth, Math.min(maxLeftWidth, newLeftWidth));

        const leftPercentage = ((newLeftWidth / containerWidth) * 100).toFixed(2) + '%';
        const rightPercentage =
          (((containerWidth - newLeftWidth) / containerWidth) * 100).toFixed(2) + '%';

        leftPanel.classList.remove('dragging');
        rightPanel.classList.remove('dragging');

        leftPanel.style.width = leftPercentage;
        rightPanel.style.width = rightPercentage;

        this.defaultLeftPanelWidth = leftPercentage;
        this.defaultRightPanelWidth = rightPercentage;
      } else {

        leftPanel.classList.remove('dragging');
        rightPanel.classList.remove('dragging');
      }

      this.isResizing$.next(false);

      resizer.classList.remove('active');

      if (splitScreen) {
        splitScreen.classList.remove('resizing');
      }

      document.body.classList.remove('user-select-none');

      document.documentElement.classList.remove('resizing-active');

      this.ngZone.run(() => {
        this.cdr.detectChanges();
      });
    };

    const handleTouchMove = (e: TouchEvent) => {

      e.preventDefault();

      if (e.touches.length > 0) {
        const touchEvent = e.touches[0];

        const mouseEvent = {
          clientX: touchEvent.clientX,
          preventDefault: () => e.preventDefault(),
        } as MouseEvent;

        handleMouseMove(mouseEvent);
      }
    };

    const handleTouchEnd = () => {
      handleMouseUp();
    };

    overlay.addEventListener('mousemove', handleMouseMove, { passive: false });
    overlay.addEventListener('mouseup', handleMouseUp, { passive: true });
    overlay.addEventListener('touchmove', handleTouchMove, {
      passive: false,
    } as EventListenerOptions);
    overlay.addEventListener('touchend', handleTouchEnd, { passive: true });

    document.addEventListener('mousemove', handleMouseMove, { passive: false });
    document.addEventListener('mouseup', handleMouseUp, { passive: true });
    document.addEventListener('touchmove', handleTouchMove, {
      passive: false,
    } as EventListenerOptions);
    document.addEventListener('touchend', handleTouchEnd, { passive: true });
  }
  onPanelToggled(isCollapsed: boolean) {
    this.isPanelCollapsed$.next(isCollapsed);
  }

  onFileChanged(_files: { filename: string; filecontent: string }[]) {

  }

  onMessageReceived(_message: { type: string; data: any }) {

  }

  onEditorReady(_vm: any) {
    this.editorReady = true;
    this.updateEditorWithGeneratedCode();
  }

  private updateEditorWithGeneratedCode() {
    try {
    } catch (error) {}
  }

  toggleHistoryView(): void {
    this.isHistoryActive$.next(!this.isHistoryActive$.value);
  }

  toggleCodeView(): void {

    if (!this.isCodeGenerationComplete) {
      return;
    }

    if (this.previewError$.value) {
      return;
    }

    if (this.codeViewer && this.codeViewer.saveCurrentContent) {
      this.codeViewer.saveCurrentContent();
    }

    this.userSelectedTab = true;

    this.ngZone.run(() => {

      this.isCodeActive$.next(true);
      this.isPreviewActive$.next(false);

      this.isArtifactsActive$.next(false);

      this.currentView$.next('editor');

      this.isLoading$.next(false);
      this.previewIcon$.next('bi-code-slash');

      this.cdr.markForCheck();
    });
  }
  togglePreviewView(): void {

    if (this.codeViewer && this.codeViewer.saveCurrentContent) {
      this.codeViewer.saveCurrentContent();
    }

    this.userSelectedTab = true;

    this.ngZone.run(() => {

      this.isCodeActive$.next(false);
      this.isPreviewActive$.next(true);

      this.isArtifactsActive$.next(false);

      this.currentView$.next('preview');

      if (!this.hasNewPollingResponseUrl && this.deployedUrl$.value) {
        this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl(this.deployedUrl$.value);
      }

      if (this.isCodeGenerationComplete && !this.previewError$.value) {
        this.isPreviewLoading$.next(false);
      }

      this.cdr.markForCheck();
    });

    if (this.currentProgressState &&
        (this.currentProgressState.includes('LAYOUT_ANALYZED') ||
         this.currentProgressState.includes('PAGES_GENERATED')) &&
        this.layoutData && this.layoutData.length > 0) {
      this.isPreviewLoading$.next(false);
      this.isLayoutLoading = false;
    } else if (!this.isCodeGenerationComplete) {
      this.isPreviewLoading$.next(true);
    }

    this.previewIcon$.next('bi-eye');
    this.isPreviewTabEnabled = true;
  }

  toggleLogsView(): void {

    this.toggleArtifactsView();

  }

  toggleArtifactsView(): void {

    if (!this.isArtifactsTabEnabledWithLogs) {
      this.toastService.info('Artefacts and logs will be available when generated during the workflow');
      return;
    }

    if (this.codeViewer && this.codeViewer.saveCurrentContent) {
      this.codeViewer.saveCurrentContent();
    }

    this.userSelectedTab = true;

    this.isCodeActive$.next(false);
    this.isPreviewActive$.next(false);

    this.isArtifactsActive$.next(true);

    this.currentView$.next('artifacts');

    this.ensureArtifactPersistence();

    this.ensureLogsFileAtBottom();

    if (!this.selectedArtifactFile && this.artifactsData.length > 0) {
      this.selectArtifactFile(this.artifactsData[0]);
    }

    this.cdr.detectChanges();
  }

  toggleOverviewView(): void {

    if (this.codeViewer && this.codeViewer.saveCurrentContent) {
      this.codeViewer.saveCurrentContent();
    }

    this.userSelectedTab = true;

    this.isCodeActive$.next(false);
    this.isPreviewActive$.next(false);

    this.isArtifactsActive$.next(false);

    this.currentView$.next('overview');

    this.cdr.detectChanges();
  }

  toggleCodeViewEnhanced(): void {
    // ENHANCEMENT: Allow code tab navigation when template files are available OR code generation is complete
    if (!this.canShowCodeTabContent || this.previewError$.value) {
      this.completeTabTransition();
      return;
    }

    if (this.codeViewer && this.codeViewer.saveCurrentContent) {
      this.codeViewer.saveCurrentContent();
    }

    this.ngZone.run(() => {

      this.isCodeActive$.next(true);
      this.isPreviewActive$.next(false);
      this.isArtifactsActive$.next(false);

      this.currentView$.next('editor');

      this.isLoading$.next(false);
      this.previewIcon$.next('bi-code-slash');

      this.completeTabTransition();
    });
  }

  togglePreviewViewEnhanced(): void {

    if (this.codeViewer && this.codeViewer.saveCurrentContent) {
      this.codeViewer.saveCurrentContent();
    }

    this.ngZone.run(() => {

      this.isCodeActive$.next(false);
      this.isPreviewActive$.next(true);
      this.isArtifactsActive$.next(false);

      this.currentView$.next('preview');

      if (!this.hasNewPollingResponseUrl && this.deployedUrl$.value) {
        this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl(this.deployedUrl$.value);
      }

      if (this.isCodeGenerationComplete && !this.previewError$.value) {
        this.isPreviewLoading$.next(false);
      }

      this.completeTabTransition();
    });
  }

  toggleArtifactsViewEnhanced(): void {

    if (this.codeViewer && this.codeViewer.saveCurrentContent) {
      this.codeViewer.saveCurrentContent();
    }

    this.isCodeActive$.next(false);
    this.isPreviewActive$.next(false);
    this.isArtifactsActive$.next(true);

    this.currentView$.next('artifacts');

    this.ensureArtifactPersistence();

    if (!this.selectedArtifactFile && this.artifactsData.length > 0) {
      this.selectArtifactFile(this.artifactsData[0]);
    }

    this.completeTabTransition();
  }

  toggleOverviewViewEnhanced(): void {

    if (this.codeViewer && this.codeViewer.saveCurrentContent) {
      this.codeViewer.saveCurrentContent();
    }

    this.isCodeActive$.next(false);
    this.isPreviewActive$.next(false);
    this.isArtifactsActive$.next(false);

    this.currentView$.next('overview');

    this.completeTabTransition();
  }

  toggleLogsViewEnhanced(): void {

    this.toggleArtifactsViewEnhanced();

  }

  onUIDesignPageChange(pageIndex: number): void {
    this.currentUIDesignPageIndex$.next(pageIndex);
  }

  onUIDesignFullscreenRequest(page: MobilePage): void {
    this.createMobileFullscreenOverlay(page);
  }

  private createMobileFullscreenOverlay(page: MobilePage): void {

    const originalBodyOverflow = document.body.style.overflow;
    const originalBodyPosition = document.body.style.position;
    const originalBodyTop = document.body.style.top;
    const originalBodyWidth = document.body.style.width;
    const scrollY = window.scrollY;

    const overlay = document.createElement('div');
    overlay.className = 'mobile-fullscreen-overlay';
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      backdrop-filter: blur(8px);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      animation: fadeIn 0.3s ease-out;
    `;

    const content = document.createElement('div');
    content.className = 'overlay-content';

    const isMobile = window.innerWidth <= 768;
    const contentWidth = isMobile ? '95vw' : '90vw';
    const contentHeight = isMobile ? '95vh' : '90vh';
    const maxWidth = isMobile ? 'none' : '420px';
    const maxHeight = isMobile ? 'none' : '720px';
    const borderRadius = isMobile ? '8px' : '12px';

    content.style.cssText = `
      position: relative;
      width: ${contentWidth};
      height: ${contentHeight};
      max-width: ${maxWidth};
      max-height: ${maxHeight};
      background: white;
      border-radius: ${borderRadius};
      overflow: hidden;
      animation: scaleIn 0.3s ease-out;
      box-shadow:
        0 0 0 1px rgba(255, 255, 255, 0.1),
        0 4px 8px rgba(0, 0, 0, 0.1),
        0 8px 16px rgba(0, 0, 0, 0.15),
        0 16px 32px rgba(0, 0, 0, 0.2);
    `;

    const closeButton = document.createElement('button');
    closeButton.innerHTML = `
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
        <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    `;
    closeButton.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      width: 48px;
      height: 48px;
      background: rgba(0, 0, 0, 0.8);
      border: 2px solid rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      color: white;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10001;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      backdrop-filter: blur(12px);
      box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.3),
        0 2px 6px rgba(0, 0, 0, 0.2);
      font-size: 0;
    `;

    closeButton.addEventListener('mouseenter', () => {
      closeButton.style.background = 'rgba(220, 38, 38, 0.9)';
      closeButton.style.borderColor = 'rgba(255, 255, 255, 0.4)';
      closeButton.style.transform = 'scale(1.1)';
      closeButton.style.boxShadow = `
        0 6px 20px rgba(220, 38, 38, 0.4),
        0 4px 12px rgba(0, 0, 0, 0.3)
      `;
    });

    closeButton.addEventListener('mouseleave', () => {
      closeButton.style.background = 'rgba(0, 0, 0, 0.8)';
      closeButton.style.borderColor = 'rgba(255, 255, 255, 0.2)';
      closeButton.style.transform = 'scale(1)';
      closeButton.style.boxShadow = `
        0 4px 12px rgba(0, 0, 0, 0.3),
        0 2px 6px rgba(0, 0, 0, 0.2)
      `;
    });

    const iframe = document.createElement('iframe');
    iframe.srcdoc = page.content;
    iframe.style.cssText = `
      width: 100%;
      height: 100%;
      border: none;
      background: white;
      border-radius: 12px;
    `;
    iframe.setAttribute(
      'sandbox',
      'allow-same-origin allow-scripts allow-top-navigation-by-user-activation'
    );

    const style = document.createElement('style');
    style.textContent = `
      @keyframes fadeIn {
        from { opacity: 0; visibility: hidden; }
        to { opacity: 1; visibility: visible; }
      }
      @keyframes scaleIn {
        from { transform: scale(0.9); opacity: 0; }
        to { transform: scale(1); opacity: 1; }
      }

      /* Responsive close button */
      @media (max-width: 768px) {
        .mobile-overlay-close-btn {
          top: 15px !important;
          right: 15px !important;
          width: 44px !important;
          height: 44px !important;
        }
      }

      @media (max-width: 480px) {
        .mobile-overlay-close-btn {
          top: 10px !important;
          right: 10px !important;
          width: 40px !important;
          height: 40px !important;
        }
      }
    `;
    document.head.appendChild(style);

    closeButton.classList.add('mobile-overlay-close-btn');

    const closeOverlay = () => {

      document.body.style.position = originalBodyPosition;
      document.body.style.top = originalBodyTop;
      document.body.style.width = originalBodyWidth;
      document.body.style.overflow = originalBodyOverflow;

      window.scrollTo(0, scrollY);

      if (document.body.contains(overlay)) {
        document.body.removeChild(overlay);
      }
      if (document.body.contains(closeButton)) {
        document.body.removeChild(closeButton);
      }
      if (document.head.contains(style)) {
        document.head.removeChild(style);
      }
    };

    closeButton.addEventListener('click', closeOverlay);
    overlay.addEventListener('click', e => {
      if (e.target === overlay) {
        closeOverlay();
      }
    });

    const escapeListener = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        closeOverlay();
        document.removeEventListener('keydown', escapeListener);
      }
    };
    document.addEventListener('keydown', escapeListener);

    content.addEventListener('click', e => {
      e.stopPropagation();
    });

    content.appendChild(iframe);
    overlay.appendChild(content);

    document.body.appendChild(closeButton);

    document.body.style.position = 'fixed';
    document.body.style.top = `-${scrollY}px`;
    document.body.style.width = '100%';
    document.body.style.overflow = 'hidden';

    document.body.appendChild(overlay);
  }

  private ensureArtifactPersistence(): void {

    this.loadedArtifacts.forEach(artifactName => {
      const exists = this.artifactsData.some(item => item.name === artifactName);
      if (!exists) {
        this.restoreArtifact(artifactName);
      }
    });

    if (
      this.hasLayoutAnalyzed &&
      !this.artifactsData.some(item => item.name === 'Layout Analyzed')
    ) {
      this.ensureLayoutAnalyzedFileExists();
    }

    if (this.hasDesignSystem && !this.artifactsData.some(item => item.name === 'Design System')) {
      this.captureDesignSystemMappedState();
    }
  }

  private restoreArtifact(artifactName: string): void {
    switch (artifactName) {
      case 'Project Overview':
        this.artifactsData.unshift({
          name: 'Project Overview',
          type: 'markdown',
          content: '# Project Overview\n\nProject overview content has been loaded.',
        });

        this.ensureLogsFileAtBottom();
        break;
      case 'Layout Analyzed':
        this.ensureLayoutAnalyzedFileExists();
        break;
      case 'Design System':
        break;
      default:
    }
  }

  selectArtifactFile(file: any): void {
    this.selectedArtifactFile = file;

    if (file && file.name === 'Design System' && file.type === 'component') {
      this.initializeDesignTokens();
    }

    if (file && file.name === 'Layout Analyzed' && file.type === 'image') {
    }

    if (file && file.name === 'Project Overview' && file.type === 'markdown' && file.content) {
      const existingState = this.artifactTypewriterStates[file.name];

      if (
        !existingState ||
        (!existingState.isTyping && existingState.fullContent !== file.content)
      ) {
        this.startArtifactTypewriter(file.name, file.content);
      }
    }

    this.cdr.detectChanges();
  }

  private initializeLayoutAnalyzedData(): void {

    this.layoutAnalyzedData = [];

    if (this.layoutData && this.layoutData.length > 0) {

      const validLayoutKeys = this.layoutData.filter(key => this.layoutMapping[key]);

      if (validLayoutKeys.length > 0) {

        this.layoutAnalyzedData = validLayoutKeys.map(key => ({
          key,
          name: this.layoutMapping[key] || 'Identified Layout',
          imageUrl: `assets/images/layout-${key}.png`,
        }));

        this.stopLayoutAnalyzing();

      } else {

        this.startLayoutAnalyzing();
      }
    } else {

      this.startLayoutAnalyzing();
    }

    if (this.hasLayoutAnalyzed) {

      this.ensureLayoutAnalyzedFileExists();
    }

    this.cdr.detectChanges();
  }



  private ensureLayoutAnalyzedFileExists(): void {

    let layoutImageUrl = 'assets/images/layout-HB.png';
    if (this.layoutData && this.layoutData.length > 0) {
      const layoutKey = this.layoutData[0];
      layoutImageUrl = `assets/images/layout-${layoutKey}.png`;
    } else if (this.layoutAnalyzedData && this.layoutAnalyzedData.length > 0) {
      layoutImageUrl = this.layoutAnalyzedData[0].imageUrl;
    }

    const layoutAnalyzedIndex = this.artifactsData.findIndex(
      file => file.name === 'Layout Analyzed'
    );

    if (layoutAnalyzedIndex === -1) {

      this.artifactsData.push({
        name: 'Layout Analyzed',
        type: 'image',
        content: layoutImageUrl,
      });

      this.ensureLogsFileAtBottom();

    } else {

      this.artifactsData[layoutAnalyzedIndex].content = layoutImageUrl;
    }
  }

  private captureLayoutAnalyzedState(): void {

    const existingLayoutArtifact = this.artifactsData.find(item => item.name === 'Layout Analyzed');
    if (existingLayoutArtifact) {
      return;
    }

    this.hasLayoutAnalyzed = true;

    this.initializeLayoutAnalyzedData();

    let layoutImageUrl = 'assets/images/layout-HB.png';
    if (this.layoutData && this.layoutData.length > 0) {
      const layoutKey = this.layoutData[0];
      layoutImageUrl = `assets/images/layout-${layoutKey}.png`;
    } else if (this.layoutAnalyzedData && this.layoutAnalyzedData.length > 0) {
      layoutImageUrl = this.layoutAnalyzedData[0].imageUrl;
    }

    const layoutAnalyzedIndex = this.artifactsData.findIndex(
      file => file.name === 'Layout Analyzed'
    );

    if (layoutAnalyzedIndex === -1) {

      this.artifactsData.push({
        name: 'Layout Analyzed',
        type: 'image',
        content: layoutImageUrl,
      });

      this.ensureLogsFileAtBottom();

    } else {

      this.artifactsData[layoutAnalyzedIndex].content = layoutImageUrl;
    }

    this.cdr.detectChanges();
  }

  private captureDesignSystemMappedState(): void {

    this.hasDesignSystem = true;

    const hasDesignSystemFile = this.artifactsData.some(file => file.name === 'Design System');
    if (!hasDesignSystemFile) {

      this.artifactsData.push({
        name: 'Design System',
        type: 'component',
        content: 'Design system information will be displayed here.',
      });

      this.ensureLogsFileAtBottom();

      this.initializeDesignTokens();

      if (this.artifactsData.length === 1) {
        this.selectArtifactFile(this.artifactsData[0]);
      }
    }

  }



  private initializeDesignTokens(): void {

    if (this.designSystemData && this.hasNewDesignTokenStructure(this.designSystemData)) {
      this.initializeFromNewStructure(this.designSystemData);
      return;
    }

    if (!this.designSystemData || this.isUsingDefaultTokens()) {
      this.startDesignTokenLoading();

      this.designTokens = [];
      return;
    }

    this.designTokens = [...ALL_DESIGN_TOKENS];

  }

  private hasNewDesignTokenStructure(data: any): boolean {
    try {
      return data?.colors && Array.isArray(data.colors) &&
             data.colors.length > 0 &&
             data.colors[0]?.id && data.colors[0]?.name && data.colors[0]?.value;
    } catch (error) {
      return false;
    }
  }

  private initializeFromNewStructure(data: any): void {
    try {
      const newTokens: DesignToken[] = [];

      if (data.colors && Array.isArray(data.colors)) {
        data.colors.forEach((colorToken: any) => {
          if (this.isValidArtifactDesignToken(colorToken)) {
            const mappedToken: DesignToken = {
              id: colorToken.id,
              name: colorToken.name,
              value: colorToken.value,
              type: 'color',
              category: colorToken.category || 'Colors',
              editable: colorToken.editable !== false
            };
            newTokens.push(mappedToken);
          }
        });
      }

      this.designTokens = newTokens;

      this.stopDesignTokenLoading();

    } catch (error) {

      this.startDesignTokenLoading();
      this.designTokens = [];
    }
  }

  private isValidArtifactDesignToken(token: any): boolean {
    return token &&
           typeof token.id === 'string' && token.id.trim() !== '' &&
           typeof token.name === 'string' && token.name.trim() !== '' &&
           typeof token.value === 'string' && token.value.trim() !== '' &&
           (typeof token.category === 'string' || token.category === undefined) &&
           (typeof token.editable === 'boolean' || token.editable === undefined);
  }

  generateColorName(hexColor: string): string {

    const hex = hexColor.startsWith('#') ? hexColor.substring(1) : hexColor;

    const colorMap: { [key: string]: string } = {
      '000000': 'Black',
      FFFFFF: 'White',
      FF0000: 'Red',
      '00FF00': 'Green',
      '0000FF': 'Blue',
      FFFF00: 'Yellow',
      '00FFFF': 'Cyan',
      FF00FF: 'Magenta',
      C0C0C0: 'Silver',
      '808080': 'Gray',
      '800000': 'Maroon',
      '808000': 'Olive',
      '008000': 'Dark Green',
      '800080': 'Purple',
      '008080': 'Teal',
      '000080': 'Navy',
      FFA500: 'Orange',
      A52A2A: 'Brown',
      FFC0CB: 'Pink',
      E48900: 'D_Yellow',
      FFA826: 'Lemon',
      '212121': 'Black',
      '4D4D4D': 'D_Grey',
      F5F7FA: 'Silver',
    };

    if (colorMap[hex.toUpperCase()]) {
      return colorMap[hex.toUpperCase()];
    }

    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    let hue = '';
    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);

    if (max === min) {

      const brightness = Math.round((r + g + b) / 3);
      if (brightness < 64) return 'Dark Gray';
      if (brightness < 128) return 'Gray';
      if (brightness < 192) return 'Light Gray';
      return 'Off White';
    }

    if (r === max) {
      if (g > b) hue = 'Orange';
      else hue = 'Red';
    } else if (g === max) {
      if (r > b) hue = 'Yellow Green';
      else hue = 'Green';
    } else {
      if (r > g) hue = 'Purple';
      else hue = 'Blue';
    }

    const brightness = (r + g + b) / 3;
    let prefix = '';

    if (brightness < 85) prefix = 'Dark ';
    else if (brightness > 170) prefix = 'Light ';

    return prefix + hue;
  }

  updateDesignToken(tokenId: string, newValue: string): void {

    if (!this.designTokenEditState$.value.isEditMode) {
      return;
    }

    const tokenIndex = this.designTokens.findIndex(token => token.id === tokenId);

    if (tokenIndex !== -1) {
      const token = this.designTokens[tokenIndex];
      const originalValue = this.designTokenEditState$.value.originalValues.get(tokenId);

      token.value = newValue;

      if (token.type === 'color') {
        token.name = this.generateColorName(newValue);
      }

      const hasChanged = originalValue !== newValue;
      const currentState = this.designTokenEditState$.value;
      this.designTokenEditState$.next({
        ...currentState,
        hasUnsavedChanges: hasChanged || currentState.hasUnsavedChanges
      });

    }
  }

  getDesignTokensForBackend(): any {
    const tokensByCategory = {
      colors: this.designTokens
        .filter(token => token.type === 'color')
        .map(token => ({
          id: token.id,
          name: token.name,
          value: token.value,
          hexCode: token.value,
          category: token.category,
          editable: token.editable,
        })),
      typography: this.designTokens
        .filter(token => token.type === 'typography')
        .map(token => ({
          id: token.id,
          name: token.name,
          value: token.value,
          category: token.category,
          editable: token.editable,
        })),
      buttons: this.designTokens
        .filter(token => token.category === 'Buttons')
        .map(token => ({
          id: token.id,
          name: token.name,
          variant: token.value,
          category: token.category,
          editable: token.editable,
        })),
    };

    return {
      designSystem: {
        tokens: tokensByCategory,
        timestamp: new Date().toISOString(),
        projectId: this.projectId,
        jobId: this.jobId,
      },
    };
  }

  private designTokensUpdateTimer: any;



  private sendTokensUpdateRequest(tokensPayload: any): void {

    const updateRequest = {
      project_id: this.projectId,
      job_id: this.jobId,
      action: 'update_design_tokens',
      design_tokens: tokensPayload.designSystem.tokens,
      timestamp: tokensPayload.designSystem.timestamp,
    };

    this.codeGenerationService.updateDesignTokens(updateRequest).subscribe({
      next: _response => {
        this.toastService.success('Design tokens updated');
      },
      error: _error => {
        this.toastService.error('Failed to update design tokens');
      },
    });
  }

  triggerDesignTokensUpdate(): void {

    if (this.designTokensUpdateTimer) {
      clearTimeout(this.designTokensUpdateTimer);
      this.designTokensUpdateTimer = null;
    }

    if (this.projectId && this.jobId) {
      const tokensPayload = this.getDesignTokensForBackend();
      this.sendTokensUpdateRequest(tokensPayload);
    } else {
    }
  }

  getCurrentDesignTokensState(): any {
    return {
      tokens: this.designTokens,
      hasDesignSystem: this.hasDesignSystem,
      projectId: this.projectId,
      jobId: this.jobId,
      backendPayload: this.getDesignTokensForBackend(),
    };
  }

  isValidHexColor(color: string): boolean {

    return validateHexColor(color);
  }

  onTokenValueChange(event: Event, tokenId: string): void {
    const inputElement = event.target as HTMLInputElement;
    if (inputElement && inputElement.value) {
      const value = inputElement.value;

      const token = this.designTokens.find(t => t.id === tokenId);
      if (token && token.type === 'color') {

        const isValid = this.isValidHexColor(value);

        if (isValid) {
          inputElement.classList.remove('invalid');

          const formattedValue = value.startsWith('#') ? value : `#${value}`;
          this.updateDesignToken(tokenId, formattedValue);
        } else {
          inputElement.classList.add('invalid');

          return;
        }
      } else {

        this.updateDesignToken(tokenId, value);
      }
    }
  }

  getTokensByCategory(category: string): DesignToken[] {

    return this.designTokens.filter(token => token.category === category);
  }

  toggleDesignTokenEditMode(): void {
    const currentState = this.designTokenEditState$.value;

    if (currentState.isEditMode) {

      this.saveDesignTokenChanges();
    } else {

      this.enterDesignTokenEditMode();
    }
  }

  private enterDesignTokenEditMode(): void {
    const originalValues = new Map<string, string>();

    this.designTokens.forEach(token => {
      if (token.category === 'Colors') {
        originalValues.set(token.id, token.value);
      }
    });

    this.designTokenEditState$.next({
      isEditMode: true,
      editButtonText: 'Save',
      hasUnsavedChanges: false,
      originalValues
    });

  }

  private saveDesignTokenChanges(): void {
    const currentState = this.designTokenEditState$.value;

    if (currentState.hasUnsavedChanges) {
    }

    this.designTokenEditState$.next({
      isEditMode: false,
      editButtonText: 'Edit',
      hasUnsavedChanges: false,
      originalValues: new Map()
    });

  }

  areDesignTokenInputsDisabled(): boolean {
    return !this.designTokenEditState$.value.isEditMode;
  }

  getDesignTokenEditButtonText(): string {
    return this.designTokenEditState$.value.editButtonText;
  }

  startDesignTokenLoading(): void {
    this.designTokenLoadingState$.next({
      isLoading: true,
      showAnalyzingAnimation: true,
      hasReceivedTokens: false,
      loadingMessage: 'Analyzing Design Tokens...'
    });

  }

  stopDesignTokenLoading(): void {
    this.designTokenLoadingState$.next({
      isLoading: false,
      showAnalyzingAnimation: false,
      hasReceivedTokens: true,
      loadingMessage: ''
    });

  }

  shouldShowDesignTokenLoadingAnimation(): boolean {
    const loadingState = this.designTokenLoadingState$.value;
    return loadingState.isLoading && loadingState.showAnalyzingAnimation && !loadingState.hasReceivedTokens;
  }

  hasActualDesignTokensFromPolling(): boolean {
    const loadingState = this.designTokenLoadingState$.value;

    return loadingState.hasReceivedTokens &&
           this.designTokens.length > 0 &&
           !this.isUsingDefaultTokens();
  }

  private isUsingDefaultTokens(): boolean {

    if (this.designTokens.length === 0) return true;

    const colorTokens = this.designTokens.filter(token => token.category === 'Colors');
    const defaultColorNames = ['D_Yellow', 'Lemon', 'Black', 'D_Grey', 'Silver'];

    return colorTokens.length === 5 &&
           colorTokens.every(token => defaultColorNames.includes(token.name));
  }

  getFileIconClass(fileType: string): string {
    switch (fileType) {
      case 'markdown':
        return 'file-icon-md';
      case 'image':
        return 'file-icon-img';
      case 'svg':
        return 'file-icon-svg';
      case 'text':
        return 'file-icon-txt';
      case 'component':
        return 'file-icon-component';
      case 'logs':
        return 'file-icon-logs';
      default:
        return 'file-icon-default';
    }
  }

  lastDisplayedLogIndex = 0;

  getLogClass(log: string | any): string {

    if (typeof log === 'object' && log !== null && log.type) {
      switch (log.type) {
        case 'error':
          return 'log-error';
        case 'warning':
          return 'log-warning';
        case 'debug':
          return 'log-debug';
        case 'code':
          return 'log-code';
        case 'progress-description':
          return 'log-info progress-description';
        case 'status-change':
          return 'log-info status-change';
        default:
          return 'log-info';
      }
    }

    if (typeof log === 'string') {
      if (log.includes('ERROR')) {
        return 'log-error';
      } else if (log.includes('WARN')) {
        return 'log-warning';
      } else if (log.includes('DEBUG')) {
        return 'log-debug';
      } else if (log.includes('Progress Description')) {

        return 'log-info progress-description';
      } else if (log.includes('Status changed to:')) {

        return 'log-info status-change';
      } else {
        return 'log-info';
      }
    }

    return 'log-info';
  }

  @Input() defaultLeftPanelWidth: string = '50%';
  @Input() defaultRightPanelWidth: string = '50%';

  toggleLeftPanel(): void {
    const leftPanel = document.querySelector('.awe-leftpanel') as HTMLElement;
    const rightPanel = document.querySelector('.awe-rightpanel') as HTMLElement;
    const splitScreen = document.querySelector('.awe-splitscreen') as HTMLElement;

    if (leftPanel && rightPanel && splitScreen) {
      if (this.isLeftPanelCollapsed$.value) {

        leftPanel.style.width = this.defaultLeftPanelWidth;
        rightPanel.style.width = this.defaultRightPanelWidth;
        this.isLeftPanelCollapsed$.next(false);

        splitScreen.classList.remove('left-panel-collapsed');
      } else {

        leftPanel.style.width = '0%';
        rightPanel.style.width = '100%';
        this.isLeftPanelCollapsed$.next(true);

        splitScreen.classList.add('left-panel-collapsed');
      }
    }
  }

  private baseRightIcons: { name: string; status: IconStatus }[] = [
    { name: 'awe_enhance', status: 'active' },
    { name: 'awe_enhanced_send', status: 'active' },
  ];

  private fileAttachIcon: { name: string; status: IconStatus } = {
    name: 'awe_enhanced_alternate',
    status: 'default',
  };
  get rightIcons(): { name: string; status: IconStatus }[] {
    const isUIDesignMode = this.isUIDesignMode$.value;
    if (isUIDesignMode) {
      return [...this.baseRightIcons];
    } else {
      return [this.fileAttachIcon, ...this.baseRightIcons];
    }
  }

  handleIconClick(event: { name: string; side: string; index: number; theme: string }): void {
    const normalizedIconName = event.name.toLowerCase();
    switch (normalizedIconName) {
      case 'awe_enhanced_alternate':
        this.handleEnhancedAlternate();
        break;
      case 'awe_enhance':
        this.handleEnhanceText();
        break;
      case 'awe_enhanced_send':
        if (event.theme === 'dark') {
          this.handleEnhancedSendDark();
        } else if (event.theme === 'light') {
          this.handleEnhancedSendLight();
        }
        break;
    }
  }

  handleEnhancedSendDark(): void {
    if (!this.darkPrompt.trim()) return;

    this.darkMessages.push({ text: this.darkPrompt, from: 'user', theme: 'dark' });

    setTimeout(() => {
      this.darkMessages.push({
        text: 'This is an AI-generated reply to your message (dark theme).',
        from: 'ai',
        theme: 'dark',
      });
    }, 100);

    this.darkPrompt = '';
  }

  isAiResponding: boolean = false;

  handleEnhancedSendLight(): void {
    if (!this.lightPrompt.trim()) return;
    if (this.isAiResponding) return;

    if (this.selectedElementHTML && this.selectedElementPath) {

      this.sendElementModificationRequest(this.lightPrompt);

      this.lightPrompt = '';

      return;
    }

    if (this.isCodeGenerationComplete) {
      this.handleCodeEditRequest();
      return;
    }

    const currentImageDataUri = this.selectedImageDataUri;

    this.isAiResponding = true;
    this.lightMessages.push({
      text: this.lightPrompt,
      from: 'user',
      theme: 'light',
      imageDataUri: currentImageDataUri || undefined,
    });

    if (currentImageDataUri) {
      this.selectedImageDataUri = '';
    }

    this.lightPrompt = '';

    setTimeout(() => {
      this.lightMessages.push({
        text: 'Analyzing your request...',
        from: 'ai',
        theme: 'light',
      });
    }, 500);

    this.appStateService.project$
      .subscribe(projectState => {

        let userSelectionsSummary = '';

        if (projectState.imageUrl) {
          userSelectionsSummary += '- **Image**: You provided an image for reference\n';
        }

        if (projectState.technology) {
          userSelectionsSummary += `- **Technology**: ${projectState.technology.charAt(0).toUpperCase() + projectState.technology.slice(1)}\n`;
        }

        if (projectState.designLibrary) {
          userSelectionsSummary += `- **Design Library**: ${projectState.designLibrary.charAt(0).toUpperCase() + projectState.designLibrary.slice(1)}\n`;
        }

        if (projectState.application) {
          userSelectionsSummary += `- **Application Type**: ${projectState.application.charAt(0).toUpperCase() + projectState.application.slice(1)}\n`;
        }

        if (projectState.type) {
          userSelectionsSummary += `- **Generation Type**: ${projectState.type}\n`;
        }

        setTimeout(() => {

          const lastMessage = this.lightMessages[this.lightMessages.length - 1];
          if (lastMessage && lastMessage.from === 'ai') {

          }
          this.isAiResponding = false;
        }, 2000);
      })
      .unsubscribe();
  }

  private handleCodeEditRequest(): void {
    const userRequest = this.lightPrompt.trim();
    const currentImageDataUri = this.selectedImageDataUri;

    this.lastUserRequest = userRequest;

    this.lightMessages.push({
      text: userRequest,
      from: 'user',
      theme: 'light',
      imageDataUri: currentImageDataUri || undefined,
    });

    if (currentImageDataUri) {
      this.selectedImageDataUri = '';
    }
    this.lightPrompt = '';

    this.isAiResponding = true;

    this.isCodeGenerationLoading$.next(true);

    this.subscribeToRegenerationProgress();

    this.startRegenerationPreviewLoading();

    const messageId = `ai-regeneration-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    this.lightMessages.push({
      id: messageId,
      text: '',
      from: 'ai',
      theme: 'light',
    });

    this.activeAIMessageIds.add(messageId);
    this.currentActiveMessageId = messageId;

    const currentCodeFiles = this.getCurrentCodeFiles();

    if (!currentCodeFiles || currentCodeFiles.length === 0) {
      this.handleEditError(
        'No code files found to edit. Please ensure code generation is complete.'
      );

      this.stopRegenerationPreviewLoading();
      return;
    }

    const images: string[] = currentImageDataUri ? [currentImageDataUri] : [];

    this.executeSequentialCodeRegenerationWithPayload(currentCodeFiles,userRequest,images);

  }

  private startRegenerationPreviewLoading(): void {

    this.isRegenerationInProgress$.next(true);
    this.regenerationStartTime = Date.now();

    this.urlSafe = undefined;

    this.isPreviewLoading$.next(true);
    this.previewIcon$.next('bi-arrow-clockwise');
    this.previewError$.next(false);

    this.isUrlValidated$.next(false);
    this.isUrlAvailable$.next(false);
    this.isIframeReady$.next(false);

    this.urlValidationError$.next('');

    this.isPreviewTabEnabled = false;

    this.setPreviewTabDisabled('Regeneration in progress...');

    this.setCodeTabDisabled('Code regeneration in progress...');

    if (this.currentView$.value !== 'editor') {
      this.currentView$.next('editor');
      this.isCodeActive$.next(true);
      this.isPreviewActive$.next(false);
      this.isArtifactsActive$.next(false);
    }

    this.cdr.detectChanges();

  }

  private stopRegenerationPreviewLoading(): void {

    this.isPreviewLoading$.next(false);
    this.previewIcon$.next('bi-eye');

    if (this.isRegenerationInProgress$.value) {
      this.isRegenerationInProgress$.next(false);
      this.regenerationStartTime = 0;
    }

    this.resetRegenerationCallFlag('Preview loading deactivation');

  }



  private handleRegenerationFailure(): void {

    const sessionKey = this.currentCheckpointSession();
    if (sessionKey) {
      this.regenerationCheckpointService.completeCheckpointSession(sessionKey, 'failed');
      this.currentCheckpointSession.set(null);

    }

    this.isRegenerationInProgress$.next(false);
    this.regenerationStartTime = 0;

    this.stopRegenerationPreviewLoading();

    const currentUrl = this.deployedUrl$.value;
    if (currentUrl && currentUrl !== 'ERROR_DEPLOYMENT_FAILED') {

      this.isPreviewTabEnabled = true;
      this.previewError$.next(false);

      this.ensureIframeLoadedAfterRegeneration();
    } else {

      this.showDeploymentErrorInPreview();
    }

    this.resetRegenerationCallFlag('Regeneration failure');

  }

  private completeDirectRegenerationResponse(): void {

    if (this.regenerationStartTime > 0) {

    }

    this.isRegenerationInProgress$.next(false);
    this.regenerationStartTime = 0;

    this.isCodeGenerationLoading$.next(false);
    this.codeRegenerationProgressDescription = '';

    this.stopRegenerationPreviewLoading();

    this.isPreviewTabEnabled = true;
    this.previewError$.next(false);

    let currentUrl = this.deployedUrl$.value;

    if (!currentUrl || currentUrl === 'ERROR_DEPLOYMENT_FAILED') {

      const codeServiceUrl = this.codeSharingService.getDeployedUrl();
      if (codeServiceUrl) {
        currentUrl = codeServiceUrl;
        this.deployedUrl$.next(currentUrl);
      }

      else if (this.newPreviewUrl) {
        currentUrl = this.newPreviewUrl;
        this.deployedUrl$.next(currentUrl);
      }
    }

    if (currentUrl && currentUrl !== 'ERROR_DEPLOYMENT_FAILED') {

      this.ensureIframeLoadedAfterRegeneration();
    } else {

      this.restoreIframeAfterRegenerationWithoutUrl();
    }

    this.cdr.detectChanges();

    this.resetRegenerationCallFlag('Direct response completion');

  }

  private ensureIframeLoadedAfterRegeneration(): void {
    const currentUrl = this.deployedUrl$.value;

    if (!currentUrl || currentUrl === 'ERROR_DEPLOYMENT_FAILED') {
      return;
    }

    this.urlValidationError$.next('');

    this.urlSafe = undefined;
    this.cdr.detectChanges();

    setTimeout(() => {

      if (this.isValidPreviewUrl(currentUrl)) {

        const refreshUrl = this.addCacheBustingToUrl(currentUrl);
        this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl(refreshUrl);

        this.isUrlValidated$.next(true);
        this.isUrlAvailable$.next(true);
        this.isIframeReady$.next(true);

        this.isPreviewTabEnabled = true;
        this.isPreviewLoading$.next(false);
        this.previewIcon$.next('bi-eye');
        this.previewError$.next(false);

        if (this.currentView$.value !== 'preview') {
          this.currentView$.next('preview');
        }

        this.cdr.detectChanges();
      } else {
        this.showDeploymentErrorInPreview();
      }
    }, 100);
  }

  private restoreIframeAfterRegenerationWithoutUrl(): void {

    this.isPreviewLoading$.next(false);
    this.previewIcon$.next('bi-eye');
    this.previewError$.next(false);

    this.isUrlValidated$.next(true);
    this.isUrlAvailable$.next(true);
    this.isIframeReady$.next(true);

    this.urlValidationError$.next('');

    this.isPreviewTabEnabled = true;

    if (this.currentView$.value !== 'preview') {
      this.currentView$.next('preview');
    }

    if (!this.urlSafe) {
      this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl('about:blank');
    }

    this.cdr.detectChanges();
  }

  private addCacheBustingToUrl(url: string): string {
    try {
      const urlObj = new URL(url);

      urlObj.searchParams.set('_refresh', Date.now().toString());
      const refreshUrl = urlObj.toString();
      return refreshUrl;
    } catch (error) {
      return url;
    }
  }

  private restoreIframeAfterRegenerationError(): void {

    this.isPreviewLoading$.next(false);
    this.previewIcon$.next('bi-eye');
    this.previewError$.next(false);

    const currentUrl = this.deployedUrl$.value || this.codeSharingService.getDeployedUrl() || this.newPreviewUrl;

    if (currentUrl && currentUrl !== 'ERROR_DEPLOYMENT_FAILED') {

      this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl(currentUrl);

      this.isUrlValidated$.next(true);
      this.isUrlAvailable$.next(true);
      this.isIframeReady$.next(true);

    } else {

      this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl('about:blank');
      this.isUrlValidated$.next(true);
      this.isUrlAvailable$.next(true);
      this.isIframeReady$.next(true);

    }

    this.urlValidationError$.next('');

    this.isPreviewTabEnabled = true;

    if (this.currentView$.value !== 'preview') {
      this.currentView$.next('preview');
    }

    this.cdr.detectChanges();
  }





  private getCurrentCodeFiles(): FileModel[] {
    try {

      let currentFiles: FileModel[] = [];

      const fileTreeFiles = this.fileTreePersistenceService.getCurrentFiles();
      if (fileTreeFiles && fileTreeFiles.length > 0) {
        currentFiles = fileTreeFiles;
      }

      else if (this.codeViewer && this.codeViewer.getAllCurrentFiles) {
        const codeViewerFiles = this.codeViewer.getAllCurrentFiles();
        if (codeViewerFiles && codeViewerFiles.length > 0) {
          currentFiles = codeViewerFiles;
        }
      }

      else if (this.codeViewer && this.codeViewer.files && this.codeViewer.files.length > 0) {
        currentFiles = this.codeViewer.files;
      }

      else if (this.files$.value && this.files$.value.length > 0) {
        currentFiles = this.files$.value;
      }

      else {
        const generatedCode = this.codeSharingService.getGeneratedCode();
        if (generatedCode) {
          currentFiles = this.convertGeneratedCodeToFileModels(generatedCode);
        } else {
          return [];
        }
      }

      const flattenedFiles = this.flattenFileTree(currentFiles);



      if (flattenedFiles.length === 0) {


        if (this.codeViewer) {
        } else {
        }

        const generatedCode = this.codeSharingService.getGeneratedCode();
        if (generatedCode) {
          if (Array.isArray(generatedCode)) {
          } else if (typeof generatedCode === 'object') {
          }
        }
      }

      return flattenedFiles;
    } catch (error) {
      return [];
    }
  }

  private flattenFileTree(files: FileModel[]): FileModel[] {
    const flatFiles: FileModel[] = [];

    const processFile = (file: FileModel, parentPath: string = '') => {
      if (file.type === 'file') {

        const fullPath = file.fileName || file.name || '';
        const finalPath =
          parentPath && !fullPath.startsWith(parentPath) ? `${parentPath}/${file.name}` : fullPath;

        flatFiles.push({
          ...file,
          name: finalPath,
          fileName: finalPath,
        });
      } else if (file.type === 'folder' && file.children) {

        const folderPath = parentPath ? `${parentPath}/${file.name}` : file.name;
        file.children.forEach(child => processFile(child, folderPath));
      }
    };

    files.forEach(file => processFile(file));
    return flatFiles;
  }

  private convertGeneratedCodeToFileModels(generatedCode: any): FileModel[] {
    const fileModels: FileModel[] = [];

    try {
      if (typeof generatedCode === 'string') {

        try {
          const parsedCode = JSON.parse(generatedCode);
          return this.convertGeneratedCodeToFileModels(parsedCode);
        } catch {

          fileModels.push({
            name: 'index.html',
            type: 'file',
            content: generatedCode,
            fileName: 'index.html',
          });
        }
      } else if (Array.isArray(generatedCode)) {

        for (const item of generatedCode) {
          if (typeof item === 'object' && item !== null) {
            const fileName = item.fileName || item.name || item.path || 'unknown.txt';
            const content = item.content || '';
            fileModels.push({
              name: fileName,
              type: 'file',
              content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
              fileName: fileName,
            });
          }
        }
      } else if (typeof generatedCode === 'object' && generatedCode !== null) {

        for (const [filePath, content] of Object.entries(generatedCode)) {
          fileModels.push({
            name: filePath,
            type: 'file',
            content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
            fileName: filePath,
          });
        }
      }
    } catch (error) {
    }

    return fileModels;
  }















  private handleEditError(errorMessage: string): void {

    if (this.isRegenerationInProgress$.value) {
      this.isRegenerationInProgress$.next(false);
      this.regenerationStartTime = 0;
      this.stopRegenerationPreviewLoading();

      this.restoreIframeAfterRegenerationError();
    }

    this.resetRegenerationCallFlag('Edit error handling');

    const lastMessage = this.lightMessages[this.lightMessages.length - 1];
    if (lastMessage && lastMessage.from === 'ai') {
      lastMessage.text = errorMessage;
    } else {

      this.lightMessages.push({
        text: errorMessage,
        from: 'ai',
        theme: 'light',
      });
    }

    this.isAiResponding = false;

    this.isCodeGenerationLoading$.next(false);
    this.codeRegenerationProgressDescription = '';

    this.cdr.detectChanges();

  }

  handleEnhancedAlternate(): void {
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.style.display = 'none';

    fileInput.addEventListener('change', (event: Event) => {
      const input = event.target as HTMLInputElement;
      if (input.files?.length) {
        alert('File uploaded: ' + input.files[0].name);
      }
    });

    document.body.appendChild(fileInput);
    fileInput.click();
    document.body.removeChild(fileInput);
  }

  handleEnhanceText(): void {
    if (!this.lightPrompt.trim()) {
      return;
    }

    this.rightIcons[1].status = 'disable';


    this.lightMessages.push({
      text: 'Enhancing your prompt to provide more detailed instructions...',
      from: 'ai',
      theme: 'light',
    });

    this.promptService.enhancePrompt(this.lightPrompt, 'Generate Application').subscribe({
      next: (response: any) => {
        if (response && response.code && response.status_code === 200) {

          this.lightPrompt = response.code;

          this.lightMessages.push({
            text:
              "I've enhanced your prompt to provide more detailed instructions. Here's the enhanced version:\n\n" +
              response.code,
            from: 'ai',
            theme: 'light',
          });

        } else {

          this.lightMessages.push({
            text: "I couldn't enhance your prompt. Please try again with more details.",
            from: 'ai',
            theme: 'light',
          });
        }
      },
      error: (_error: any) => {

        this.lightMessages.push({
          text: 'I encountered an error while enhancing your prompt. Please try again.',
          from: 'ai',
          theme: 'light',
        });
      },
      complete: () => {
        this.rightIcons[1].status = 'active';
      },
    });
  }

  onIconClick(_iconName: string): void {

  }

  toggleExportModal(): void {

    this.isExperienceStudioModalOpen$.next(!this.isExperienceStudioModalOpen$.value);

    if (this.isExperienceStudioModalOpen$.value) {
    } else {
    }

  }

  copyToClipboard(inputElement: HTMLInputElement): void {

    try {
      inputElement.select();
      document.execCommand('copy');
      inputElement.setSelectionRange(0, 0);

      this.toastService.success('Link copied to clipboard');
    } catch (error) {
      this.toastService.error('Failed to copy link to clipboard');
    }
  }

  exportToVSCode(): void {

    this.isExperienceStudioModalOpen$.next(false);

    this.toastService.info('Preparing VSCode export...');

    try {

      const generatedCode = this.getCodeFromMultipleSources();

      if (!generatedCode) {
        this.toastService.error(
          'No code available to export. Please ensure code generation is complete.'
        );
        return;
      }

      const appName = this.generateAppNameForDownload();

      const exportFiles = this.prepareFilesForVSCodeExport(generatedCode);

      this.vscodeExportService
        .exportToVSCode({
          projectName: appName,
          files: exportFiles,
          openInVSCode: true,
          downloadFallback: true,
        })
        .subscribe({
          next: result => {
            this.handleVSCodeExportResult(result, appName);
          },
          error: _error => {
            this.toastService.error(
              'Failed to export to VSCode. Please try downloading the project instead.'
            );
          },
        });
    } catch (error) {
      this.toastService.error('Failed to prepare VSCode export. Please try again.');
    }
  }

  private prepareFilesForVSCodeExport(generatedCode: any): any[] {

    const exportFiles: any[] = [];

    try {
      if (typeof generatedCode === 'string') {

        try {
          const parsedCode = JSON.parse(generatedCode);
          this.processCodeForVSCodeExport(parsedCode, exportFiles);
        } catch {

          exportFiles.push({
            name: 'index.html',
            content: generatedCode,
            path: 'index.html',
          });
        }
      } else if (Array.isArray(generatedCode)) {

        for (const item of generatedCode) {
          if (typeof item === 'object' && item !== null) {
            const fileName = item.fileName || item.name || item.path || 'unknown.txt';
            const content = item.content || '';
            exportFiles.push({
              name: fileName,
              content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
              path: fileName,
            });
          }
        }
      } else if (typeof generatedCode === 'object' && generatedCode !== null) {

        this.processCodeForVSCodeExport(generatedCode, exportFiles);
      }

      return exportFiles;
    } catch (error) {
      return [];
    }
  }

  private processCodeForVSCodeExport(codeObject: any, exportFiles: any[]): void {
    for (const [filePath, content] of Object.entries(codeObject)) {
      exportFiles.push({
        name: filePath,
        content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
        path: filePath,
      });
    }
  }

  private handleVSCodeExportResult(result: VSCodeExportResult, appName: string): void {

    switch (result.method) {
      case 'vscode-protocol':
        if (result.success) {
          this.toastService.success('🎉 VSCode opened with your project!');
          this.toastService.info(
            'Your project files have been created and VSCode should be opening now.'
          );
        } else {
          this.toastService.warning('VSCode protocol attempted but may not have worked.');
          this.toastService.info("If VSCode didn't open, please check your VSCode installation.");
        }
        break;

      case 'download-fallback':
        const downloadFileName = result.downloadFileName || `${appName.toLowerCase()}.zip`;
        this.toastService.success(`📦 Project downloaded as "${downloadFileName}"`);
        this.toastService.info(
          `💡 Extract ${downloadFileName} → Open VSCode → File → Open Folder → Select extracted folder`
        );
        this.toastService.info(
          '🔧 For best experience: Open the .code-workspace file included in the download'
        );
        break;

      case 'manual-instructions':
        this.toastService.warning('⚠️ Please copy the files manually to your VSCode project');
        break;

      default:
        this.toastService.error(
          '❌ Unknown export method. Please try downloading the project instead.'
        );
        break;
    }

    if (result.success && result.method === 'vscode-protocol') {
      setTimeout(() => {
        this.toastService.info(
          '💡 Tip: Look for the new project folder and open the .code-workspace file in VSCode for the best experience!'
        );
      }, 3000);
    }

  }



  exportToAzure(): void {

    this.isExperienceStudioModalOpen$.next(false);

    this.toastService.info('Preparing Azure export...');

    setTimeout(() => {
      this.toastService.success('Project exported to Azure');
    }, 1500);
  }

  // ENHANCED: Clone in VS Code functionality with SSE metadata integration
  cloneInVSCode(): void {
    // Prevent multiple simultaneous clone operations
    if (this.isCloneLoading$.value) {
      return;
    }

    this.isExperienceStudioModalOpen$.next(false);
    this.isCloneLoading$.next(true);

    try {
      // ENHANCED: Primary approach - Use repository metadata from SSE events
      const repositoryMetadata = this.generationStateService.getCurrentRepositoryMetadata();

      if (repositoryMetadata && repositoryMetadata.cloneUrl) {
        console.log('🔗 Using repository URL from SSE metadata:', repositoryMetadata.cloneUrl);
        this.performVSCodeClone(repositoryMetadata.cloneUrl);
      } else {
        // ENHANCED: Fallback approach - Generate repository URL based on project context
        console.log('⚠️ No SSE repository metadata available, using fallback URL generation');
        const fallbackUrl = this.generateRepositoryUrl();

        if (fallbackUrl) {
          console.log('🔗 Using fallback repository URL:', fallbackUrl);
          this.performVSCodeClone(fallbackUrl);
        } else {
          this.toastService.error('Unable to generate repository URL. Please ensure project is properly configured.');
          return;
        }
      }

    } catch (error) {
      console.error('❌ Failed to initiate VS Code clone:', error);
      this.toastService.error('Failed to initiate VS Code clone. Please try copying the repository URL manually.');
    } finally {
      this.isCloneLoading$.next(false);
    }
  }

  // ENHANCED: Perform VS Code clone operation with given repository URL
  private performVSCodeClone(repositoryUrl: string): void {
    const vscodeCloneUrl = `vscode://vscode.git/clone?url=${encodeURIComponent(repositoryUrl)}`;

    this.toastService.info('Opening VS Code to clone repository...');

    // Try to open VS Code with clone URL
    const vscodeWindow = window.open(vscodeCloneUrl, '_blank');

    if (vscodeWindow) {
      this.toastService.success('VS Code should open with clone dialog!');
      setTimeout(() => {
        this.toastService.info('💡 If VS Code didn\'t open, ensure it\'s installed and try copying the repository URL manually.');
      }, 3000);
    } else {
      // Fallback: copy URL to clipboard and show instructions
      this.copyRepositoryUrlToClipboard(repositoryUrl);
    }
  }

  // ENHANCED: Generate fallback repository URL based on project context
  private generateRepositoryUrl(): string {
    try {
      console.log('🔧 Generating fallback repository URL based on project context');

      // Generate Azure DevOps repository URL based on project context
      const organizationName = 'ascendion'; // Default organization
      const projectName = 'elderwand'; // Default project name

      // Use project ID or name to create a unique repository name
      let repositoryName = 'generated-project';

      if (this.projectId) {
        repositoryName = `project-${this.projectId}`;
        console.log('🆔 Using project ID for repository name:', repositoryName);
      } else if (this.projectName) {
        repositoryName = this.projectName.toLowerCase().replace(/[^a-z0-9]/g, '-');
        console.log('📝 Using project name for repository name:', repositoryName);
      } else {
        const appName = this.generateAppNameForDownload();
        repositoryName = appName.toLowerCase().replace(/[^a-z0-9]/g, '-');
        console.log('🏷️ Using generated app name for repository name:', repositoryName);
      }

      // Generate Azure DevOps repository URL
      const repositoryUrl = `https://dev.azure.com/${organizationName}/${projectName}/_git/${repositoryName}`;
      console.log('🔗 Generated fallback repository URL:', repositoryUrl);

      return repositoryUrl;
    } catch (error) {
      console.error('❌ Failed to generate fallback repository URL:', error);
      return '';
    }
  }

  // ENHANCEMENT: Fallback method to copy repository URL to clipboard
  private copyRepositoryUrlToClipboard(repositoryUrl: string): void {
    try {
      navigator.clipboard.writeText(repositoryUrl).then(() => {
        this.toastService.success('Repository URL copied to clipboard!');
        this.toastService.info('💡 Open VS Code → View → Command Palette → "Git: Clone" → Paste URL');
      }).catch(() => {
        this.showRepositoryUrlFallback(repositoryUrl);
      });
    } catch (error) {
      this.showRepositoryUrlFallback(repositoryUrl);
    }
  }

  // ENHANCEMENT: Show repository URL in a modal/alert as final fallback
  private showRepositoryUrlFallback(repositoryUrl: string): void {
    this.toastService.warning('Please copy this repository URL manually:');
    setTimeout(() => {
      alert(`Repository URL: ${repositoryUrl}\n\nTo clone in VS Code:\n1. Open VS Code\n2. View → Command Palette\n3. Type "Git: Clone"\n4. Paste the URL above`);
    }, 500);
  }

  // ENHANCEMENT: Server-side download with JSZip fallback
  private async downloadProjectFromServer(): Promise<boolean> {
    if (!this.projectId) {
      this.toastService.error('Project ID not available for download');
      return false;
    }

    const userSignature = this.userSignatureService.getUserSignatureSync();
    const params = new HttpParams()
      // .set('project_id', this.projectId)
      .set('user_signature', userSignature);

    try {
      this.toastService.info('Downloading from server...');

      const response = await firstValueFrom(
        this.http.get(`${environment.apiUrl}/download/project/${this.projectId}`, {
          params,
          responseType: 'blob',
          observe: 'response'
        }).pipe(
          timeout(60000), // 60 second timeout
          takeUntilDestroyed(this.destroyRef)
        )
      );

      if (response && response.body) {
        // Extract filename from Content-Disposition header or use default
        const contentDisposition = response.headers.get('Content-Disposition');
        let filename = `project_${this.projectId}.zip`;

        if (contentDisposition) {
          const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
          if (filenameMatch && filenameMatch[1]) {
            filename = filenameMatch[1].replace(/['"]/g, '');
          }
        }

        // Trigger download
        const url = URL.createObjectURL(response.body);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        this.toastService.success(`Project downloaded successfully as ${filename}`);
        return true;
      }

      return false;
    } catch (error) {

      if (error instanceof HttpErrorResponse) {
        if (error.status >= 400 && error.status < 500) {
          this.toastService.warning('Server download unavailable, using local generation...');
        } else {
          this.toastService.warning('Server temporarily unavailable, using local generation...');
        }
      } else {
        this.toastService.warning('Download timeout, using local generation...');
      }

      return false;
    }
  }

  async downloadProject(): Promise<void> {
    // ENHANCEMENT: Prevent multiple simultaneous downloads
    if (this.isDownloadLoading$.value) {
      return;
    }

    this.isExperienceStudioModalOpen$.next(false);
    this.isDownloadLoading$.next(true);

    try {
      // ENHANCEMENT: Try server-side download first, fallback to JSZip
      const serverDownloadSuccess = await this.downloadProjectFromServer();

      if (serverDownloadSuccess) {
        return; // Server download succeeded, we're done
      }

      // Fallback to JSZip method
      this.toastService.info('Preparing download...');

      let generatedCode = this.getCodeFromMultipleSources();

      if (!generatedCode) {
        this.toastService.error(
          'No code available to download. Please ensure code generation is complete.'
        );
        return;
      }

      const appName = this.generateAppNameForDownload();

      const zip = new JSZip();

      const projectFolder = zip.folder(appName);

      if (!projectFolder) {
        throw new Error('Failed to create project folder in zip');
      }

      this.toastService.info('Processing files...');
      await this.addFilesToZip(projectFolder, generatedCode);

      this.addProjectMetadata(projectFolder, appName);

      this.toastService.info('Creating zip file...');
      const zipBlob = await zip.generateAsync({
        type: 'blob',
        compression: 'DEFLATE',
        compressionOptions: {
          level: 6,
        },
      });

      this.triggerDownload(zipBlob, appName);

      this.toastService.success(`Project "${appName}" downloaded successfully`);

    } catch (error) {
      this.toastService.error('Error creating project download. Please try again.');
    } finally {
      // ENHANCEMENT: Always cleanup loading state
      this.isDownloadLoading$.next(false);
    }
  }

  private getCodeFromMultipleSources(): any {

    let generatedCode = this.codeSharingService.getGeneratedCode();
    if (generatedCode) {
      return generatedCode;
    }

    let currentFiles: any = null;
    this.files.subscribe(files => (currentFiles = files)).unsubscribe();
    if (currentFiles && currentFiles.length > 0) {
      return currentFiles;
    }

    if (this.artifactsData && this.artifactsData.length > 0) {
      const codeArtifacts = this.artifactsData.filter(
        artifact => artifact.type === 'file' || artifact.type === 'code'
      );
      if (codeArtifacts.length > 0) {
        return codeArtifacts;
      }
    }

    return null;
  }

  private triggerDownload(zipBlob: Blob, appName: string): void {
    try {

      const url = URL.createObjectURL(zipBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${appName}.zip`;
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();

      document.body.removeChild(link);
      URL.revokeObjectURL(url);

    } catch (error) {
      throw new Error('Failed to trigger download');
    }
  }

  private generateAppNameForDownload(): string {

    if (this.appName) {
      return this.appName;
    }

    if (this.projectName) {
      return this.projectName.toLowerCase().replace(/[^a-z0-9]/g, '-');
    }

    const promptData = this.promptService.getCurrentPromptData();
    if (promptData?.selectedCardTitle) {
      const baseName = promptData.selectedCardTitle.toLowerCase().replace(/[^a-z0-9]/g, '-');
      return `${baseName}-app`;
    }

    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
    return `generated-app-${timestamp}`;
  }

  private async addFilesToZip(projectFolder: JSZip, generatedCode: any): Promise<void> {
    if (typeof generatedCode === 'string') {

      try {
        const parsedCode = JSON.parse(generatedCode);
        await this.processCodeObject(projectFolder, parsedCode);
      } catch {

        projectFolder.file('index.html', generatedCode);
      }
    } else if (Array.isArray(generatedCode)) {

      for (const item of generatedCode) {
        if (typeof item === 'object' && item !== null) {
          const fileName = item.fileName || item.name || item.path || 'unknown.txt';
          const content = item.content || '';
          this.addFileToZipWithPath(projectFolder, fileName, content);
        }
      }
    } else if (typeof generatedCode === 'object' && generatedCode !== null) {

      await this.processCodeObject(projectFolder, generatedCode);
    }
  }

  private async processCodeObject(projectFolder: JSZip, codeObject: any): Promise<void> {
    for (const [filePath, content] of Object.entries(codeObject)) {
      const fileContent = typeof content === 'string' ? content : JSON.stringify(content, null, 2);
      this.addFileToZipWithPath(projectFolder, filePath, fileContent);
    }
  }

  private addFileToZipWithPath(projectFolder: JSZip, filePath: string, content: string): void {

    const cleanPath = filePath.replace(/^\/+/, '');

    const pathParts = cleanPath.split('/');
    const fileName = pathParts.pop() || 'unknown.txt';

    let currentFolder = projectFolder;
    for (const folderName of pathParts) {
      if (folderName.trim()) {
        const existingFolder = currentFolder.folder(folderName);
        currentFolder = existingFolder || currentFolder.folder(folderName)!;
      }
    }

    currentFolder.file(fileName, content);
  }

  private addProjectMetadata(projectFolder: JSZip, appName: string): void {

    const readmeContent = this.generateReadmeContent(appName);
    projectFolder.file('README.md', readmeContent);

  }



  private generateReadmeContent(appName: string): string {
    const currentDate = new Date().toLocaleDateString();
    return `# ${appName}

Generated on: ${currentDate}

## Description
This project was generated using the Experience Studio platform.

## Getting Started

### Prerequisites
- Node.js (version 14 or higher)
- npm or yarn

### Installation
\`\`\`bash
npm install
\`\`\`

### Running the Application
\`\`\`bash
npm start
\`\`\`

### Building for Production
\`\`\`bash
npm run build
\`\`\`

## Project Structure
- \`src/\` - Source code files
- \`public/\` - Static assets

## Support
For support and questions, please refer to the Experience Studio documentation.
`;
  }

  generateUnitTests(): void {

    this.isExperienceStudioModalOpen$.next(false);

    this.toastService.info('Generating unit tests...');

    setTimeout(() => {
      this.toastService.success('Unit tests generated successfully');
    }, 2000);
  }

  public handlePreviewTabClick(): void {

    const currentDeployedUrl = this.deployedUrl$.value;

    if (currentDeployedUrl || this.isInFailedState) {
      this.onTabClick('preview');
    } else {

    }
  }

  public handleEnhancedPreviewTabClick(): void {
    const currentState = this.currentPreviewTabState;

    if (currentState.isEnabled || currentState.hasError) {
      this.onTabClick('preview');
    } else if (currentState.isLoading) {

    } else {

    }
  }

  public handleEnhancedCodeTabClick(): void {
    const currentState = this.currentCodeTabState;

    if (currentState.isEnabled || currentState.hasError) {
      this.onTabClick('code');
    } else if (currentState.isLoading) {

    } else {

    }
  }

  generateAPI(): void {

    this.isExperienceStudioModalOpen$.next(false);

    this.toastService.info('Generating API...');

    setTimeout(() => {
      this.toastService.success('API generated successfully');
    }, 2000);
  }

  onTabClick(tab: string): void {

    if (this.shouldPreventTabSwitching(tab)) {
      return;
    }

    if (this.tabTransitionInProgress()) {
      return;
    }

    this.userSelectedTab = true;

    this.startTabTransition(tab);

    switch (tab) {
      case 'overview':
        this.toggleOverviewViewEnhanced();
        break;
      case 'preview':
        this.togglePreviewViewEnhanced();
        break;
      case 'code':
        // ENHANCEMENT: Allow code tab navigation when template files are available OR code generation is complete
        if (this.canShowCodeTabContent && !this.previewError$.value) {
          this.toggleCodeViewEnhanced();
        } else {
          this.completeTabTransition();
        }
        break;
      case 'logs':

        if (this.isArtifactsTabEnabledWithLogs) {
          this.toggleLogsViewEnhanced();
        } else {
          this.completeTabTransition();
        }
        break;
      case 'artifacts':

        if (this.isArtifactsTabEnabledWithLogs) {
          this.toggleArtifactsViewEnhanced();
        } else {
          this.completeTabTransition();
        }
        break;
      default:
        this.completeTabTransition();
    }
  }

  @HostListener('window:message', ['$event'])
  onMessage(event: MessageEvent) {
    if (event.data && event.data.type === 'elementSelected') {
      this.selectedElement = event.data.element;
      this.showEditorIcon = true;
    }
  }

  onIframeLoad(_event: Event): void {
    this.isPreviewLoading$.next(false);

    if (this.isRegenerationInProgress$.value) {
      this.isRegenerationInProgress$.next(false);
      this.regenerationStartTime = 0;
    }

    if (this.isCodeGenerationComplete && !this.hasAddedInitialGenerationAccordion) {
      this.addGenerationAccordionToChat();
      this.hasAddedInitialGenerationAccordion = true;
    }

    if (this.isElementSelectionMode) {
      this.injectSelectionScripts();
    }
  }

  onIframeError(_event: any): void {
    this.previewError$.next(true);
    this.errorDescription$.next(
      'Failed to load preview. The deployed application may not be accessible.'
    );
  }

  toggleElementSelectionMode(): void {
    this.isElementSelectionMode = !this.isElementSelectionMode;

    if (this.isElementSelectionMode) {
      if (this.currentView$.value !== 'preview') {
        this.togglePreviewView();
      }

      this.injectSelectionScripts();

    } else {

      this.cleanupSelectionMode();

      if (this.selectedElement) {
        this.clearSelectedElement();
      }
    }

    this.cdr.detectChanges();
  }

  private injectSelectionScripts(): void {
    try {

      const iframe = document.querySelector('.preview-frame') as HTMLIFrameElement;
      if (!iframe || !iframe.contentWindow) {
        return;
      }

      let isCrossOrigin = false;
      try {

        const testAccess = iframe.contentDocument;
        if (!testAccess) {
          isCrossOrigin = true;
        }
      } catch (e) {
        isCrossOrigin = true;
      }

      if (isCrossOrigin) {

        iframe.contentWindow.postMessage(
          {
            type: 'initElementSelection',
          },
          '*'
        );

        window.addEventListener('message', this.handleIframeMessage);
        return;
      }

      if (!iframe.contentDocument) {
        return;
      }

      const style = iframe.contentDocument.createElement('style');
      style.id = 'element-selection-styles';
      style.textContent = `
        .element-hover {
          outline: 2px dashed #007bff !important;
          outline-offset: 2px !important;
          cursor: pointer !important;
          position: relative !important;
        }

        .element-selected {
          outline: 3px solid #28a745 !important;
          outline-offset: 3px !important;
          position: relative !important;
        }

        .element-tooltip {
          position: fixed;
          background: rgba(0, 0, 0, 0.8);
          color: white;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          z-index: 10000;
          pointer-events: none;
        }
      `;

      iframe.contentDocument.head.appendChild(style);

      const script = iframe.contentDocument.createElement('script');
      script.id = 'element-selection-script';
      script.textContent = `
        (function() {
          let hoveredElement = null;
          let tooltip = null;

          // Create tooltip element
          function createTooltip() {
            tooltip = document.createElement('div');
            tooltip.className = 'element-tooltip';
            document.body.appendChild(tooltip);
          }

          // Update tooltip position and content
          function updateTooltip(element, event) {
            if (!tooltip) createTooltip();

            const tagName = element.tagName.toLowerCase();
            const id = element.id ? '#' + element.id : '';
            const classes = Array.from(element.classList)
              .filter(cls => cls !== 'element-hover' && cls !== 'element-selected')
              .map(cls => '.' + cls)
              .join('');

            tooltip.textContent = tagName + id + classes;
            tooltip.style.top = (event.clientY + 15) + 'px';
            tooltip.style.left = (event.clientX + 10) + 'px';
          }

          // Hide tooltip
          function hideTooltip() {
            if (tooltip) {
              tooltip.style.display = 'none';
            }
          }

          // Show tooltip
          function showTooltip() {
            if (tooltip) {
              tooltip.style.display = 'block';
            }
          }

          // Handle mouseover event
          function handleMouseOver(event) {
            // Skip if the target is the body or html element
            if (event.target === document.body || event.target === document.documentElement) {
              return;
            }

            // Remove hover class from previous element
            if (hoveredElement && hoveredElement !== event.target) {
              hoveredElement.classList.remove('element-hover');
            }

            // Add hover class to current element
            hoveredElement = event.target;
            hoveredElement.classList.add('element-hover');

            // Update tooltip
            updateTooltip(hoveredElement, event);
            showTooltip();

            // Stop event propagation
            event.stopPropagation();
          }

          // Handle mousemove event
          function handleMouseMove(event) {
            if (hoveredElement) {
              updateTooltip(hoveredElement, event);
            }
          }

          // Handle mouseout event
          function handleMouseOut(event) {
            // Only remove the hover class if we're leaving the element
            if (hoveredElement && !hoveredElement.contains(event.relatedTarget)) {
              hoveredElement.classList.remove('element-hover');
              hoveredElement = null;
              hideTooltip();
            }

            event.stopPropagation();
          }

          // Handle click event
          function handleClick(event) {
            // Skip if the target is the body or html element
            if (event.target === document.body || event.target === document.documentElement) {
              return;
            }

            // Get the clicked element
            const element = event.target;

            // Remove hover class
            element.classList.remove('element-hover');

            // Add selected class
            element.classList.add('element-selected');

            // Get element details
            const tagName = element.tagName.toLowerCase();
            const id = element.id || null;
            const classes = Array.from(element.classList)
              .filter(cls => cls !== 'element-hover' && cls !== 'element-selected')
              .join(' ');

            // Get element HTML
            const outerHTML = element.outerHTML;

            // Get computed styles
            const computedStyle = window.getComputedStyle(element);
            const cssProperties = {};
            for (let i = 0; i < computedStyle.length; i++) {
              const prop = computedStyle[i];
              cssProperties[prop] = computedStyle.getPropertyValue(prop);
            }

            // Get element path
            const getElementPath = (el) => {
              if (!el) return '';
              if (el === document.body) return 'body';

              let path = '';
              let current = el;

              while (current && current !== document.body) {
                let selector = current.tagName.toLowerCase();

                if (current.id) {
                  selector += '#' + current.id;
                } else {
                  const siblings = Array.from(current.parentNode.children)
                    .filter(child => child.tagName === current.tagName);

                  if (siblings.length > 1) {
                    const index = siblings.indexOf(current) + 1;
                    selector += ':nth-of-type(' + index + ')';
                  }
                }

                path = selector + (path ? ' > ' + path : '');
                current = current.parentNode;
              }

              return 'body > ' + path;
            };

            const elementPath = getElementPath(element);

            // Send message to parent window
            window.parent.postMessage({
              type: 'elementSelected',
              data: {
                tagName,
                id,
                classes,
                html: outerHTML,
                css: cssProperties,
                path: elementPath
              }
            }, '*');

            // Stop event propagation and prevent default
            event.stopPropagation();
            event.preventDefault();
          }

          // Add event listeners
          document.addEventListener('mouseover', handleMouseOver, true);
          document.addEventListener('mousemove', handleMouseMove, true);
          document.addEventListener('mouseout', handleMouseOut, true);
          document.addEventListener('click', handleClick, true);

          // Function to clean up element selection
          function cleanupElementSelection() {
            document.removeEventListener('mouseover', handleMouseOver, true);
            document.removeEventListener('mousemove', handleMouseMove, true);
            document.removeEventListener('mouseout', handleMouseOut, true);
            document.removeEventListener('click', handleClick, true);

            // Remove any hover or selected classes
            const hovered = document.querySelector('.element-hover');
            if (hovered) hovered.classList.remove('element-hover');

            const selected = document.querySelector('.element-selected');
            if (selected) selected.classList.remove('element-selected');

            // Remove tooltip
            if (tooltip) {
              document.body.removeChild(tooltip);
              tooltip = null;
            }

            // Remove style and script elements
            const style = document.getElementById('element-selection-styles');
            if (style) style.parentNode.removeChild(style);

            const script = document.getElementById('element-selection-script');
            if (script) script.parentNode.removeChild(script);
          }

          // Store the cleanup function for direct access (same-origin case)
          window._elementSelectionCleanup = cleanupElementSelection;

          // Also listen for cleanup messages (cross-origin case)
          window.addEventListener('message', function(event) {
            if (event.data && event.data.type === 'cleanupElementSelection') {

              cleanupElementSelection();
            }
          });
        })();
      `;

      iframe.contentDocument.body.appendChild(script);

      window.addEventListener('message', this.handleIframeMessage);
    } catch (error) {
    }
  }

  private handleIframeMessage = (event: MessageEvent): void => {

    if (event.data && event.data.type === 'elementSelected') {

      this.selectedElementTagName = event.data.data.tagName;
      this.selectedElementId = event.data.data.id;
      this.selectedElementHTML = event.data.data.html;
      this.selectedElementCSS = JSON.stringify(event.data.data.css, null, 2);
      this.selectedElementPath = event.data.data.path;

      this.addSelectedElementToChat(event.data.data);

      this.isElementSelectionMode = false;
      this.cleanupSelectionMode();

      this.ngZone.run(() => {
        this.cdr.detectChanges();
      });
    }
  };

  private addSelectedElementToChat(elementData: any): void {

    const displayHTML = this.formatHTMLForDisplay(elementData.html);

    const message = `## Selected Element: \`<${elementData.tagName}>\`

\`\`\`html
${displayHTML}
\`\`\`

**Element Path:** \`${elementData.path}\`

Would you like to modify this element? Please describe the changes you'd like to make.`;

    this.lightMessages.push({
      text: message,
      from: 'ai',
      theme: 'light',
    });

    this.cdr.detectChanges();
  }

  private formatHTMLForDisplay(html: string): string {

    if (html.length > 500) {
      html = html.substring(0, 500) + '...';
    }

    return html
      .replace(/></g, '>\n<')
      .replace(/(<[^\/].*?>)/g, '$1')
      .replace(/(<\/.*?>)/g, '$1');
  }

  private cleanupSelectionMode(): void {
    try {

      const iframe = document.querySelector('.preview-frame') as HTMLIFrameElement;
      if (!iframe || !iframe.contentWindow) {
        return;
      }

      try {

        if (
          iframe.contentWindow &&
          'function' === typeof (iframe.contentWindow as any)._elementSelectionCleanup
        ) {
          (iframe.contentWindow as any)._elementSelectionCleanup();
        } else {

          iframe.contentWindow.postMessage({ type: 'cleanupElementSelection' }, '*');
        }
      } catch (crossOriginError) {

        iframe.contentWindow.postMessage({ type: 'cleanupElementSelection' }, '*');
      }

      window.removeEventListener('message', this.handleIframeMessage);

    } catch (error) {
    }
  }

  private clearSelectedElement(): void {
    this.selectedElementTagName = null;
    this.selectedElementId = null;
    this.selectedElementHTML = null;
    this.selectedElementCSS = null;
    this.selectedElementPath = null;
    this.selectedElement = null;
  }

  onEditButtonClick(): void {

    this.toggleElementSelectionMode();
  }

  sendElementModificationRequest(message: string): void {
    if (!this.selectedElementHTML || !this.selectedElementPath) {
      return;
    }

    const userSignature = this.userSignatureService.getUserSignatureSync();

    const payload = {
      elementHtml: this.selectedElementHTML,
      elementCss: this.selectedElementCSS,
      elementPath: this.selectedElementPath,
      userMessage: message,
      projectId: this.projectId,
      jobId: this.jobId,
      userSignature: userSignature,
    };

    this.lightMessages.push({
      text: message,
      from: 'user',
      theme: 'light',
    });

    this.lightMessages.push({
      text: "I'm processing your request to modify the selected element. This may take a moment...",
      from: 'ai',
      theme: 'light',
    });

    this.codeGenerationService.modifyElement(payload).subscribe({
      next: response => {

        const lastAiMessage = this.lightMessages.find(msg => msg.from === 'ai');
        if (lastAiMessage) {
          lastAiMessage.text = `I've updated the element based on your request. Here's what I did:

\`\`\`html
${this.formatHTMLForDisplay(response.modifiedHtml || '')}
\`\`\`

The changes should be reflected in the preview shortly.`;
        }

        this.clearSelectedElement();

        this.cdr.detectChanges();
      },
      error: _error => {

        const lastAiMessage = this.lightMessages.find(msg => msg.from === 'ai');
        if (lastAiMessage) {
          lastAiMessage.text = `I encountered an error while trying to modify the element. Please try again or select a different element.`;
        }

        this.cdr.detectChanges();
      },
    });
  }

  onEditorIconClick() {

    this.toggleElementSelectionMode();
  }

  onRetryClick() {

    this.previewError$.next(false);

    this.previewTabName$.next('Preview');
    this.setPreviewTabLoading('Retrying code generation...');

    this.isCodeGenerationComplete = false;

    this.isPromptBarEnabled = false;

    this.isCodeActive$.next(false);
    this.isPreviewActive$.next(true);

    this.toastService.info('Retrying code generation...');

    if (this.projectId && this.jobId && !this.isUIDesignMode$.value) {



      this.pollingService.resetLogsButKeepStep();

      this.enableSSEEventFilteringForRetry();

      this.synchronizeStepperWithSSEStream();

    } else if (this.isUIDesignMode$.value) {
    }

    if (this.projectId && this.jobId) {
      this.updatePollingStatus(true);

      this.isLoading$.next(true);
      this.isPreviewLoading$.next(true);

      this.togglePreviewView();
    } else {

      this.navigateToHome();
    }

  }

  private enableSSEEventFilteringForRetry(): void {

    if (!this.projectId || !this.jobId) {
      return;
    }

    const retryTimestamp = Date.now();

    this.enhancedSSEService.enableRetryEventFiltering(retryTimestamp);

  }

  private synchronizeStepperWithSSEStream(): void {

    if (!this.projectId || !this.jobId) {
      return;
    }

    this.stepperStateService.resynchronizeWithSSEStream(this.projectId, this.jobId);

  }

  private sortFilesByTechnology(files: FileModel[], technology: string): FileModel[] {
    if (!files || files.length === 0) {
      return files;
    }

    const filePriority: { [key: string]: { [key: string]: number } } = {
      react: {

        'src/components/': 100,
        'components/': 100,
        'src/pages/': 99,
        'pages/': 99,
        'src/services/': 98,
        'services/': 98,
        'src/utils/': 97,
        'utils/': 97,
        'src/hooks/': 96,
        'hooks/': 96,
        'src/context/': 95,
        'context/': 95,
        'src/assets/': 94,
        'assets/': 94,
        'src/styles/': 93,
        'styles/': 93,
        'src/App.js': 92,
        'src/App.jsx': 92,
        'src/App.tsx': 92,
        'App.js': 92,
        'App.jsx': 92,
        'App.tsx': 92,
        'src/App.css': 91,
        'App.css': 91,
        'src/index.js': 90,
        'src/index.jsx': 90,
        'src/index.tsx': 90,
        'index.js': 90,
        'index.jsx': 90,
        'index.tsx': 90,
        'public/index.html': 89,
        'index.html': 89,
        'public/': 88,
        '.js': 80,
        '.jsx': 80,
        '.tsx': 80,
        '.css': 79,
        '.scss': 79,
        '.html': 78,

        'tsconfig.json': 20,
        'staticwebapp.config.json': 19,
        'package-lock.json': 18,
        'eslint.config.js': 17,
        'azure-pipelines.yml': 16,
        'angular.json': 15,
        '.npmrc': 14,
        '.hintrc': 13,
        '.editorconfig': 12,
        'projects/playground/': 11,
        'projects/play-comp-library/': 10,
        'projects/': 9,
        'Pipeline/play-library.yml': 8,
        'Pipeline/': 7,
        'node_modules/': 6,
        'dist/': 5,
        '.vscode/': 4,
        '.angular/': 3,
        '.gitignore': 2,
        'README.md': 1,
        'package.json': 0,
      },
      angular: {

        'src/app/components/': 100,
        'components/': 100,
        'src/app/pages/': 99,
        'pages/': 99,
        'src/app/services/': 98,
        'services/': 98,
        'src/app/models/': 97,
        'models/': 97,
        'src/app/directives/': 96,
        'directives/': 96,
        'src/app/pipes/': 95,
        'pipes/': 95,
        'src/app/guards/': 94,
        'guards/': 94,
        'src/app/interceptors/': 93,
        'interceptors/': 93,
        'src/assets/': 92,
        'assets/': 92,
        'src/environments/': 91,
        'environments/': 91,
        'src/app/app.component.ts': 90,
        'app.component.ts': 90,
        'src/app/app.component.html': 89,
        'app.component.html': 89,
        'src/app/app.component.scss': 88,
        'src/app/app.component.css': 88,
        'app.component.scss': 88,
        'app.component.css': 88,
        'src/app/app-routing.module.ts': 87,
        'app-routing.module.ts': 87,
        'src/app/app.module.ts': 86,
        'app.module.ts': 86,
        'src/main.ts': 85,
        'main.ts': 85,
        'src/index.html': 84,
        'index.html': 84,
        '.ts': 80,
        '.html': 79,
        '.scss': 78,
        '.css': 78,

        'tsconfig.json': 20,
        'staticwebapp.config.json': 19,
        'package-lock.json': 18,
        'eslint.config.js': 17,
        'azure-pipelines.yml': 16,
        'angular.json': 15,
        '.npmrc': 14,
        '.hintrc': 13,
        '.editorconfig': 12,
        'projects/playground/': 11,
        'projects/play-comp-library/': 10,
        'projects/': 9,
        'Pipeline/play-library.yml': 8,
        'Pipeline/': 7,
        'node_modules/': 6,
        'dist/': 5,
        '.vscode/': 4,
        '.angular/': 3,
        '.gitignore': 2,
        'README.md': 1,
        'package.json': 0,
      },
      vue: {

        'src/components/': 100,
        'components/': 100,
        'src/views/': 99,
        'views/': 99,
        'src/pages/': 99,
        'pages/': 99,
        'src/services/': 98,
        'services/': 98,
        'src/utils/': 97,
        'utils/': 97,
        'src/assets/': 96,
        'assets/': 96,
        'src/styles/': 95,
        'styles/': 95,
        'src/store/index.js': 94,
        'src/store/index.ts': 94,
        'store/index.js': 94,
        'store/index.ts': 94,
        'src/router/index.js': 93,
        'src/router/index.ts': 93,
        'router/index.js': 93,
        'router/index.ts': 93,
        'src/App.vue': 92,
        'App.vue': 92,
        'src/main.js': 91,
        'src/main.ts': 91,
        'main.js': 91,
        'main.ts': 91,
        'public/index.html': 90,
        'index.html': 90,
        'public/': 89,
        '.vue': 80,
        '.js': 79,
        '.ts': 79,
        '.scss': 78,
        '.css': 78,
        '.html': 77,

        'tsconfig.json': 20,
        'staticwebapp.config.json': 19,
        'package-lock.json': 18,
        'eslint.config.js': 17,
        'azure-pipelines.yml': 16,
        'angular.json': 15,
        '.npmrc': 14,
        '.hintrc': 13,
        '.editorconfig': 12,
        'projects/playground/': 11,
        'projects/play-comp-library/': 10,
        'projects/': 9,
        'Pipeline/play-library.yml': 8,
        'Pipeline/': 7,
        'node_modules/': 6,
        'dist/': 5,
        '.vscode/': 4,
        '.angular/': 3,
        '.gitignore': 2,
        'README.md': 1,
        'package.json': 0,
      },
    };

    const normalizedTech = technology.toLowerCase();
    const techKey = normalizedTech.includes('react')
      ? 'react'
      : normalizedTech.includes('angular')
        ? 'angular'
        : normalizedTech.includes('vue')
          ? 'vue'
          : 'angular';

    const priorityMap = filePriority[techKey] || filePriority['angular'];

    const sortedFiles = [...files].sort((a, b) => {
      const aName = a.name.toLowerCase();
      const bName = b.name.toLowerCase();

      let aPriority = 1000;
      let aNestedLevel = aName.split('/').length - 1;

      for (const [pattern, priority] of Object.entries(priorityMap)) {
        if (pattern.endsWith('/')) {

          if (aName.includes(pattern)) {
            aPriority = priority;
            break;
          }
        } else if (aName === pattern.toLowerCase()) {

          aPriority = priority;
          break;
        } else if (aName.endsWith(pattern.toLowerCase())) {

          aPriority = priority;
          break;
        }
      }

      let bPriority = 1000;
      let bNestedLevel = bName.split('/').length - 1;

      for (const [pattern, priority] of Object.entries(priorityMap)) {
        if (pattern.endsWith('/')) {

          if (bName.includes(pattern)) {
            bPriority = priority;
            break;
          }
        } else if (bName === pattern.toLowerCase()) {

          bPriority = priority;
          break;
        } else if (bName.endsWith(pattern.toLowerCase())) {

          bPriority = priority;
          break;
        }
      }

      if (aPriority !== bPriority) {
        return bPriority - aPriority;
      }

      if (aNestedLevel !== bNestedLevel) {
        return bNestedLevel - aNestedLevel;
      }

      return aName.localeCompare(bName);
    });

    const nestingStats: Record<string, number> = {};
    sortedFiles.forEach(file => {
      const level = file.name.split('/').length - 1;
      const levelKey = level.toString();
      nestingStats[levelKey] = (nestingStats[levelKey] || 0) + 1;
    });

    return sortedFiles;
  }

  private initializeDefaultPrompt(): void {

    if (this.lightMessages.length === 0) {

      this.appStateService.project$
        .subscribe(projectState => {
          if (projectState.prompt) {

            let userSelectionsSummary = '';

            if (projectState.imageUrl) {
              userSelectionsSummary += '- **Image**:  You provided an image for reference\n';
            }

            if (projectState.technology) {
              userSelectionsSummary += `- **Technology**: ${projectState.technology.charAt(0).toUpperCase() + projectState.technology.slice(1)}\n`;
            }

            if (projectState.designLibrary) {
              userSelectionsSummary += `- **Design Library**: ${projectState.designLibrary.charAt(0).toUpperCase() + projectState.designLibrary.slice(1)}\n`;
            }

            if (projectState.application) {
              userSelectionsSummary += `- **Application Type**: ${projectState.application.charAt(0).toUpperCase() + projectState.application.slice(1)}\n`;
            }

            if (projectState.type) {
              userSelectionsSummary += `- **Generation Type**: ${projectState.type}\n`;
            }

            this.lightMessages = [
              {
                text: projectState.prompt,
                from: 'user',
                theme: 'light',
                imageDataUri: projectState.imageDataUri || undefined,
              },

            ];

            if (projectState.imageDataUri) {
              this.selectedImageDataUri = projectState.imageDataUri;
            }
          } else {

            this.lightMessages = [
              {
                text: "Welcome to the code generation experience. I'll help you create code based on your requirements.",
                from: 'ai',
                theme: 'light',
              },
            ];
          }
        })
        .unsubscribe();
    }
  }

  private startLogStreaming(allLogs: string[]): void {

    if (this.logStreamTimer) {
      clearInterval(this.logStreamTimer);
    }

    if (!allLogs || allLogs.length === 0) {
      this.isStreamingLogs = false;
      return;
    }

    const wasFirstTime = !this.hasLogs;
    if (!this.hasLogs) {
      this.hasLogs = true;
    }

    if (wasFirstTime) {
      this.autoEnableLogsTabIfNeeded();

      this.enableArtifactsTabIfNeeded();
    }

    const newLogs = this.filterNewLogs(allLogs);

    if (newLogs.length === 0) {
      this.isStreamingLogs = false;
      return;
    }

    const processedLogs = this.processLogs(newLogs);

    if (processedLogs.length > 0) {

      this.isStreamingLogs = false;
      this.isTypingLog = false;

      processedLogs.forEach(log => {
        log.visibleContent = log.content || '';
      });

      this.formattedLogMessages = [...this.formattedLogMessages, ...processedLogs];

      this.logMessages = [...this.logMessages, ...newLogs];

      this.enableArtifactsTabIfNeeded();

      setTimeout(() => {
        this.scrollLogsToBottom();
      }, 0);
    }
  }

  private filterNewLogs(allLogs: string[]): string[] {

    const existingRawLogs = new Set(this.formattedLogMessages.map(log => log.rawLog || ''));

    return allLogs.filter(log => {

      if (!log || log.trim() === '') {
        return false;
      }

      if (existingRawLogs.has(log)) {
        return false;
      }

      const logHash = this.createLogHash(log);

      if (this.processedLogHashes.has(logHash)) {
        return false;
      }

      this.processedLogHashes.add(logHash);
      return true;
    });
  }

  private createLogHash(log: string): string {

    const timestamp = this.extractTimestamp(log);
    const content = this.extractLogContent(log);
    const length = log.length;

    return `${timestamp}-${content.substring(0, 50)}-${length}`;
  }

  private processLogs(logs: string[]): any[] {
    const processedLogs: any[] = [];

    const processedJsonLogs = new Set<string>();

    logs.forEach(log => {

      if (!log || log.trim() === '') {
        return;
      }

      const logContent = this.extractLogContent(log);

      if (log.includes('Generated code:')) {
        try {

          const jsonStartIndex = log.indexOf('Generated code:') + 'Generated code:'.length;
          const jsonPart = log.substring(jsonStartIndex).trim();

          if (jsonPart) {
            const codeData = JSON.parse(jsonPart);

            processedJsonLogs.add(log);

            if (typeof codeData === 'object' && codeData !== null) {
              if (Array.isArray(codeData)) {

                codeData.forEach(item => {
                  if (item && item.fileName && item.content) {
                    const codeId = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
                    processedLogs.push({
                      id: codeId,
                      type: 'code',
                      timestamp: this.extractTimestamp(log),
                      path: item.fileName,
                      content: item.content,
                      contentLines: item.content.split('\n').length,
                      contentSize: item.content.length,
                      rawLog: log,
                    });

                    this.expandedCodeLogs.add(codeId);
                  }
                });
                return;
              } else {

                Object.entries(codeData).forEach(([path, content]) => {
                  if (path && content) {
                    const formattedContent =
                      typeof content === 'string' ? content : JSON.stringify(content, null, 2);

                    const codeId = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
                    processedLogs.push({
                      id: codeId,
                      type: 'code',
                      timestamp: this.extractTimestamp(log),
                      path: path,
                      content: formattedContent,
                      contentLines: formattedContent.split('\n').length,
                      contentSize: formattedContent.length,
                      rawLog: log,
                    });

                    this.expandedCodeLogs.add(codeId);
                  }
                });
                return;
              }
            }
          }
        } catch (e) {
        }
      }

      if (log.includes('{') && log.includes('}')) {
        try {

          const jsonStartIndex = log.indexOf('{');
          const jsonEndIndex = log.lastIndexOf('}') + 1;

          if (jsonStartIndex !== -1 && jsonEndIndex > jsonStartIndex) {
            const jsonPart = log.substring(jsonStartIndex, jsonEndIndex);

            const codeData = JSON.parse(jsonPart);

            processedJsonLogs.add(log);

            if (
              codeData &&
              typeof codeData === 'object' &&
              codeData.filesToGenerate &&
              Array.isArray(codeData.filesToGenerate)
            ) {

              const formattedFiles = codeData.filesToGenerate.map((file: string) => {

                let icon = '📄';
                if (file.endsWith('.ts') || file.endsWith('.tsx')) icon = '📘';
                else if (file.endsWith('.js') || file.endsWith('.jsx')) icon = '📙';
                else if (file.endsWith('.css')) icon = '🎨';
                else if (file.endsWith('.html')) icon = '🌐';
                else if (file.endsWith('.json')) icon = '📋';

                return `- ${icon} ${file}`;
              });

              const formattedContent = `# Files to Generate\n\n${formattedFiles.join('\n')}`;

              if (!formattedContent || formattedContent.trim() === '') return;

              const codeId = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
              processedLogs.push({
                id: codeId,
                type: 'code',
                timestamp: this.extractTimestamp(log),
                path: 'Project Structure',
                content: formattedContent,
                contentLines: formattedContent.split('\n').length,
                contentSize: formattedContent.length,
                rawLog: log,
              });

              this.expandedCodeLogs.add(codeId);

              processedLogs.push({
                id: `log-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
                type: 'info',
                timestamp: this.extractTimestamp(log),
                content: `Preparing to generate ${codeData.filesToGenerate.length} files`,
                rawLog: log,
              });

              return;
            }

            if (codeData && typeof codeData === 'object' && codeData.message && codeData.data) {

              if (typeof codeData.data === 'string') {

                let dataString = codeData.data;
                let dataJson = null;

                try {
                  dataJson = JSON.parse(dataString);
                } catch (directParseError) {

                  if (dataString.includes('\\\"') || dataString.includes('\\\\')) {
                    try {

                      dataString = dataString.replace(/\\"/g, '"').replace(/\\\\/g, '\\');
                      dataJson = JSON.parse(dataString);
                    } catch (unescapeError) {}
                  }
                }

                // If we successfully parsed the data field as JSON
                if (dataJson) {
                  // Handle different formats of the data field
                  if (typeof dataJson === 'object' && !Array.isArray(dataJson)) {
                    // Object with file paths as keys
                    // Process each file path and content
                    Object.entries(dataJson).forEach(([path, content]) => {
                      if (path && content) {
                        const formattedContent =
                          typeof content === 'string' ? content : JSON.stringify(content, null, 2);

                        // Skip if formatted content is empty
                        if (!formattedContent || formattedContent.trim() === '') return;

                        const codeId = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
                        processedLogs.push({
                          id: codeId,
                          type: 'code',
                          timestamp: this.extractTimestamp(log),
                          path: path,
                          content: formattedContent,
                          contentLines: formattedContent.split('\n').length,
                          contentSize: formattedContent.length,
                          rawLog: log,
                        });

                        // Auto-expand code logs
                        this.expandedCodeLogs.add(codeId);
                      }
                    });
                  } else if (Array.isArray(dataJson)) {
                    dataJson.forEach(item => {
                      if (item && typeof item === 'object') {
                        const path = item.fileName || item.path || item.name || 'unknown.file';
                        const content = item.content || '';

                        if (path && content) {
                          const formattedContent =
                            typeof content === 'string'
                              ? content
                              : JSON.stringify(content, null, 2);

                          // Skip if formatted content is empty
                          if (!formattedContent || formattedContent.trim() === '') return;

                          const codeId = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
                          processedLogs.push({
                            id: codeId,
                            type: 'code',
                            timestamp: this.extractTimestamp(log),
                            path: path,
                            content: formattedContent,
                            contentLines: formattedContent.split('\n').length,
                            contentSize: formattedContent.length,
                            rawLog: log,
                          });

                          // Auto-expand code logs
                          this.expandedCodeLogs.add(codeId);
                        }
                      }
                    });
                  }

                  // Also add the original message as an info log
                  processedLogs.push({
                    id: `log-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
                    type: 'info',
                    timestamp: this.extractTimestamp(log),
                    content: codeData.message,
                    rawLog: log,
                  });

                  return;
                }
              }
            }

            // Check if this is a key-value pair format (path: content)
            if (typeof codeData === 'object' && codeData !== null) {
              // Handle object format with key-value pairs
              if (!Array.isArray(codeData)) {
                // Process each key-value pair as a separate code block
                Object.entries(codeData).forEach(([path, content]) => {
                  // Skip if content is empty
                  if (!content || (typeof content === 'string' && content.trim() === '')) {
                    return;
                  }

                  // Skip empty content
                  if (!content) return;

                  // Format the content properly
                  const formattedContent =
                    typeof content === 'string' ? content : JSON.stringify(content, null, 2);

                  // Skip if formatted content is empty or just whitespace
                  if (!formattedContent || formattedContent.trim() === '') return;

                  // Calculate content size for proper capsule sizing
                  const contentLines = formattedContent.split('\n').length;
                  const contentSize = formattedContent.length;

                  // Add the code log with a unique ID
                  const codeId = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
                  processedLogs.push({
                    id: codeId,
                    type: 'code',
                    timestamp: this.extractTimestamp(log),
                    path: path || 'unknown.file',
                    content: formattedContent,
                    contentLines: contentLines,
                    contentSize: contentSize,
                    rawLog: log,
                  });

                  // Auto-expand code logs
                  this.expandedCodeLogs.add(codeId);
                });
                return;
              }
              // Handle array format with fileName and content properties
              else if (Array.isArray(codeData) && codeData.length > 0) {
                codeData.forEach(item => {
                  // Skip if content is empty
                  if (
                    !item.content ||
                    (typeof item.content === 'string' && item.content.trim() === '')
                  ) {
                    return;
                  }

                  // Format the content properly
                  const formattedContent =
                    typeof item.content === 'string'
                      ? item.content
                      : JSON.stringify(item.content, null, 2);

                  // Skip if formatted content is empty
                  if (!formattedContent || formattedContent.trim() === '') return;

                  // Add the code log with a unique ID
                  const codeId = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
                  processedLogs.push({
                    id: codeId,
                    type: 'code',
                    timestamp: this.extractTimestamp(log),
                    path: item.fileName || item.path || 'unknown.file',
                    content: formattedContent,
                    contentLines: formattedContent.split('\n').length,
                    contentSize: formattedContent.length,
                    rawLog: log,
                  });

                  // Auto-expand code logs
                  this.expandedCodeLogs.add(codeId);
                });
                return;
              }
              // Handle single file format with fileName and content properties
              else if (
                typeof codeData === 'object' &&
                'fileName' in codeData &&
                'content' in codeData
              ) {
                // Skip if content is empty
                if (
                  !codeData.content ||
                  (typeof codeData.content === 'string' && codeData.content.trim() === '')
                ) {
                  return;
                }

                // Format the content properly
                const formattedContent =
                  typeof codeData.content === 'string'
                    ? codeData.content
                    : JSON.stringify(codeData.content, null, 2);

                // Skip if formatted content is empty or just whitespace
                if (!formattedContent || formattedContent.trim() === '') return;

                // Calculate content size for proper capsule sizing
                const contentLines = formattedContent.split('\n').length;
                const contentSize = formattedContent.length;

                // Add the code log with a unique ID
                const codeId = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
                processedLogs.push({
                  id: codeId,
                  type: 'code',
                  timestamp: this.extractTimestamp(log),
                  path: codeData.fileName || 'unknown.file',
                  content: formattedContent,
                  contentLines: contentLines,
                  contentSize: contentSize,
                  rawLog: log,
                });

                // Auto-expand code logs
                this.expandedCodeLogs.add(codeId);
                return;
              }
            }
          }
        } catch (e) {
        }
      }

      // Check for error logs - always show errors
      if (log.includes('ERROR')) {
        // Skip if content is empty
        if (!logContent || logContent.trim() === '') {
          return;
        }

        processedLogs.push({
          id: `log-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
          type: 'error',
          timestamp: this.extractTimestamp(log),
          content: logContent,
          rawLog: log,
        });
        return;
      }

      // Check for warning logs - always show warnings
      if (log.includes('WARN')) {
        // Skip if content is empty
        if (!logContent || logContent.trim() === '') {
          return;
        }

        processedLogs.push({
          id: `log-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
          type: 'warning',
          timestamp: this.extractTimestamp(log),
          content: logContent,
          rawLog: log,
        });
        return;
      }

      // Check for progress description logs - only show if there's a state change
      if (log.includes('Progress Description')) {

        if (!logContent || logContent.trim() === '') {
          return;
        }

        const progressDesc = logContent.replace('Progress Description Updated: ', '').trim();

        if (progressDesc !== this.lastProgressState) {
          this.lastProgressState = progressDesc;

          processedLogs.push({
            id: `log-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
            type: 'progress-description',
            timestamp: this.extractTimestamp(log),
            content: logContent,
            rawLog: log,
          });
        }
        return;
      }

      if (log.includes('Status changed to:')) {

        if (!logContent || logContent.trim() === '') {
          return;
        }

        processedLogs.push({
          id: `log-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
          type: 'status-change',
          timestamp: this.extractTimestamp(log),
          content: logContent,
          rawLog: log,
        });
        return;
      }

      if (log.includes('DEBUG')) {

        if (!logContent || logContent.trim() === '') {
          return;
        }

        if (logContent.includes('Raw response:')) {
          return;
        }

        processedLogs.push({
          id: `log-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
          type: 'debug',
          timestamp: this.extractTimestamp(log),
          content: logContent,
          rawLog: log,
        });
        return;
      }

      if (!logContent || logContent.trim() === '' || processedJsonLogs.has(log)) {
        return;
      }

      if ((log.includes('{') && log.includes('}')) || (log.includes('[') && log.includes(']'))) {

        const jsonStartIndex = log.indexOf('{');
        const jsonEndIndex = log.lastIndexOf('}') + 1;

        if (jsonStartIndex !== -1 && jsonEndIndex > jsonStartIndex) {
          try {

            const jsonPart = log.substring(jsonStartIndex, jsonEndIndex);
            JSON.parse(jsonPart);

            if (
              jsonPart.includes('"data"') ||
              jsonPart.includes('"filesToGenerate"') ||
              jsonPart.includes('"fileName"') ||
              jsonPart.includes('"content"')
            ) {
              return;
            }
          } catch (e) {

          }
        }
      }

      processedLogs.push({
        id: `log-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        type: 'info',
        timestamp: this.extractTimestamp(log),
        content: logContent,
        rawLog: log,
      });
    });



    return processedLogs;
  }

  private extractTimestamp(log: string): string {

    const parts = log.split(' - ');
    if (parts.length >= 1) {
      const timestamp = parts[0];

      if (timestamp.match(/^\d{2}:\d{2}:\d{2}/)) {
        const now = new Date();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        return `${month}/${day} ${timestamp}`;
      }
    }

    const now = new Date();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const time = now.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
    return `${month}/${day} ${time}`;
  }

  private extractLogContent(log: string): string {

    const parts = log.split(' - ');
    if (parts.length >= 3) {
      return parts.slice(2).join(' - ');
    }
    return log;
  }

  private getLanguageFromPath(path: string): string {
    const extension = path.split('.').pop()?.toLowerCase();

    switch (extension) {
      case 'ts':
        return 'typescript';
      case 'js':
        return 'javascript';
      case 'html':
        return 'html';
      case 'css':
        return 'css';
      case 'scss':
        return 'scss';
      case 'json':
        return 'json';
      case 'md':
        return 'markdown';
      case 'py':
        return 'python';
      case 'java':
        return 'java';
      case 'xml':
        return 'xml';
      case 'yaml':
      case 'yml':
        return 'yaml';
      default:
        return 'plaintext';
    }
  }







  /**
   * Scrolls the logs container to the bottom to show the latest logs
   */
  private scrollLogsToBottom(): void {
    setTimeout(() => {
      const logsContainer = document.querySelector('.logs-content');
      if (logsContainer) {
        logsContainer.scrollTop = logsContainer.scrollHeight;
      }
    }, 0);
  }

  /**
   * Adds a log update message to the chat window
   * This method is kept for backward compatibility but is no longer used
   * @param logs Array of log messages
   */
  private addLogUpdateToChatWindow(_logs: string[]): void {
    // ENHANCED: Enable artifacts tab when logs are available
    this.isLogsTabEnabled = true;

    // Enable artifacts tab with logs functionality
    this.enableArtifactsTabIfNeeded();

    // ENHANCED: Auto-switch to artifacts tab and select logs file when logs become available
    // Only auto-switch if user hasn't manually selected a tab and artifacts tab is enabled
    if (this.hasLogs && !this.userSelectedTab && this.isArtifactsTabEnabledWithLogs) {
      setTimeout(() => {

        this.toggleArtifactsView();

        this.userSelectedTab = false;
      }, 500);
    }

  }

  private formatLayoutAnalyzedDescription(description: string): string {
    if (!description.includes('LAYOUT_ANALYZED')) {
      return description;
    }

    const layoutMatch = description.match(/LAYOUT_ANALYZED\s+(.*?)(?:\n|$)/);

    if (layoutMatch && layoutMatch[1]) {
      const fileList = layoutMatch[1].trim();

      let files: string[] = [];

      if (fileList.includes(',')) {
        files = fileList.split(',').map(file => file.trim());
      }

      else if (fileList.includes(' ')) {
        files = fileList.split(/\s+/).filter(file => file.trim() !== '');
      }

      else {
        files = [fileList];
      }

      files = files.filter(file => file.trim() !== '');

      const formattedFileList = files
        .map(file => {

          let icon = '📄';
          if (file.endsWith('.ts') || file.endsWith('.tsx')) icon = '📘';
          else if (file.endsWith('.js') || file.endsWith('.jsx')) icon = '📙';
          else if (file.endsWith('.css')) icon = '🎨';
          else if (file.endsWith('.html')) icon = '🌐';
          else if (file.endsWith('.json')) icon = '📋';

          return `- ${icon} ${file}`;
        })
        .join('\n');

      return description.replace(
        /LAYOUT_ANALYZED\s+(.*?)(?:\n|$)/,
        `### Project Structure Analysis\n\nThe following files will be generated:\n\n${formattedFileList}\n\n`
      );
    }

    return description;
  }

  formatCodeForDisplay(code: string): string {
    if (!code) return '';

    return code
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/ /g, '&nbsp;')
      .replace(/\n/g, '<br>');
  }

  toggleCodeExpansion(log: any): void {

    if (!log.id) {
      log.id = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    }

    const isCurrentlyTyping =
      this.isTypingLog &&
      this.currentLogIndex < this.formattedLogMessages.length &&
      this.formattedLogMessages[this.currentLogIndex].id === log.id;

    if (isCurrentlyTyping && this.expandedCodeLogs.has(log.id)) {
      return;
    }

    const hasFinishedTyping =
      log.visibleContent && log.content && log.visibleContent.length === log.content.length;

    if (this.expandedCodeLogs.has(log.id)) {

      if (hasFinishedTyping || !isCurrentlyTyping) {
        this.expandedCodeLogs.delete(log.id);
      } else {
      }
    } else {

      this.expandedCodeLogs.add(log.id);
    }
  }

  isCodeExpanded(id: string): boolean {

    return id ? this.expandedCodeLogs.has(id) : true;
  }

  getLayoutForPageIndex(index: number): string {
    if (!this.layoutData || this.layoutData.length === 0) {
      return '';
    }

    if (this.layoutData.length === 1) {
      return this.layoutData[0];
    }

    return this.layoutData[index % this.layoutData.length];
  }

  extractErrorMessageFromProgressDescription(): void {

    const statusResponse = this.pollingService.getLastStatusResponse();

    if (
      statusResponse &&
      statusResponse.details &&
      statusResponse.details.progress_description &&
      statusResponse.details.progress_description.trim() !== ''
    ) {
      // ENHANCED: Try to extract error message from log field first
      const extractedErrorMessage = this.extractErrorMessageFromSSEData(statusResponse.details);
      const errorMessage = extractedErrorMessage || statusResponse.details.progress_description;

      this.errorDescription$.next(errorMessage);
      this.errorTerminalOutput$.next(this.formatErrorOutput(statusResponse.details));
      return;
    }

    const progressDescription = this.pollingService.getLastProgressDescription();
    if (progressDescription && progressDescription.trim() !== '') {

      try {

        if (progressDescription.includes('{') && progressDescription.includes('}')) {
          const jsonStartIndex = progressDescription.indexOf('{');
          const jsonEndIndex = progressDescription.lastIndexOf('}') + 1;
          const jsonPart = progressDescription.substring(jsonStartIndex, jsonEndIndex);
          const parsedData = JSON.parse(jsonPart);

          // ENHANCED: Try to extract error message from log field first
          const extractedErrorMessage = this.extractErrorMessageFromSSEData(parsedData);
          if (extractedErrorMessage) {
            this.errorDescription$.next(extractedErrorMessage);
            this.errorTerminalOutput$.next(this.formatErrorOutput(parsedData));
            return;
          }

          if (parsedData.message) {
            this.errorDescription$.next(parsedData.message);
          } else if (parsedData.progress_description) {
            this.errorDescription$.next(parsedData.progress_description);
          } else if (parsedData.details && parsedData.details.message) {
            this.errorDescription$.next(parsedData.details.message);
          } else if (parsedData.details && parsedData.details.progress_description) {
            this.errorDescription$.next(parsedData.details.progress_description);
          } else if (parsedData.error) {
            this.errorDescription$.next(
              typeof parsedData.error === 'string'
                ? parsedData.error
                : JSON.stringify(parsedData.error)
            );
          }

          this.errorTerminalOutput$.next(this.formatErrorOutput(parsedData));
        } else {

          this.errorDescription$.next(progressDescription);
          this.errorTerminalOutput$.next(progressDescription);
        }
      } catch (e) {
        this.errorDescription$.next(progressDescription);
        this.errorTerminalOutput$.next(progressDescription);
      }
    }
  }

  formatErrorOutput(errorData: any): string {
    try {

      const formattedError: any = {
        error_type: errorData.status || 'ERROR',
        timestamp: new Date().toISOString(),
        details: {},
      };

      if (errorData.message) {
        formattedError.message = errorData.message;
      } else if (errorData.progress_description) {
        formattedError.message = errorData.progress_description;
      }

      if (errorData.details) {
        formattedError.details = errorData.details;
      }

      if (errorData.log) {
        try {

          if (
            typeof errorData.log === 'string' &&
            (errorData.log.includes('{') || errorData.log.includes('['))
          ) {
            formattedError.log = JSON.parse(errorData.log);
          } else {
            formattedError.log = errorData.log;
          }
        } catch (e) {
          formattedError.log = errorData.log;
        }
      }

      if (errorData.stack) {
        formattedError.stack_trace = errorData.stack;
      }

      return JSON.stringify(formattedError, null, 2);
    } catch (e) {

      return JSON.stringify(errorData, null, 2);
    }
  }

  trackByLogIndex(index: number, item: any): string {
    return item.id || index.toString();
  }

  trackByLayoutId(index: number, layout: any): string {
    return layout || index.toString();
  }

  shouldShowAnalyzingLayout(): boolean {

    if (this.layoutAnalyzedData && this.layoutAnalyzedData.length > 0 && this.hasLayoutBeenDetected) {
      return false;
    }

    return this.isAnalyzingLayout || !this.hasLayoutBeenDetected ||
           !this.layoutAnalyzedData || this.layoutAnalyzedData.length === 0;
  }

  getDefaultLayoutKeyForAnalyzing(): string {

    if (this.detectedLayoutFromPrevMetadata) {
      return this.detectedLayoutFromPrevMetadata;
    }

    if (this.layoutData && this.layoutData.length > 0) {
      return this.layoutData[0];
    }

    if (this.layoutAnalyzedData && this.layoutAnalyzedData.length > 0) {
      return this.layoutAnalyzedData[0].key;
    }

    return 'HB';
  }

  private startLayoutAnalyzing(): void {
    this.isAnalyzingLayout = true;
    this.hasLayoutBeenDetected = false;
  }

  private stopLayoutAnalyzing(): void {
    this.isAnalyzingLayout = false;
    this.hasLayoutBeenDetected = true;
  }

  private handleLayoutAnalysisFailed(): void {
    this.isAnalyzingLayout = false;
    this.hasLayoutBeenDetected = false;
  }



  trackByPageIndex(index: number): number {
    return index;
  }

  trackByArtifactId(index: number, artifact: any): string {
    return artifact.id || artifact.name || index.toString();
  }

  shouldShowStepper(): boolean {

    if (this.isPolling) {
      return true;
    }

    const isRegenerationActive = this.isRegenerationInProgress$.value;
    const hasStepperMessage = this.lightMessages.some(msg => msg.hasSteps);

    if (isRegenerationActive && hasStepperMessage) {
      return true;
    }

    if (this.isCodeGenerationComplete && hasStepperMessage) {
      return true;
    }

    return false;
  }
}
