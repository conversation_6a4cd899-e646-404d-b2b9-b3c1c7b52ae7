[2025-07-08T14:32:06.246Z] [INFO] [SSEDataProcessorService] 🎯 Detected FIRST-CUT GENERATION event - stepper and logs will display this data
logger.ts:150 [2025-07-08T14:32:06.246Z] [INFO] [SSEDataProcessorService] 📨 Processing SSE event with checkpoint validation: {type: 'initial-code-gen', id: '1751985126124-0', sessionKey: 'b6bac82f-4398-4660-a36c-319d41854a9f-a4b81a3b-e14f-40d0-b093-02184212101a', generationType: 'initial-code-gen', hasEventId: true, …}
logger.ts:150 [2025-07-08T14:32:06.246Z] [INFO] [SSEDataProcessorService] 🔍 Parsed SSE event data: {status: 'IN_PROGRESS', progress: 'SEED_PROJECT_INITIALIZED', hasLog: true, hasProgressDescription: true, metadataCount: 0, …}
logger.ts:150 [2025-07-08T14:32:06.246Z] [INFO] [VerticalStepperComponent] 🔒 Preserving frozen description for completed step: {stepIndex: 0, stepTitle: 'Project Overview', isCompleted: true, isFrozen: true, progressState: 'OVERVIEW', …}
logger.ts:150 [2025-07-08T14:32:06.246Z] [INFO] [VerticalStepperComponent] 🔒 Preserving frozen description for completed step: {stepIndex: 0, stepTitle: 'Project Overview', isCompleted: false, isFrozen: true, progressState: 'OVERVIEW', …}
logger.ts:150 [2025-07-08T14:32:06.246Z] [INFO] [VerticalStepperComponent] 🔒 Preserving frozen description for completed step: {stepIndex: 0, stepTitle: 'Project Overview', isCompleted: false, isFrozen: true, progressState: 'OVERVIEW', …}
logger.ts:150 [2025-07-08T14:32:06.247Z] [INFO] [NewPollingResponseProcessor] State change detected: Progress: SEED_PROJECT_INITIALIZED -> SEED_PROJECT_INITIALIZED, Status: IN_PROGRESS -> IN_PROGRESS
code-window.component.ts:6705 🔒 Code tab permanently enabled after SEED_PROJECT_INITIALIZED + COMPLETED
logger.ts:150 [2025-07-08T14:32:06.249Z] [INFO] [ChatWindowComponent] 📊 Initial generation progress update: SEED_PROJECT_INITIALIZED
logger.ts:150 [2025-07-08T14:32:06.249Z] [INFO] [ChatWindowComponent] 📊 Handling initial generation progress: SEED_PROJECT_INITIALIZED
logger.ts:150 [2025-07-08T14:32:06.249Z] [INFO] [ChatWindowComponent] 📈 Initial generation status update: IN_PROGRESS
logger.ts:150 [2025-07-08T14:32:06.250Z] [INFO] [ChatWindowComponent] 📈 Handling initial generation status: IN_PROGRESS
logger.ts:150 [2025-07-08T14:32:06.251Z] [INFO] [NewPollingResponseProcessor] ✅ Direct log content updated with deduplication and filtering
logger.ts:150 [2025-07-08T14:32:06.251Z] [INFO] [NewPollingResponseProcessor] Processing enhanced metadata for progress: SEED_PROJECT_INITIALIZED, status: IN_PROGRESS
logger.ts:150 [2025-07-08T14:32:06.252Z] [INFO] [NewPollingResponseProcessor] 🎯 Processing new workflow response with generation type: {generationType: 'initial-code-gen', progress: 'SEED_PROJECT_INITIALIZED', status: 'IN_PROGRESS', metadataCount: 0, metadataTypes: Array(0)}
logger.ts:150 [2025-07-08T14:32:06.252Z] [INFO] [ChatWindowComponent] 📊 Initial generation progress update: SEED_PROJECT_INITIALIZED
logger.ts:150 [2025-07-08T14:32:06.252Z] [INFO] [ChatWindowComponent] 📊 Handling initial generation progress: SEED_PROJECT_INITIALIZED
logger.ts:150 [2025-07-08T14:32:06.252Z] [INFO] [ChatWindowComponent] 📈 Initial generation status update: IN_PROGRESS
logger.ts:150 [2025-07-08T14:32:06.252Z] [INFO] [ChatWindowComponent] 📈 Handling initial generation status: IN_PROGRESS
logger.ts:150 [2025-07-08T14:32:06.252Z] [INFO] [NewPollingResponseProcessor] Processing new workflow metadata for progress: SEED_PROJECT_INITIALIZED, status: IN_PROGRESS
logger.ts:150 [2025-07-08T14:32:06.252Z] [INFO] [NewPollingResponseProcessor] Processing metadata by new workflow for SEED_PROJECT_INITIALIZED - IN_PROGRESS: []
logger.ts:150 [2025-07-08T14:32:06.253Z] [INFO] [NewPollingResponseProcessor] New workflow response processing completed
logger.ts:150 [2025-07-08T14:32:06.253Z] [INFO] [SSEEventCacheService] ✅ Event marked as processed: {eventId: '1751985126124-0', sessionKey: 'b6bac82f-4398-4660-a36c-319d41854a9f-a4b81a3b-e14f-40d0-b093-02184212101a', eventType: 'initial-code-gen', timestamp: '2025-07-08T14:32:06.246Z'}
logger.ts:150 [2025-07-08T14:32:06.253Z] [INFO] [SSEDataProcessorService] ✅ SSE event processed successfully with checkpoint tracking: {eventId: '1751985126124-0', sessionKey: 'b6bac82f-4398-4660-a36c-319d41854a9f-a4b81a3b-e14f-40d0-b093-02184212101a', finalStatus: 'IN_PROGRESS', finalProgress: 'SEED_PROJECT_INITIALIZED', hasCodeFiles: false, …}
logger.ts:150 [2025-07-08T14:32:08.611Z] [INFO] [TemplateLoading] ✅ Template ZIP downloaded directly {size: 257452}
logger.ts:150 [2025-07-08T14:32:08.611Z] [INFO] [TemplateLoading] 📦 Processing ZIP file {size: 257452}
logger.ts:150 [2025-07-08T14:32:08.854Z] [INFO] [TemplateLoading] ✅ ZIP processing completed {totalFiles: 78, fileTypes: {…}, fileDetails: Array(78)}
logger.ts:150 [2025-07-08T14:32:08.855Z] [INFO] [TemplateLoading] ✅ Template loading completed successfully {fileCount: 78, files: Array(78)}
code-window.component.ts:2013 🏗️ Template files received from SSE-triggered loading: {fileCount: 78, fileNames: Array(78), totalSize: 661153}
code-window.component.ts:2038 🔄 Loading template files into Monaco Editor: {files: Array(78)}
logger.ts:150 [2025-07-08T14:32:08.855Z] [INFO] [FileTreePersistenceService] 🏗️ Initializing baseline file tree
logger.ts:150 [2025-07-08T14:32:08.879Z] [INFO] [FileTreePersistenceService] 💾 File tree state persisted to sessionStorage
logger.ts:150 [2025-07-08T14:32:08.879Z] [INFO] [FileTreePersistenceService] ✅ Baseline initialized with 78 files (version 1)
code-window.component.ts:2055 ✅ Template files successfully loaded into Monaco Editor: {validFiles: 78, filteredFiles: 0, fileNames: Array(78)}fileNames: (78) ['tsconfig.node.json', 'index.html', 'tsconfig.app.json', 'bun.lockb', 'README.md', 'tailwind.config.ts', 'azure-pipelines.yaml', 'package-lock.json', 'package.json', 'staticwebapp.config.json', 'components.json', 'tsconfig.json', 'eslint.config.js', 'vite.config.ts', 'postcss.config.js', 'public/favicon.ico', 'public/netlify.toml', 'public/robots.txt', 'public/placeholder.svg', 'src/App.tsx', 'src/main.tsx', 'src/App.css', 'src/index.css', 'src/vite-env.d.ts', 'src/components/ui/aspect-ratio.tsx', 'src/components/ui/alert-dialog.tsx', 'src/components/ui/pagination.tsx', 'src/components/ui/tabs.tsx', 'src/components/ui/card.tsx', 'src/components/ui/slider.tsx', 'src/components/ui/popover.tsx', 'src/components/ui/progress.tsx', 'src/components/ui/toaster.tsx', 'src/components/ui/input-otp.tsx', 'src/components/ui/chart.tsx', 'src/components/ui/hover-card.tsx', 'src/components/ui/sheet.tsx', 'src/components/ui/scroll-area.tsx', 'src/components/ui/resizable.tsx', 'src/components/ui/label.tsx', 'src/components/ui/sonner.tsx', 'src/components/ui/navigation-menu.tsx', 'src/components/ui/accordion.tsx', 'src/components/ui/drawer.tsx', 'src/components/ui/tooltip.tsx', 'src/components/ui/alert.tsx', 'src/components/ui/use-toast.ts', 'src/components/ui/switch.tsx', 'src/components/ui/calendar.tsx', 'src/components/ui/breadcrumb.tsx', 'src/components/ui/radio-group.tsx', 'src/components/ui/command.tsx', 'src/components/ui/toggle-group.tsx', 'src/components/ui/avatar.tsx', 'src/components/ui/menubar.tsx', 'src/components/ui/dialog.tsx', 'src/components/ui/badge.tsx', 'src/components/ui/sidebar.tsx', 'src/components/ui/table.tsx', 'src/components/ui/separator.tsx', 'src/components/ui/button.tsx', 'src/components/ui/toggle.tsx', 'src/components/ui/toast.tsx', 'src/components/ui/checkbox.tsx', 'src/components/ui/collapsible.tsx', 'src/components/ui/dropdown-menu.tsx', 'src/components/ui/select.tsx', 'src/components/ui/textarea.tsx', 'src/components/ui/input.tsx', 'src/components/ui/skeleton.tsx', 'src/components/ui/context-menu.tsx', 'src/components/ui/form.tsx', 'src/components/ui/carousel.tsx', 'src/hooks/use-mobile.tsx', 'src/hooks/use-toast.ts', 'src/lib/utils.ts', 'src/pages/Index.tsx', 'src/pages/NotFound.tsx']filteredFiles: 0validFiles: 78[[Prototype]]: Objectconstructor: ƒ Object()hasOwnProperty: ƒ hasOwnProperty()isPrototypeOf: ƒ isPrototypeOf()propertyIsEnumerable: ƒ propertyIsEnumerable()toLocaleString: ƒ toLocaleString()toString: ƒ ()valueOf: ƒ valueOf()__defineGetter__: ƒ __defineGetter__()__defineSetter__: ƒ __defineSetter__()__lookupGetter__: ƒ __lookupGetter__()__lookupSetter__: ƒ __lookupSetter__()__proto__: (...)get __proto__: ƒ __proto__()set __proto__: ƒ __proto__()
logger.ts:150 [2025-07-08T14:32:08.880Z] [INFO] [SSEDataProcessorService] ✅ SSE-triggered template loading completed successfully {fileCount: 78, framework: 'react', designLibrary: 'tailwindcss', files: Array(78), trigger: 'OVERVIEW + COMPLETED'}
logger.ts:150 [2025-07-08T14:32:08.881Z] [INFO] [GenerationState] 🏗️ Updated template loading state {isLoading: false}
code-window.component.ts:2064 🔍 Monaco Editor state verification: {filesInSubject: 78, fileNames: Array(78), codeViewerReady: false}